#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشغيل الاختبارات السريعة
Quick Test Runner for Trading Signals System
"""

import subprocess
import sys
import time
import requests
import json
import os
from datetime import datetime

def print_header():
    """طباعة الرأس"""
    print("🚀 أداة تشغيل اختبارات نظام إشارات التداول السريعة")
    print("=" * 60)
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_server_status(url="http://localhost:9000"):
    """فحص حالة السيرفر"""
    print("🔍 فحص حالة السيرفر...")
    try:
        response = requests.get(f"{url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ السيرفر يعمل بشكل طبيعي")
            return True
        else:
            print(f"⚠️ السيرفر يستجيب بكود: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ السيرفر غير متاح - تأكد من تشغيله على البورت 9000")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def run_quick_test():
    """إجراء اختبار سريع"""
    print("\n⚡ اختبار سريع للوظائف الأساسية...")
    
    # اختبار webhook أساسي
    try:
        test_signal = {
            "stock_code": "QUICK_TEST",
            "report": "buy",
            "buy_price": "100.00",
            "tp1": "105.00",
            "tp2": "110.00",
            "tp3": "115.00",
            "sl": "95.00"
        }
        
        headers = {'Content-Type': 'application/json'}
        response = requests.post(
            "http://localhost:9000/webhook?jsonRequest=true",
            data=json.dumps(test_signal),
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Webhook يعمل بشكل صحيح")
            return True
        else:
            print(f"⚠️ Webhook يستجيب بكود: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Webhook: {e}")
        return False

def run_basic_tests():
    """تشغيل الاختبارات الأساسية"""
    print("\n🧪 تشغيل الاختبارات الأساسية...")
    try:
        result = subprocess.run([sys.executable, "SYSTEM_TESTING_SUITE.py"], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ الاختبارات الأساسية اكتملت بنجاح")
            return True
        else:
            print(f"⚠️ الاختبارات الأساسية اكتملت مع أخطاء")
            print(f"الخرج: {result.stdout}")
            print(f"الأخطاء: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ انتهت مهلة الاختبارات الأساسية")
        return False
    except FileNotFoundError:
        print("❌ ملف الاختبارات الأساسية غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات الأساسية: {e}")
        return False

def run_advanced_tests():
    """تشغيل الاختبارات المتقدمة"""
    print("\n🔬 تشغيل الاختبارات المتقدمة...")
    try:
        result = subprocess.run([sys.executable, "ADVANCED_SYSTEM_TESTS.py"], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ الاختبارات المتقدمة اكتملت بنجاح")
            return True
        else:
            print(f"⚠️ الاختبارات المتقدمة اكتملت مع أخطاء")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ انتهت مهلة الاختبارات المتقدمة")
        return False
    except FileNotFoundError:
        print("❌ ملف الاختبارات المتقدمة غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات المتقدمة: {e}")
        return False

def check_test_results():
    """فحص نتائج الاختبارات"""
    print("\n📊 فحص نتائج الاختبارات...")
    
    results_files = [
        ("testing_results.json", "الاختبارات الأساسية"),
        ("advanced_test_results.json", "الاختبارات المتقدمة")
    ]
    
    overall_summary = {}
    
    for filename, description in results_files:
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if filename == "testing_results.json":
                    summary = data.get("summary", {})
                    total = summary.get("total_tests", 0)
                    passed = summary.get("passed", 0)
                    failed = summary.get("failed", 0)
                    
                    success_rate = (passed / total * 100) if total > 0 else 0
                    print(f"  📋 {description}:")
                    print(f"    🔢 إجمالي: {total}")
                    print(f"    ✅ نجح: {passed}")
                    print(f"    ❌ فشل: {failed}")
                    print(f"    📈 معدل النجاح: {success_rate:.1f}%")
                    
                    overall_summary[description] = {
                        "total": total,
                        "passed": passed,
                        "failed": failed,
                        "success_rate": success_rate
                    }
                
                elif filename == "advanced_test_results.json":
                    results = data.get("results", {})
                    total = len(results)
                    passed = len([r for r in results.values() if r == "نجح"])
                    failed = total - passed
                    
                    success_rate = (passed / total * 100) if total > 0 else 0
                    print(f"  📋 {description}:")
                    print(f"    🔢 إجمالي: {total}")
                    print(f"    ✅ نجح: {passed}")
                    print(f"    ❌ فشل: {failed}")
                    print(f"    📈 معدل النجاح: {success_rate:.1f}%")
                    
                    overall_summary[description] = {
                        "total": total,
                        "passed": passed,
                        "failed": failed,
                        "success_rate": success_rate
                    }
                
            except Exception as e:
                print(f"  ❌ خطأ في قراءة {filename}: {e}")
        else:
            print(f"  ⚠️ ملف النتائج {filename} غير موجود")
    
    return overall_summary

def generate_quick_report(summary):
    """إنشاء تقرير سريع"""
    print("\n📄 التقرير السريع:")
    print("=" * 40)
    
    total_tests = sum([s["total"] for s in summary.values()])
    total_passed = sum([s["passed"] for s in summary.values()])
    total_failed = sum([s["failed"] for s in summary.values()])
    
    overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"🔢 إجمالي الاختبارات: {total_tests}")
    print(f"✅ اختبارات ناجحة: {total_passed}")
    print(f"❌ اختبارات فاشلة: {total_failed}")
    print(f"📈 معدل النجاح الإجمالي: {overall_success_rate:.1f}%")
    
    # تقييم الحالة
    if overall_success_rate >= 90:
        status = "ممتاز ✨"
        recommendation = "النظام يعمل بشكل ممتاز"
    elif overall_success_rate >= 75:
        status = "جيد ✅"
        recommendation = "النظام يعمل بشكل جيد"
    elif overall_success_rate >= 50:
        status = "مقبول ⚠️"
        recommendation = "النظام يحتاج تحسينات"
    else:
        status = "ضعيف ❌"
        recommendation = "النظام يحتاج إصلاحات فورية"
    
    print(f"🎯 حالة النظام: {status}")
    print(f"💡 التوصية: {recommendation}")
    
    # حفظ التقرير السريع
    quick_report = {
        "timestamp": datetime.now().isoformat(),
        "summary": summary,
        "overall": {
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "success_rate": overall_success_rate,
            "status": status,
            "recommendation": recommendation
        }
    }
    
    with open('quick_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(quick_report, f, ensure_ascii=False, indent=2)
    
    print("\n📁 تم حفظ التقرير السريع في: quick_test_report.json")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص حالة السيرفر
    if not check_server_status():
        print("\n🛑 لا يمكن المتابعة بدون تشغيل السيرفر")
        print("💡 تأكد من تشغيل السيرفر بالأمر: python server.py")
        return
    
    # اختبار سريع
    if not run_quick_test():
        print("\n⚠️ الاختبار السريع فشل - قد تكون هناك مشاكل أساسية")
    
    # خيارات التشغيل
    print("\n🎯 اختر نوع الاختبار:")
    print("1. اختبارات أساسية فقط (سريع - 2-3 دقائق)")
    print("2. اختبارات أساسية + متقدمة (شامل - 5-10 دقائق)")
    print("3. فحص النتائج الموجودة فقط")
    print("4. خروج")
    
    try:
        choice = input("\nاختر رقم (1-4): ").strip()
        
        if choice == "1":
            print("\n🚀 بدء الاختبارات الأساسية...")
            run_basic_tests()
            time.sleep(2)
            summary = check_test_results()
            generate_quick_report(summary)
            
        elif choice == "2":
            print("\n🚀 بدء الاختبارات الشاملة...")
            run_basic_tests()
            time.sleep(2)
            run_advanced_tests()
            time.sleep(2)
            summary = check_test_results()
            generate_quick_report(summary)
            
        elif choice == "3":
            summary = check_test_results()
            if summary:
                generate_quick_report(summary)
            else:
                print("❌ لا توجد نتائج اختبارات سابقة")
                
        elif choice == "4":
            print("👋 إلى اللقاء!")
            return
            
        else:
            print("❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    print("\n✅ انتهى تشغيل أداة الاختبارات السريعة")
    print("📁 راجع الملفات التالية للتفاصيل:")
    print("  - testing_results.json (نتائج أساسية)")
    print("  - advanced_test_results.json (نتائج متقدمة)")
    print("  - quick_test_report.json (تقرير سريع)")

if __name__ == "__main__":
    main()
