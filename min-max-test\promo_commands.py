#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دوال أوامر البرومو كود للبوت
Promo code command functions for the bot
"""

import logging
import datetime

# إعداد التسجيل
logger = logging.getLogger(__name__)

# استيراد نظام أكواد الخصم
try:
    from promo_codes import PromoCodeManager
    from user_limit import UserManager
    PROMO_CODES_AVAILABLE = True
    logger.info("Promo codes system loaded successfully")
except ImportError as e:
    PROMO_CODES_AVAILABLE = False
    logger.warning(f"Promo codes system not available: {e}")
except Exception as e:
    PROMO_CODES_AVAILABLE = False
    logger.error(f"Error importing promo codes system: {e}")

# قائمة الإدارة
try:
    from config import ADMIN_IDS
    logger.info(f"Admin IDs loaded: {len(ADMIN_IDS)} admins")
except ImportError:
    ADMIN_IDS = []
    logger.warning("No config.py found - all users will have admin access for testing")

# ===== أوامر نظام أكواد الخصم =====

async def create_trial_code_command(message):
    """إنشاء كود تجربة مجانية - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        args = message.text.split()
        days = int(args[1]) if len(args) > 1 else 7
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود تجربة مجانية"
        
        code = PromoCodeManager.create_trial_code(days, expiry_days, note)
        
        if code:
            await message.reply(f"""
✅ **تم إنشاء كود التجربة المجانية بنجاح!**

🎫 **الكود:** `{code}`
📅 **مدة التجربة:** {days} أيام
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`
""", parse_mode="Markdown")
        else:
            await message.reply("❌ حدث خطأ في إنشاء الكود")
    except ValueError:
        await message.reply("""
❌ **خطأ في التنسيق**

📋 **طريقة الاستخدام:**
`/create_trial_code [أيام] [انتهاء] [ملاحظة]`

**مثال:**
`/create_trial_code 7 30 كود خاص للأعضاء الجدد`
""", parse_mode="Markdown")
    except Exception as e:
        logger.error(f"Error in create_trial_code_command: {e}")
        await message.reply(f"❌ خطأ: {str(e)}")

async def create_discount_code_command(message):
    """إنشاء كود خصم - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        args = message.text.split()
        discount_percent = int(args[1]) if len(args) > 1 else 20
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود خصم خاص"
        
        code = PromoCodeManager.create_discount_code(discount_percent, expiry_days, note)
        
        if code:
            await message.reply(f"""
✅ **تم إنشاء كود الخصم بنجاح!**

🎫 **الكود:** `{code}`
💰 **نسبة الخصم:** {discount_percent}%
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`
""", parse_mode="Markdown")
        else:
            await message.reply("❌ حدث خطأ في إنشاء الكود")
    except ValueError:
        await message.reply("""
❌ **خطأ في التنسيق**

📋 **طريقة الاستخدام:**
`/create_discount_code [نسبة] [انتهاء] [ملاحظة]`

**مثال:**
`/create_discount_code 50 15 عرض نهاية الأسبوع`
""", parse_mode="Markdown")
    except Exception as e:
        logger.error(f"Error in create_discount_code_command: {e}")
        await message.reply(f"❌ خطأ: {str(e)}")

async def redeem_promo_code(message):
    """استخدام كود البرومو - محدث لتحديث بيانات المستخدم"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return

    args = message.text.split()
    if len(args) != 2:
        await message.reply("""
🎫 **كيفية استخدام الكود:**

📋 **الاستخدام:** `/redeem YOUR_CODE`

**مثال:** `/redeem TRIAL123ABC`
""", parse_mode="Markdown")
        return

    code = args[1].upper()
    user_id = str(message.from_user.id)
    user_name = f"{message.from_user.first_name or ''} {message.from_user.last_name or ''}".strip()

    try:
        is_valid, error_msg, code_data = PromoCodeManager.validate_promo_code(code, user_id)

        if not is_valid:
            await message.reply(f"❌ {error_msg}")
            return

        # تطبيق الكود حسب نوعه
        if code_data['type'] == 'trail':
            trail_days = int(code_data['value'])

            # تحديث بيانات المستخدم في الجدول
            try:
                from user_subscription_manager import UserSubscriptionManager
                import datetime

                # التحقق من وجود المستخدم وإضافته إذا لم يكن موجوداً
                user_info = UserSubscriptionManager.get_user_subscription_info(user_id)
                if not user_info:
                    UserSubscriptionManager.add_new_user_to_sheet(
                        user_id,
                        message.from_user.first_name or "",
                        message.from_user.last_name or ""
                    )

                # تفعيل التجربة المجانية
                success = UserSubscriptionManager.activate_trial_subscription(user_id, trail_days)

                if success:
                    # تحديث حالة الكود
                    PromoCodeManager.use_promo_code(code_data, user_id)

                    # حساب تاريخ الانتهاء
                    end_date = (datetime.date.today() + datetime.timedelta(days=trail_days)).strftime("%Y-%m-%d")

                    await message.reply(f"""
🎉 **تم تفعيل التجربة المجانية بنجاح!**

✨ **مفعل لك الآن لمدة {trail_days} أيام:**
├ 🔄 تحليلات غير محدودة
├ 📱 تنبيهات فورية للفرص الذهبية
├ 🎯 توصيات خاصة عالية الدقة
├ 📈 تحليل تقني متقدم بالذكاء الاصطناعي
├ 💎 مستويات دعم ومقاومة مخفية
└ 👨‍💼 دعم شخصي من المحللين

📅 **ينتهي في:** {end_date}

🚀 **ابدأ الاستفادة الآن:**
├ 📊 /analyze + كود السهم
├ 🎯 /stock + كود السهم
└ 📈 /chart + كود السهم

💡 **نصيحة:** استفد من فترة التجربة لتقييم الأداء!
""", parse_mode="Markdown")

                    logger.info(f"تم تفعيل التجربة المجانية للمستخدم {user_id} ({user_name}) لمدة {trail_days} أيام باستخدام الكود {code}")
                else:
                    await message.reply("❌ فشل في تفعيل التجربة المجانية. يرجى المحاولة مرة أخرى.")

            except ImportError:
                # إذا لم يكن UserSubscriptionManager متاحاً، استخدم الطريقة القديمة
                PromoCodeManager.use_promo_code(code_data, user_id)
                await message.reply(f"""
🎉 **تم تفعيل التجربة المجانية!**

⏰ **المدة:** {trail_days} أيام
🎫 **الكود:** {code}
✅ **الحالة:** مفعل

💡 يمكنك الآن الاستفادة من جميع المزايا المتاحة!
""", parse_mode="Markdown")

        elif code_data['type'] == 'discount':
            PromoCodeManager.use_promo_code(code_data, user_id)
            await message.reply(f"""
💰 **تم تفعيل كود الخصم!**

🎯 **نسبة الخصم:** {code_data['value']}%
🎫 **الكود:** {code}
✅ **الحالة:** مفعل

💡 سيتم تطبيق الخصم على عمليات الشراء القادمة!
""", parse_mode="Markdown")

            logger.info(f"تم تفعيل كود خصم {code_data['value']}% للمستخدم {user_id} ({user_name}) باستخدام الكود {code}")

    except Exception as e:
        logger.error(f"Error in redeem_promo_code: {e}")
        await message.reply("❌ حدث خطأ في معالجة الكود")

async def list_promo_codes(message):
    """عرض الأكواد النشطة - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        active_codes = PromoCodeManager.list_active_codes()
        
        if active_codes is None:
            await message.reply("❌ حدث خطأ في عرض الأكواد")
            return
        
        if not active_codes:
            await message.reply("📋 لا توجد أكواد نشطة حالياً")
            return
        
        message_text = f"📋 **الأكواد النشطة ({len(active_codes)}):**\n\n"
        
        for i, code in enumerate(active_codes[-10:], 1):  # عرض آخر 10 أكواد
            code_type = "🎁 تجربة" if code['type'] == 'trail' else "💰 خصم"
            message_text += f"{i}. `{code['code']}`\n"
            message_text += f"   {code_type} - {code['value']}{'أيام' if code['type'] == 'trail' else '%'}\n"
            message_text += f"   📅 ينتهي: {code['expiry_date']}\n"
            message_text += f"   🔢 استخدم: {code['usage_count']} مرة\n\n"
        
        await message.reply(message_text, parse_mode="Markdown")
        
    except Exception as e:
        logger.error(f"Error in list_promo_codes: {e}")
        await message.reply("❌ حدث خطأ في عرض الأكواد")

async def test_promo_command(message):
    """اختبار نظام البرومو كود"""
    user_id = message.from_user.id
    username = message.from_user.username or "غير محدد"
    
    await message.reply(f"""
🧪 **تقرير حالة نظام البرومو كود**

✅ نظام تسجيل الأوامر سليم
👤 **المستخدم:** {user_id}
📝 **اسم المستخدم:** @{username}
🕐 **الوقت:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 **حالة نظام الأكواد:** {'متاح' if PROMO_CODES_AVAILABLE else 'غير متاح'}
👨‍💼 **صلاحية الإدارة:** {'نعم' if not ADMIN_IDS or user_id in ADMIN_IDS else 'لا'}

📋 **الأوامر المتاحة:**
• `/create_trial_code` - إنشاء كود تجربة
• `/create_discount_code` - إنشاء كود خصم  
• `/redeem` - استخدام كود
• `/list_codes` - عرض الأكواد

💡 **ملاحظة:** أوامر الإنشاء والعرض متاحة للإدارة فقط

🔧 **تم التشغيل من:** run.py
""", parse_mode="Markdown")

# دالة لتسجيل جميع أوامر البرومو كود
def register_promo_commands(dp):
    """تسجيل جميع أوامر البرومو كود مع الديسباتشر"""
    try:
        # أوامر البرومو كود الأساسية
        dp.register_message_handler(create_trial_code_command, commands=['create_trial_code'])
        dp.register_message_handler(create_discount_code_command, commands=['create_discount_code'])
        dp.register_message_handler(redeem_promo_code, commands=['redeem'])
        dp.register_message_handler(list_promo_codes, commands=['list_codes'])
        dp.register_message_handler(test_promo_command, commands=['test_promo'])

        # أوامر إدارة المشتركين المجانيين
        try:
            from free_users_manager import (
                list_free_users_command,
                send_promo_to_active_command,
                send_promo_to_all_command,
                send_promo_to_all_batches_command,
                confirm_send_all_batches_command
            )
            dp.register_message_handler(list_free_users_command, commands=['list_free_users'])
            dp.register_message_handler(send_promo_to_active_command, commands=['send_promo_active'])
            dp.register_message_handler(send_promo_to_all_command, commands=['send_promo_all'])
            dp.register_message_handler(send_promo_to_all_batches_command, commands=['send_promo_all_batches'])

            # تسجيل معالج الرسائل النصية للتأكيد
            from aiogram.filters import Text
            dp.register_message_handler(
                confirm_send_all_batches_command,
                lambda message: message.text and "نعم أريد الإرسال للجميع" in message.text
            )

            logger.info("Free users management commands registered successfully")
        except ImportError as e:
            logger.warning(f"Free users management not available: {e}")

        logger.info("Promo code commands registered successfully")
        return True
    except Exception as e:
        logger.error(f"Error registering promo code commands: {e}")
        return False
