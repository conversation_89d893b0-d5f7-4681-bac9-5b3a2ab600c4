# 🎫 دليل نظام أكواد الخصم والتجربة المجانية

## 📋 نظرة عامة

تم تطوير نظام شامل لأكواد الخصم والتجربة المجانية لبوت تحليلات الأسهم، يهدف إلى:
- تحفيز المستخدمين المجانيين للتحويل للاشتراك المدفوع
- توفير تجربة مجانية محدودة المدة
- إنشاء أكواد خصم مرتبطة بالأعضاء ولاستخدام واحد فقط
- تحسين رسائل التحويل والتسويق

## 🏗️ مكونات النظام

### 1. الملفات الجديدة:
- **`promo_codes.py`** - إد<PERSON>رة أكواد البرومو والخصم
- **تحديثات على `user_limit.py`** - دمج النظام مع إدارة المستخدمين  
- **تحديثات على `arabic_messages.py`** - رسائل تحفيزية جديدة

### 2. Google Sheets:
- **Worksheet جديد: `promo_codes`** - تخزين وإدارة الأكواد

## 🎯 أنواع الأكواد

### 1. أكواد التجربة المجانية (Trial Codes)
```
TRIAL + 6 أحرف عشوائية
مثال: TRIAL12AB34
```
- **الهدف**: تجربة مجانية لفترة محددة (افتراضي 7 أيام)
- **الميزات**: تحليلات غير محدودة أثناء فترة التجربة
- **الاستخدام**: مرة واحدة فقط لكل مستخدم

### 2. أكواد الخصم (Discount Codes)  
```
SAVE + نسبة الخصم + 5 أحرف عشوائية
مثال: SAVE30AB1CD, SAVE50XY9ZT
```
- **الهدف**: خصم على الاشتراك المدفوع
- **القيم**: من 10% إلى 90%
- **الاستخدام**: مرة واحدة فقط لكل مستخدم

## 📊 هيكل قاعدة البيانات (Google Sheets)

| العمود | الوصف | مثال |
|--------|--------|-------|
| كود البرومو | الكود الفريد | TRIAL12AB34 |
| نوع الكود | trial أو discount | trial |
| قيمة الخصم/الأيام | الرقم (أيام أو نسبة) | 7 |
| مستخدم بواسطة | معرفات المستخدمين | 123456789,987654321 |
| تاريخ الاستخدام | آخر استخدام | 2024-01-15 |
| تاريخ الإنشاء | تاريخ الإنشاء | 2024-01-10 |
| حالة الكود | نشط/منتهي/معطل | نشط |
| تاريخ الانتهاء | صلاحية الكود | 2024-02-10 |
| عدد مرات الاستخدام | إحصائية | 5 |
| ملاحظات | معلومات إضافية | عرض خاص |

## 🤖 أوامر البوت

### للإدارة:

#### إنشاء كود تجربة مجانية:
```
/create_trial_code [أيام] [أيام الانتهاء] [ملاحظة]

أمثلة:
/create_trial_code 7 30 كود خاص للأعضاء الجدد
/create_trial_code 3 15 عرض نهاية الأسبوع
/create_trial_code (افتراضي: 7 أيام، 30 يوم انتهاء)
```

#### إنشاء كود خصم:
```
/create_discount_code [نسبة الخصم] [أيام الانتهاء] [ملاحظة]

أمثلة:
/create_discount_code 50 15 عرض الجمعة البيضاء
/create_discount_code 30 7 خصم سريع
/create_discount_code (افتراضي: 30% خصم، 30 يوم انتهاء)
```

#### عرض الأكواد النشطة:
```
/list_codes
```

### للمستخدمين:

#### استخدام كود برومو:
```
/redeem YOUR_CODE

أمثلة:
/redeem TRIAL12AB34
/redeem SAVE30XY9ZT
```

## 💼 كيفية عمل النظام

### 1. إنشاء الأكواد:
1. الإدارة تستخدم أوامر الإنشاء
2. النظام يولد كود عشوائي فريد
3. البيانات تحفظ في Google Sheets
4. الكود يصبح جاهز للاستخدام

### 2. استخدام الأكواد:
1. المستخدم يرسل `/redeem CODE`
2. النظام يتحقق من صحة الكود
3. يفحص عدم الاستخدام السابق
4. يطبق المكافأة (تجربة مجانية أو خصم)
5. يحدث بيانات الاستخدام

### 3. التحقق من الصحة:
- **كود صحيح؟** موجود في قاعدة البيانات
- **نشط؟** حالة الكود = "نشط"
- **لم ينته؟** التاريخ الحالي < تاريخ الانتهاء  
- **لم يستخدم؟** معرف المستخدم غير موجود في "مستخدم بواسطة"

## 🎨 رسائل التحفيز والتحويل

### 1. رسائل محدودية الخدمة:
- **عند الوصول للحد اليومي**: رسالة تشجع على الاشتراك أو استخدام كود
- **عند محاولة استخدام ميزة مدفوعة**: رسالة توضح فوائد الاشتراك
- **عند اقتراب الحد**: رسالة تحفيزية للتحويل

### 2. أنواع الرسائل:
```python
UPGRADE_MOTIVATIONAL_MESSAGES = {
    "daily_limit_reached": "رسالة عند انتهاء الحد اليومي",
    "premium_feature_blocked": "رسالة عند محاولة استخدام ميزة مدفوعة", 
    "conversion_opportunity": "رسالة تحفيزية للتحويل"
}
```

### 3. رسائل نجاح الأكواد:
```python
CODE_SUCCESS_MESSAGES = {
    "trial_activated": "رسالة نجاح تفعيل التجربة المجانية",
    "discount_activated": "رسالة نجاح تفعيل كود الخصم"
}
```

## 🔧 التكامل مع النظام الحالي

### في `user_limit.py`:
```python
# إضافة دوال جديدة:
- send_upgrade_message()        # إرسال رسائل التحويل
- get_user_promo_codes()        # جلب أكواد المستخدم
- apply_trial_from_code()       # تطبيق التجربة المجانية
- has_active_discount()         # فحص الخصومات النشطة
- check_and_send_conversion_message() # رسائل تحفيزية ذكية
```

### في `process_data.py`:
سيتم تعديل الدوال لاستخدام الرسائل الجديدة:
```python
# بدلاً من الرسالة القديمة:
"لقد وصلت للحد اليومي"

# الرسالة الجديدة:
UserManager.send_upgrade_message(user_id, "daily_limit_reached")
```

## 📈 مزايا النظام

### 1. تحفيز التحويل:
- رسائل مخصصة حسب سلوك المستخدم
- عروض وخصومات جذابة
- تجربة مجانية تظهر قيمة الخدمة

### 2. إدارة متقدمة:
- تتبع استخدام الأكواد
- إحصائيات مفصلة  
- تحكم كامل في العروض

### 3. تجربة محسنة:
- رسائل واضحة ومحفزة
- سهولة استخدام الأكواد
- تنوع في العروض والحوافز

## ⚙️ التثبيت والتفعيل

### 1. إضافة ملف promo_codes.py:
```bash
# الملف موجود ويحتوي على جميع الكلاسات والدوال
```

### 2. تحديث user_limit.py:
```python
# تم إضافة الدوال الجديدة
```

### 3. تحديث arabic_messages.py:
```python
# تم إضافة الرسائل التحفيزية
```

### 4. إضافة الأوامر في main.py:
```python
# نسخ الكود من PROMO_COMMANDS_CODE في promo_codes.py
```

### 5. إعداد Google Sheets:
- سيتم إنشاء worksheet "promo_codes" تلقائياً عند أول استخدام

## 🔍 أمثلة عملية

### سيناريو 1: مستخدم جديد
```
1. مستخدم جديد يبدأ استخدام البوت
2. يتلقى رسالة ترحيب تحتوي على كود WELCOME7
3. يستخدم /redeem WELCOME7
4. يحصل على 7 أيام تجربة مجانية
5. يستفيد من جميع الميزات لفترة التجربة
```

### سيناريو 2: مستخدم يقترب من الحد
```
1. مستخدم استخدم 4 من 5 تحليلات مجانية
2. يتلقى رسالة تحفيزية مع كود خصم 40%
3. يقرر الاشتراك باستخدام الكود
4. يحصل على خصم فوري عند الدفع
```

### سيناريو 3: عرض نهاية الأسبوع
```
1. الإدارة تنشئ كود WEEKEND50 بخصم 50%
2. يتم نشر الكود في القنوات التسويقية
3. المستخدمون يستخدمون /redeem WEEKEND50
4. يحصلون على خصم 50% عند الاشتراك
```

## 📊 المقاييس والتحليلات

يمكن تتبع:
- عدد الأكواد المستخدمة
- معدل التحويل من كود لاشتراك
- أكثر أنواع الأكواد فعالية
- المستخدمين الأكثر نشاطاً

## 🚀 الخطوات التالية

1. **تطبيق النظام**: نسخ الأوامر إلى main.py
2. **اختبار شامل**: تجربة جميع السيناريوهات  
3. **مراقبة الأداء**: تتبع معدلات التحويل
4. **تحسين الرسائل**: تطوير محتوى أكثر تأثيراً
5. **توسيع النظام**: إضافة ميزات جديدة

---

**📝 ملاحظة**: هذا النظام قابل للتوسع ويمكن إضافة ميزات جديدة مثل:
- أكواد الإحالة
- نظام نقاط الولاء
- عروض موسمية تلقائية
- تخصيص الرسائل حسب سلوك المستخدم
