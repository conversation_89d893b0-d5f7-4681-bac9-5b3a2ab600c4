#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات متخصصة لمكونات النظام المختلفة
Specialized Component Tests for Trading System
"""

import requests
import json
import time
import logging
from datetime import datetime
import sys

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedSystemTests:
    def __init__(self, base_url="http://localhost:9000"):
        self.base_url = base_url
        
    def test_arabic_message_support(self):
        """اختبار دعم الرسائل العربية"""
        logger.info("🌍 اختبار دعم الرسائل العربية")
        
        arabic_signal = {
            "stock_code": "شركة الاختبار",
            "report": "buy",
            "buy_price": "15.75",
            "tp1": "16.50",
            "tp2": "17.25",
            "tp3": "18.00",
            "sl": "14.50"
        }
        
        try:
            headers = {'Content-Type': 'application/json; charset=utf-8'}
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(arabic_signal, ensure_ascii=False),
                headers=headers,
                timeout=10
            )
            
            if response.status_code in [200, 400]:
                logger.info("  ✅ النظام يتعامل مع النصوص العربية")
                return True
            else:
                logger.warning(f"  ⚠️ مشكلة في التعامل مع العربية: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ خطأ في اختبار العربية: {e}")
            return False
    
    def test_google_sheets_integration(self):
        """اختبار تكامل Google Sheets"""
        logger.info("📊 اختبار تكامل Google Sheets")
        
        # إرسال إشارة صحيحة ومراقبة ما إذا كانت تحاول الوصول لـ Google Sheets
        test_signal = {
            "stock_code": "GOOG_TEST",
            "report": "buy",
            "buy_price": "20.00",
            "tp1": "21.00",
            "tp2": "22.00",
            "tp3": "23.00",
            "sl": "19.00"
        }
        
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(test_signal),
                headers=headers,
                timeout=15  # وقت أطول للسماح بمحاولة الوصول لـ Google Sheets
            )
            
            # نحلل الاستجابة لفهم ما حدث
            if response.status_code == 200:
                logger.info("  ✅ الإشارة تمت معالجتها بنجاح")
                try:
                    data = response.json()
                    if 'sheets_updated' in data or 'google' in str(data).lower():
                        logger.info("  ✅ تكامل Google Sheets يعمل")
                        return True
                    else:
                        logger.info("  ⚠️ معالجة الإشارة نجحت لكن لا يوجد دليل على تحديث Google Sheets")
                        return False
                except:
                    logger.info("  ⚠️ استجابة غير JSON - قد تكون معالجة صامتة")
                    return False
                    
            elif response.status_code == 500:
                logger.warning("  ⚠️ خطأ خادم - قد يكون مشكلة في Google Sheets API")
                return False
            else:
                logger.warning(f"  ⚠️ استجابة غير متوقعة: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            logger.warning("  ⚠️ انتهاء المهلة - قد يكون النظام يحاول الوصول لـ Google Sheets")
            return False
        except Exception as e:
            logger.error(f"  ❌ خطأ في اختبار Google Sheets: {e}")
            return False
    
    def test_telegram_integration(self):
        """اختبار تكامل Telegram"""
        logger.info("💬 اختبار تكامل Telegram")
        
        # إرسال إشارة للتحقق من إرسال رسائل Telegram
        telegram_test = {
            "stock_code": "TELE_TEST",
            "report": "sell",
            "sell_price": "25.00",
            "tp1": "24.00",
            "tp2": "23.00",
            "tp3": "22.00",
            "sl": "26.00"
        }
        
        try:
            headers = {'Content-Type': 'application/json'}
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(telegram_test),
                headers=headers,
                timeout=20
            )
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                logger.info(f"  ✅ معالجة الإشارة نجحت ({response_time:.2f}s)")
                
                # وقت الاستجابة الطويل قد يدل على إرسال رسائل
                if response_time > 2:
                    logger.info("  💬 وقت استجابة طويل - قد يكون هناك إرسال رسائل")
                
                try:
                    data = response.json()
                    if 'telegram' in str(data).lower() or 'message_sent' in data:
                        logger.info("  ✅ دليل على إرسال رسائل Telegram")
                        return True
                    else:
                        logger.info("  ⚠️ لا يوجد دليل واضح على إرسال Telegram")
                        return False
                except:
                    logger.info("  ⚠️ استجابة غير JSON")
                    return False
                    
            else:
                logger.warning(f"  ⚠️ فشل معالجة الإشارة: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"  ❌ خطأ في اختبار Telegram: {e}")
            return False
    
    def test_data_validation_logic(self):
        """اختبار منطق التحقق من البيانات"""
        logger.info("🔍 اختبار منطق التحقق من البيانات")
        
        validation_tests = [
            {
                "name": "أسعار هدف منطقية (شراء)",
                "data": {
                    "stock_code": "VAL001",
                    "report": "buy",
                    "buy_price": "10.00",
                    "tp1": "10.50",
                    "tp2": "11.00",
                    "tp3": "11.50",
                    "sl": "9.50"
                },
                "expected": "accept"
            },
            {
                "name": "أسعار هدف خاطئة (شراء)",
                "data": {
                    "stock_code": "VAL002",
                    "report": "buy",
                    "buy_price": "10.00",
                    "tp1": "9.50",  # أقل من سعر الشراء
                    "tp2": "11.00",
                    "tp3": "11.50",
                    "sl": "9.50"
                },
                "expected": "reject"
            },
            {
                "name": "أسعار هدف منطقية (بيع)",
                "data": {
                    "stock_code": "VAL003",
                    "report": "sell",
                    "sell_price": "10.00",
                    "tp1": "9.50",
                    "tp2": "9.00",
                    "tp3": "8.50",
                    "sl": "10.50"
                },
                "expected": "accept"
            },
            {
                "name": "ستوب لوس خاطئ (بيع)",
                "data": {
                    "stock_code": "VAL004",
                    "report": "sell",
                    "sell_price": "10.00",
                    "tp1": "9.50",
                    "tp2": "9.00",
                    "tp3": "8.50",
                    "sl": "9.00"  # أقل من سعر البيع
                },
                "expected": "reject"
            }
        ]
        
        passed_validations = 0
        total_validations = len(validation_tests)
        
        for test in validation_tests:
            try:
                headers = {'Content-Type': 'application/json'}
                response = requests.post(
                    f"{self.base_url}/webhook?jsonRequest=true",
                    data=json.dumps(test["data"]),
                    headers=headers,
                    timeout=10
                )
                
                if test["expected"] == "accept" and response.status_code == 200:
                    logger.info(f"  ✅ {test['name']}: قبول صحيح")
                    passed_validations += 1
                elif test["expected"] == "reject" and response.status_code == 400:
                    logger.info(f"  ✅ {test['name']}: رفض صحيح")
                    passed_validations += 1
                else:
                    logger.warning(f"  ⚠️ {test['name']}: نتيجة غير متوقعة ({response.status_code})")
                    
            except Exception as e:
                logger.error(f"  ❌ {test['name']}: خطأ - {e}")
        
        success_rate = (passed_validations / total_validations) * 100
        logger.info(f"  📊 معدل نجاح التحقق: {success_rate:.1f}% ({passed_validations}/{total_validations})")
        
        return success_rate >= 75
    
    def test_concurrent_signals(self):
        """اختبار الإشارات المتزامنة"""
        logger.info("🔄 اختبار معالجة الإشارات المتزامنة")
        
        import threading
        import queue
        
        results = queue.Queue()
        
        def send_signal(signal_id):
            signal = {
                "stock_code": f"CONC{signal_id:03d}",
                "report": "buy",
                "buy_price": f"{10 + signal_id * 0.1:.2f}",
                "tp1": f"{11 + signal_id * 0.1:.2f}",
                "tp2": f"{12 + signal_id * 0.1:.2f}",
                "tp3": f"{13 + signal_id * 0.1:.2f}",
                "sl": f"{9 + signal_id * 0.1:.2f}"
            }
            
            try:
                headers = {'Content-Type': 'application/json'}
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/webhook?jsonRequest=true",
                    data=json.dumps(signal),
                    headers=headers,
                    timeout=15
                )
                response_time = time.time() - start_time
                
                results.put({
                    "signal_id": signal_id,
                    "status_code": response.status_code,
                    "response_time": response_time,
                    "success": response.status_code == 200
                })
                
            except Exception as e:
                results.put({
                    "signal_id": signal_id,
                    "error": str(e),
                    "success": False
                })
        
        # إرسال 3 إشارات متزامنة
        threads = []
        num_signals = 3
        
        start_time = time.time()
        for i in range(num_signals):
            thread = threading.Thread(target=send_signal, args=(i,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # جمع النتائج
        all_results = []
        while not results.empty():
            all_results.append(results.get())
        
        successful_signals = len([r for r in all_results if r.get("success", False)])
        avg_response_time = sum([r.get("response_time", 0) for r in all_results if "response_time" in r]) / len(all_results) if all_results else 0
        
        logger.info(f"  📊 النتائج:")
        logger.info(f"    🎯 إشارات ناجحة: {successful_signals}/{num_signals}")
        logger.info(f"    ⏱️ الوقت الإجمالي: {total_time:.2f}s")
        logger.info(f"    📈 متوسط وقت الاستجابة: {avg_response_time:.2f}s")
        
        # النجاح إذا كانت جميع الإشارات نجحت
        return successful_signals == num_signals
    
    def test_memory_usage_pattern(self):
        """اختبار نمط استخدام الذاكرة"""
        logger.info("💾 اختبار نمط استخدام الذاكرة")
        
        # إرسال عدة إشارات متتالية لمراقبة الذاكرة
        num_signals = 10
        response_times = []
        
        for i in range(num_signals):
            signal = {
                "stock_code": f"MEM{i:03d}",
                "report": "buy" if i % 2 == 0 else "sell",
                "buy_price" if i % 2 == 0 else "sell_price": f"{15 + i * 0.5:.2f}",
                "tp1": f"{16 + i * 0.5:.2f}" if i % 2 == 0 else f"{14 + i * 0.5:.2f}",
                "tp2": f"{17 + i * 0.5:.2f}" if i % 2 == 0 else f"{13 + i * 0.5:.2f}",
                "tp3": f"{18 + i * 0.5:.2f}" if i % 2 == 0 else f"{12 + i * 0.5:.2f}",
                "sl": f"{14 + i * 0.5:.2f}" if i % 2 == 0 else f"{16 + i * 0.5:.2f}"
            }
            
            try:
                headers = {'Content-Type': 'application/json'}
                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/webhook?jsonRequest=true",
                    data=json.dumps(signal),
                    headers=headers,
                    timeout=10
                )
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                logger.info(f"    📊 إشارة {i+1}: {response.status_code} ({response_time:.3f}s)")
                
                time.sleep(0.2)  # توقف قصير
                
            except Exception as e:
                logger.warning(f"    ⚠️ إشارة {i+1}: خطأ - {e}")
                response_times.append(float('inf'))
        
        # تحليل الأداء
        valid_times = [t for t in response_times if t != float('inf')]
        if valid_times:
            avg_time = sum(valid_times) / len(valid_times)
            max_time = max(valid_times)
            min_time = min(valid_times)
            
            # التحقق من تدهور الأداء
            first_half = valid_times[:len(valid_times)//2]
            second_half = valid_times[len(valid_times)//2:]
            
            if first_half and second_half:
                first_avg = sum(first_half) / len(first_half)
                second_avg = sum(second_half) / len(second_half)
                performance_change = ((second_avg - first_avg) / first_avg) * 100
                
                logger.info(f"  📈 متوسط وقت الاستجابة: {avg_time:.3f}s")
                logger.info(f"  📊 النطاق: {min_time:.3f}s - {max_time:.3f}s")
                logger.info(f"  🔄 تغيير الأداء: {performance_change:+.1f}%")
                
                # إذا تدهور الأداء بأكثر من 50%، قد تكون هناك مشكلة ذاكرة
                if performance_change > 50:
                    logger.warning("  ⚠️ تدهور كبير في الأداء - قد تكون هناك مشكلة ذاكرة")
                    return False
                else:
                    logger.info("  ✅ الأداء مستقر")
                    return True
            else:
                logger.warning("  ⚠️ بيانات غير كافية للتحليل")
                return False
        else:
            logger.error("  ❌ لا توجد استجابات صحيحة")
            return False
    
    def run_advanced_tests(self):
        """تشغيل جميع الاختبارات المتقدمة"""
        logger.info("🔬 بدء الاختبارات المتقدمة لنظام إشارات التداول")
        logger.info("=" * 60)
        
        tests = [
            ("دعم الرسائل العربية", self.test_arabic_message_support),
            ("تكامل Google Sheets", self.test_google_sheets_integration),
            ("تكامل Telegram", self.test_telegram_integration),
            ("منطق التحقق من البيانات", self.test_data_validation_logic),
            ("الإشارات المتزامنة", self.test_concurrent_signals),
            ("نمط استخدام الذاكرة", self.test_memory_usage_pattern)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                logger.info(f"\n🧪 {test_name}")
                result = test_func()
                results[test_name] = "نجح" if result else "فشل"
                
                if result:
                    logger.info(f"  ✅ {test_name}: نجح")
                else:
                    logger.warning(f"  ❌ {test_name}: فشل")
                    
            except Exception as e:
                logger.error(f"  🚨 {test_name}: خطأ - {e}")
                results[test_name] = f"خطأ: {str(e)}"
            
            time.sleep(1)  # توقف بين الاختبارات
        
        # ملخص النتائج
        logger.info("\n" + "=" * 60)
        logger.info("📊 ملخص نتائج الاختبارات المتقدمة")
        logger.info("=" * 60)
        
        for test_name, result in results.items():
            status_emoji = "✅" if result == "نجح" else "❌" if result == "فشل" else "🚨"
            logger.info(f"{status_emoji} {test_name}: {result}")
        
        successful_tests = len([r for r in results.values() if r == "نجح"])
        total_tests = len(results)
        success_rate = (successful_tests / total_tests) * 100
        
        logger.info(f"\n📈 معدل النجاح الإجمالي: {success_rate:.1f}% ({successful_tests}/{total_tests})")
        
        return results

def main():
    """الدالة الرئيسية للاختبارات المتقدمة"""
    print("🔬 الاختبارات المتقدمة لنظام إشارات التداول")
    print("=" * 50)
    
    tester = AdvancedSystemTests("http://localhost:9000")
    
    try:
        results = tester.run_advanced_tests()
        
        # حفظ النتائج
        with open('advanced_test_results.json', 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "results": results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ النتائج في: advanced_test_results.json")
        
    except KeyboardInterrupt:
        logger.info("\n🛑 تم إيقاف الاختبارات بواسطة المستخدم")
    except Exception as e:
        logger.error(f"🚨 خطأ في تشغيل الاختبارات: {e}")

if __name__ == "__main__":
    main()
