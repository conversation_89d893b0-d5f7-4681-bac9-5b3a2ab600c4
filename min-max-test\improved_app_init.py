"""
Enhanced Flask application initialization with security features
"""
import os
import logging
from flask import Flask, request
from werkzeug.middleware.proxy_fix import ProxyFix

logger = logging.getLogger(__name__)

def create_secure_app():
    """
    Create a Flask application with enhanced security settings
    """
    try:
        # Create Flask app instance
        app = Flask(__name__)
        
        # Apply ProxyFix middleware for correct IP address handling behind proxies
        app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1)
        
        # Security configurations
        app.config.update(
            # Generate a secure secret key or use environment variable
            SECRET_KEY=os.environ.get('SECRET_KEY', os.urandom(24)),
            
            # Session security
            SESSION_COOKIE_SECURE=True,
            SESSION_COOKIE_HTTPONLY=True,
            SESSION_COOKIE_SAMESITE='Lax',
            PERMANENT_SESSION_LIFETIME=1800,  # 30 minutes
            
            # CSRF protection
            WTF_CSRF_ENABLED=True,
            WTF_CSRF_SECRET_KEY=os.environ.get('CSRF_SECRET_KEY', os.urandom(24)),
            
            # Content Security Policy
            CSP={
                'default-src': "'self'",
                'style-src': ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                'script-src': ["'self'", "https://cdn.jsdelivr.net"],
                'font-src': ["'self'", "https://cdn.jsdelivr.net"],
                'img-src': ["'self'", "data:"],
            },
            
            # Control upload size
            MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16 MB max upload
        )
        
        # Security headers middleware
        @app.after_request
        def add_security_headers(response):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'SAMEORIGIN'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            
            # Content Security Policy header
            csp_directives = []
            for key, value in app.config.get('CSP', {}).items():
                if isinstance(value, list):
                    csp_directives.append(f"{key} {' '.join(value)}")
                else:
                    csp_directives.append(f"{key} {value}")
            if csp_directives:
                response.headers['Content-Security-Policy'] = '; '.join(csp_directives)
                
            return response
            
        # Request logging for security
        @app.before_request
        def log_request_info():
            # Log potentially suspicious requests
            path = request.path
            if '../' in path or '..' in path or '\\' in path:
                security_logger = logging.getLogger('security')
                security_logger.warning(
                    f"Suspicious path detected: {path} from IP {request.remote_addr}"
                )
                
        # Error handlers
        @app.errorhandler(404)
        def page_not_found(e):
            return "Page not found", 404

        @app.errorhandler(500)
        def server_error(e):
            logger.error(f"Server error: {e}")
            return "Internal server error", 500
        
        # Success
        logger.info("Secure Flask app initialized successfully")
        return app
        
    except Exception as e:
        logger.error(f"Failed to initialize secure Flask app: {e}")
        raise

# Simple test to verify module works
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    app = create_secure_app()
    print("App initialized successfully!")
