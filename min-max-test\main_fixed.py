import logging
from auth import authenticate_google_sheet,dp,bot
from aiogram import Bo<PERSON>, Di<PERSON><PERSON>er, executor
import server
import threading
import sys
import argparse
from user_limit import check_user_limit,display_refer_link
from update_google_sheet import update_google_sheet,update_sheet
from reset_counter import reset_counters_command
from upgrade import upgrade_user,upgrade_command
from plot import chart_handler

# استيراد نظام أكواد الخصم الجديد
try:
    from promo_codes import PromoCodeManager
    from user_limit import UserManager
    import datetime
    PROMO_CODES_AVAILABLE = True
    print("✅ تم تحميل نظام الأكواد بنجاح")
except ImportError as e:
    PROMO_CODES_AVAILABLE = False
    logging.warning(f"Promo codes system not available: {e}")
except Exception as e:
    PROMO_CODES_AVAILABLE = False
    logging.error(f"Error importing promo codes system: {e}")

from process_data import (
    process_modarba_all, 
    process_modarba,
    process_stock_code,
    process_analyze_command,
    process_hv,
    process_callback_messages,
    process_help,
    process_subscribe,
    cmd_menu,
    get_subscription_date,
    fibo,
    process_deals,
    handle_deals_button,
    handle_chart_button,
    BUTTON_DEALS,
    BUTTON_CHART,
)

logging.basicConfig(level=logging.INFO)

# ===== أوامر نظام أكواد الخصم =====

async def create_trial_code_command(message):
    """إنشاء كود تجربة مجانية - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة (إذا كان ملف config موجود)
    try:
        from config import ADMIN_IDS
        if user_id not in ADMIN_IDS:
            await message.reply("هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        # إذا لم يوجد ملف config، نسمح لأي شخص (للاختبار)
        pass
    
    try:
        args = message.text.split()
        days = int(args[1]) if len(args) > 1 else 7
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود تجربة مجانية"
        
        code = PromoCodeManager.create_trial_code(days, expiry_days, note)
        
        if code:
            admin_msg = f"""✅ **تم إنشاء كود التجربة المجانية بنجاح!**

🎫 **الكود:** `{code}`
📅 **مدة التجربة:** {days} أيام
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`"""
            await message.reply(admin_msg, parse_mode="Markdown")
        else:
            await message.reply("حدث خطأ في إنشاء الكود")
    except:
        await message.reply("""📋 **طريقة الاستخدام:**
`/create_trial_code [أيام] [انتهاء] [ملاحظة]`

**مثال:**
`/create_trial_code 7 30 كود خاص للأعضاء الجدد`""", parse_mode="Markdown")

async def create_discount_code_command(message):
    """إنشاء كود خصم - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    try:
        from config import ADMIN_IDS
        if user_id not in ADMIN_IDS:
            await message.reply("هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        # إذا لم يوجد ملف config، نسمح لأي شخص (للاختبار)
        pass
    
    try:
        args = message.text.split()
        discount_percent = int(args[1]) if len(args) > 1 else 20
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود خصم خاص"
        
        code = PromoCodeManager.create_discount_code(discount_percent, expiry_days, note)
        
        if code:
            admin_msg = f"""✅ **تم إنشاء كود الخصم بنجاح!**

🎫 **الكود:** `{code}`
💰 **نسبة الخصم:** {discount_percent}%
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`"""
            await message.reply(admin_msg, parse_mode="Markdown")
        else:
            await message.reply("حدث خطأ في إنشاء الكود")
    except:
        await message.reply("""📋 **طريقة الاستخدام:**
`/create_discount_code [نسبة] [انتهاء] [ملاحظة]`

**مثال:**
`/create_discount_code 50 15 عرض نهاية الأسبوع`""", parse_mode="Markdown")

async def redeem_promo_code(message):
    """استخدام كود البرومو"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("نظام الأكواد غير متاح حالياً")
        return
        
    args = message.text.split()
    if len(args) != 2:
        await message.reply("🎫 الاستخدام: `/redeem YOUR_CODE`", parse_mode="Markdown")
        return
    
    code = args[1].upper()
    user_id = str(message.from_user.id)
    
    is_valid, error_msg, code_data = PromoCodeManager.validate_promo_code(code, user_id)
    
    if not is_valid:
        await message.reply(f"❌ {error_msg}")
        return
    
    # تطبيق الكود حسب نوعه
    if code_data['type'] == 'trail':
        from user_limit import UserManager
        user_manager = UserManager()
        trail_days = int(code_data['value'])
        
        # إضافة اشتراك تجريبي للمستخدم
        import datetime
        trail_end = (datetime.date.today() + datetime.timedelta(days=trail_days)).strftime("%Y-%m-%d")
        
        user_manager.update_user_data(
            row=code_data.get('user_row', 1),
            subscription_type='trail',
            end_date=trail_end
        )
        
        PromoCodeManager.use_promo_code(code_data, user_id)
        await message.reply(f"🎉 تم تفعيل التجربة المجانية لمدة {trail_days} أيام!")
    
    elif code_data['type'] == 'discount':
        PromoCodeManager.use_promo_code(code_data, user_id)
        await message.reply(f"💰 تم تفعيل كود خصم {code_data['value']}%")

async def list_promo_codes(message):
    """عرض الأكواد النشطة - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    try:
        from config import ADMIN_IDS
        if user_id not in ADMIN_IDS:
            await message.reply("هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        # إذا لم يوجد ملف config، نسمح لأي شخص (للاختبار)
        pass
    
    active_codes = PromoCodeManager.list_active_codes()
    
    if not active_codes:
        await message.reply("لا توجد أكواد نشطة حالياً")
        return
    
    codes_text = "📋 **الأكواد النشطة:**\n\n"
    
    for code in active_codes[:10]:  # عرض أول 10 أكواد فقط
        code_type = "تجربة مجانية" if code['type'] == 'trail' else "خصم"
        codes_text += f"🎫 `{code['code']}` - {code_type} ({code['value']})\n"
        codes_text += f"   📅 ينتهي: {code['expiry_date']}\n"
        codes_text += f"   🔢 استُخدم: {code['usage_count']} مرة\n\n"
    
    if len(active_codes) > 10:
        codes_text += f"... وهناك {len(active_codes) - 10} كود آخر"
    
    await message.reply(codes_text, parse_mode="Markdown")

def parse_arguments():
    parser = argparse.ArgumentParser(description='Stock Analyzer Bot')
    parser.add_argument('--api-port', type=int, default=8000,
                        help='Port for the API server (default: 8000)')
    parser.add_argument('--api-host', type=str, default='0.0.0.0',
                        help='Host for the API server (default: 0.0.0.0)')
    parser.add_argument('--disable-api', action='store_true',
                        help='Disable the API server')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    if not args.disable_api:
        try:
            # Start API server with explicit parameters
            print(f"Starting API server on {args.api_host}:{args.api_port}")
            server_thread = threading.Thread(
                target=server.start_server,
                args=(args.api_host, args.api_port),
                daemon=True  # Make thread daemon so it exits when main program exits
            )
            server_thread.start()
            print(f"API thread started")
            
            # Also start webhook server
            print("Starting webhook server on port 9000")
            server.start_server_async()
            print("Webhook thread started")
        except Exception as e:
            print(f"Error starting API server: {e}", file=sys.stderr)
            logging.error(f"Error starting API server: {e}", exc_info=True)
    else:
        print("API server disabled")

    # Register message handlers
    dp.message_handler(commands=['comp'])(update_sheet)
    dp.message_handler(commands=['subscribe'])(process_subscribe)
    dp.message_handler(commands=['start'])(lambda message: check_user_limit(message, bot))
    dp.message_handler(commands=['help'])(process_help)
    dp.message_handler(commands=['modarba_all'])(process_modarba_all)
    dp.message_handler(commands=['modarba'])(process_modarba)
    dp.message_handler(commands=['hv'])(process_hv)
    dp.message_handler(commands=['mysubscribtion'])(get_subscription_date)
    dp.message_handler(commands=['stock'])(process_stock_code)
    dp.message_handler(commands=['analyze'])(process_analyze_command)
    dp.message_handler(commands=['fibo'])(fibo)
    dp.message_handler(commands=['reset_counters'])(reset_counters_command)
    dp.message_handler(commands=['upgrade'])(upgrade_command)
    dp.message_handler(commands=['chart'])(chart_handler)
    dp.message_handler(commands=['deals'])(process_deals)
    dp.message_handler(commands=['myrefer'])(display_refer_link)
    dp.register_message_handler(cmd_menu, commands=['menu'])
    dp.register_message_handler(lambda message: message.text == "📊 الصفقات", handle_deals_button)
    dp.register_message_handler(process_callback_messages)
    
    # Register the specific handlers for these buttons
    dp.register_message_handler(handle_deals_button, lambda message: message.text == BUTTON_DEALS)
    dp.register_message_handler(handle_chart_button, lambda message: message.text == BUTTON_CHART)
    
    # ===== تسجيل أوامر نظام أكواد الخصم =====
    print(f"PROMO_CODES_AVAILABLE: {PROMO_CODES_AVAILABLE}")
    if PROMO_CODES_AVAILABLE:
        print("🎫 تسجيل أوامر الأكواد...")
        dp.message_handler(commands=['create_trial_code'])(create_trial_code_command)
        dp.message_handler(commands=['create_discount_code'])(create_discount_code_command) 
        dp.message_handler(commands=['redeem'])(redeem_promo_code)
        dp.message_handler(commands=['list_codes'])(list_promo_codes)
        print("✅ تم تسجيل أوامر الأكواد بنجاح!")
    else:
        print("⚠️ أوامر الأكواد غير متاحة - PROMO_CODES_AVAILABLE = False")
    
    # Start the bot
    print("🚀 Starting bot...")
    executor.start_polling(dp, skip_updates=True)
