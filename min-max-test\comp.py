import gspread
import pandas as pd
from oauth2client.service_account import ServiceAccountCredentials

# Column configurations
COLUMN_CONFIG = {
    'excel': {
        'stock_code': 0,
        'high_price': 3
    },
    'google': {
        'buy_sell': 2,
        't1': 7,
        't2': 8,
        't3': 9,
        'sl': 10
    }
}

def setup_google_connection():
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    creds = ServiceAccountCredentials.from_json_keyfile_name('client_secret.json', scope)
    return gspread.authorize(creds)

def update_target_status(sheet, index, high_price, t1, t2, t3, sl):
    current_date = str(pd.Timestamp.now().date())
    base_row = index + 2
    
    if high_price <= sl:
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['buy_sell'] + 1, 'sl')
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['sl'] + 1, current_date)
    elif high_price >= t3:
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['buy_sell'] + 1, 't3done')
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['t3'] + 1, current_date)
    elif high_price >= t2:
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['buy_sell'] + 1, 't2done')
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['t2'] + 1, current_date)
    elif high_price >= t1:
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['buy_sell'] + 1, 't1done')
        sheet.update_cell(base_row, COLUMN_CONFIG['google']['t1'] + 1, current_date)

def main():
    client = setup_google_connection()
    sheet = client.open('Sheet1').worksheet('data')
    
    excel_data = pd.read_excel('data.xlsx')
    excel_data_filtered = excel_data.iloc[:, [COLUMN_CONFIG['excel']['stock_code'], 
                                            COLUMN_CONFIG['excel']['high_price']]]
    
    google_data = sheet.get_all_values()
    columns = [None] * 13
    columns[2] = 'buy_sell'
    columns[4:8] = ['t1', 't2', 't3', 'sl']
    
    google_data_filtered = pd.DataFrame(google_data, columns=columns)
    google_data_filtered = google_data_filtered.iloc[1:, [COLUMN_CONFIG['google']['buy_sell'], 
                                                        COLUMN_CONFIG['google']['t1'],
                                                        COLUMN_CONFIG['google']['t2'], 
                                                        COLUMN_CONFIG['google']['t3'],
                                                        COLUMN_CONFIG['google']['sl']]]
    
    merged_data = pd.merge(excel_data_filtered, google_data_filtered, 
                          left_on=COLUMN_CONFIG['excel']['stock_code'], right_on=0)
    
    for index, row in merged_data.iterrows():
        high_price = row[1]
        t1 = float(row[2])
        t2 = float(row[3])
        t3 = float(row[4])
        sl = float(row[5])
        
        update_target_status(sheet, index, high_price, t1, t2, t3, sl)
    
    print("Google Sheet updated successfully!")

if __name__ == "__main__":
    main()
