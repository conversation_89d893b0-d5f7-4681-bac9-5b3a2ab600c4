from flask import Flask, request, jsonify, render_template, redirect, url_for
import gspread
import json
import os
from oauth2client.service_account import ServiceAccountCredentials
import datetime
from threading import Thread
from aiogram.types import Message
from aiogram import <PERSON><PERSON>, Dispatcher, executor, types
from user_limit import check_user_limit
from auth import open_google_sheet,dp
from config import bot_token, ADMIN_IDS
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
import pandas as pd
from stock_analyzer import StockAnalyzer
from urllib.parse import urlparse, parse_qs
import socket
import sys
import re
import traceback
from arabic_messages import ANALYSIS_FOOTER
from config import file_path
from technical_analysis import analyze_money_flow
import numpy as np
app = Flask('')
# Initialize bot and dispatcher
#bot = Bot(token=os.environ['TELEGRAM_TOKEN'])
#dp = Dispatcher(bot)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the stock analyzer
analyzer = StockAnalyzer()

class StockAnalyzerHandler(BaseHTTPRequestHandler):
    def _set_headers(self, content_type='application/json', status_code=200):
        self.send_response(status_code)
        self.send_header('Content-type', content_type)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
    def do_GET(self):
        try:
            url_components = urlparse(self.path)
            path = url_components.path
            query_params = parse_qs(url_components.query)
            
            # Handle API health check
            if path == '/api/health':
                self._set_headers()
                response = {'status': 'healthy', 'version': '1.0.0'}
                self.wfile.write(json.dumps(response).encode())
                return
                
            # Handle stock analysis requests via both formats:
            # 1. Query parameter: /api/analyze?ticker=COMI
            # 2. Path parameter: /api/analyze/COMI
            if path == '/api/analyze':
                # Get ticker from query parameter
                ticker = query_params.get('ticker', [''])[0]
                
                if not ticker:
                    self._set_headers(status_code=400)
                    response = {'error': 'Missing required parameter: ticker', 'usage': 'Use /api/analyze?ticker=SYMBOL or /api/analyze/SYMBOL'}
                    self.wfile.write(json.dumps(response).encode())
                    return
                
                self._process_stock_analysis(ticker)
                
            # Handle RESTful path format: /api/analyze/TICKER
            elif path.startswith('/api/analyze/'):
                # Extract ticker from path
                ticker = path.split('/api/analyze/')[1].strip()
                
                if not ticker:
                    self._set_headers(status_code=400)
                    response = {'error': 'Missing ticker symbol in path', 'usage': 'Use /api/analyze/SYMBOL'}
                    self.wfile.write(json.dumps(response).encode())
                    return
                
                self._process_stock_analysis(ticker)
                
            else:
                self._set_headers(status_code=404)
                response = {
                    'error': 'Not Found',
                    'availableEndpoints': [
                        '/api/health',
                        '/api/analyze?ticker=SYMBOL',
                        '/api/analyze/SYMBOL'
                    ]
                }
                self.wfile.write(json.dumps(response).encode())
                
        except Exception as e:
            logger.error(f"Server error: {str(e)}", exc_info=True)
            self._set_headers(status_code=500)
            response = {'error': f'Server error: {str(e)}'}
            self.wfile.write(json.dumps(response).encode())
    
    def _process_stock_analysis(self, ticker):
        """Process a stock analysis request for the given ticker"""
        try:
            # Normalize ticker format
            ticker = ticker.upper().strip()
            
            # Load Excel data
            try:
                excel_path = file_path
                df = pd.read_excel(excel_path)
                stock_data = df[df.iloc[:, 0].astype(str).str.upper() == ticker]
                
                if stock_data.empty:
                    # Try again with 'D' suffix as fallback
                    if not ticker.endswith('D'):
                        stock_data = df[df.iloc[:, 0].astype(str).str.upper() == f"{ticker}D"]
                    
                    if stock_data.empty:
                        self._set_headers(status_code=404)
                        response = {
                            'error': f'Stock not found: {ticker}',
                            'message_ar': f'❌ السهم غير موجود: {ticker}',
                            'suggestion': 'Please check the stock code and try again.',
                            'suggestion_ar': '🔍 يرجى التحقق من رمز السهم والمحاولة مرة أخرى.',
                            'status': 'error'
                        }
                        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
                        return
                
                # Analyze the stock
                analysis_result = analyzer.analyze_stock(stock_data)
                
                # Return the analysis
                self._set_headers()
                response = {
                    'ticker': ticker,
                    'analysis': analysis_result,
                    'status': 'success',
                    'timestamp': datetime.datetime.now().isoformat(),
                    'provider': '📊 المحلل الآلي لأسهم البورصة المصرية'
                }
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
            except Exception as e:
                logger.error(f"Error analyzing stock {ticker}: {str(e)}", exc_info=True)
                self._set_headers(status_code=500)
                response = {'error': f'Analysis failed: {str(e)}'}
                self.wfile.write(json.dumps(response).encode())
        
        except Exception as e:
            logger.error(f"Error processing stock analysis: {str(e)}", exc_info=True)
            self._set_headers(status_code=500)
            response = {'error': f'Processing error: {str(e)}'}
            self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def is_port_in_use(port):
    """Check if the port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def start_server(host='0.0.0.0', port=8000):
    """Start the HTTP server"""
    # Check if port is in use and find an available one if needed
    original_port = port
    while is_port_in_use(port):
        logger.warning(f"Port {port} is already in use, trying port {port+1}")
        port += 1
    
    if port != original_port:
        logger.info(f"Selected alternative port {port}")
        
    server_address = (host, port)
    try:
        httpd = HTTPServer(server_address, StockAnalyzerHandler)
        logger.info(f"Starting server on http://{host}:{port}")
        print(f"API server running at: http://{host}:{port}")
        httpd.serve_forever()
    except OSError as e:
        logger.error(f"Failed to start server: {e}")
        print(f"Failed to start API server: {e}", file=sys.stderr)
        # Try one more port if first attempt fails
        if port == original_port:
            logger.info(f"Attempting to start on port {port+1}")
            start_server(host, port+1)
    except KeyboardInterrupt:
        logger.info("Stopping server...")
        httpd.server_close()
    except Exception as e:
        logger.error(f"Server error: {e}", exc_info=True)
        print(f"Server error: {e}", file=sys.stderr)

# Add this helper function to calculate percentages and risk metrics
def calculate_metrics(buy_price, targets, stop_loss):
    """Calculate percentage gains and risk metrics for targets"""
    buy_price = float(buy_price)
    target1 = float(targets[0])
    target2 = float(targets[1])
    target3 = float(targets[2])
    sl = float(stop_loss)
    
    # Calculate percentage gains for each target
    t1_pct = round((target1 - buy_price) / buy_price * 100, 2)
    t2_pct = round((target2 - buy_price) / buy_price * 100, 2)
    t3_pct = round((target3 - buy_price) / buy_price * 100, 2)
    sl_pct = round((sl - buy_price) / buy_price * 100, 2)
    
    # Calculate risk-reward ratios
    rr_ratio1 = round(abs(t1_pct / sl_pct), 2) if sl_pct != 0 else "∞"
    rr_ratio2 = round(abs(t2_pct / sl_pct), 2) if sl_pct != 0 else "∞"
    rr_ratio3 = round(abs(t3_pct / sl_pct), 2) if sl_pct != 0 else "∞"
    
    # Determine risk level based on risk-reward ratio
    if isinstance(rr_ratio1, str) or rr_ratio1 > 2:
        risk_level = "منخفضة ✅"
        risk_emoji = "🟢"
    elif rr_ratio1 > 1:
        risk_level = "متوسطة ⚠️"
        risk_emoji = "🟡"
    else:
        risk_level = "عالية 🚨"
        risk_emoji = "🔴"
    
    # Estimate time to achieve targets (very rough estimate)
    # Higher percentages generally take longer
    t1_days = max(5, int(t1_pct * 1.5))  # Rough estimate: 1.5 days per 1% gain
    t2_days = max(10, int(t2_pct * 1.5))
    t3_days = max(15, int(t3_pct * 1.5))
    
    return {
        "t1_pct": t1_pct,
        "t2_pct": t2_pct,
        "t3_pct": t3_pct,
        "sl_pct": abs(sl_pct),
        "risk_level": risk_level,
        "risk_emoji": risk_emoji,
        "rr_ratio1": rr_ratio1,
        "rr_ratio2": rr_ratio2,
        "rr_ratio3": rr_ratio3,
        "t1_days": t1_days,
        "t2_days": t2_days,
        "t3_days": t3_days
    }

def calculate_days_between(start_date_str, end_date_str):
    """Calculate days between two dates in YYYY-MM-DD format"""
    from datetime import datetime
    try:
        start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
        end_date = datetime.strptime(end_date_str, "%Y-%m-%d")
        return (end_date - start_date).days
    except Exception as e:
        logger.error(f"Error calculating days between dates: {e}")
        return "غير معروف"

async def remove_blocked_user(user_id, sheet, reason="Blocked the bot"):
    """Remove a user who has blocked the bot from the database"""
    try:
        # Find the user in the sheet
        user_cell = sheet.find(str(user_id))
        if user_cell:
            # Delete the entire row
            sheet.delete_rows(user_cell.row)
            logger.info(f"Removed user {user_id} from database. Reason: {reason}")
            return True
        else:
            logger.warning(f"User {user_id} not found in sheet for removal")
            return False
    except Exception as e:
        logger.error(f"Error removing blocked user {user_id}: {str(e)}")
        return False

def check_open_position(sheet, stock_code):
    """
    Check if there's already an open position for the given stock code.
    Returns True if there's an open position, False otherwise.
    """
    try:
        # Find all occurrences of the stock code in Sheet4
        all_data = sheet.get_all_values()
        
        for row_index, row in enumerate(all_data, start=1):
            if len(row) > 0 and str(row[0]).upper() == str(stock_code).upper():
                # Check if this is an open position
                # Column 3 (index 2) contains the report status
                if len(row) > 2:
                    report_status = str(row[2]).lower().strip()
                    
                    # Consider position open if:
                    # - Status is 'buy' (initial buy signal)
                    # - Status is 't1done', 't2done', or 't3done' (targets achieved but position still open)
                    # Consider position closed if:
                    # - Status is 'tsl' (stop loss triggered)
                    # - Status is 'sell' (sell signal executed)
                    
                    open_statuses = ['buy', 't1done', 't2done', 't3done']
                    closed_statuses = ['tsl', 'sell']
                    
                    if report_status in open_statuses:
                        # Double check - get more details
                        buy_date = str(row[4]) if len(row) > 4 else "غير معروف"
                        buy_price = str(row[3]) if len(row) > 3 else "غير معروف"
                        
                        logger.info(f"Found open position for {stock_code} at row {row_index} - Status: {report_status}, Buy Date: {buy_date}, Buy Price: {buy_price}")
                        return True, row_index, report_status
                    elif report_status in closed_statuses:
                        logger.info(f"Found closed position for {stock_code} at row {row_index} with status: {report_status}")
                        # Continue checking other rows in case there are multiple entries
                        continue
                    else:
                        logger.warning(f"Unknown status '{report_status}' for {stock_code} at row {row_index}")
                        continue
        
        logger.info(f"No open position found for {stock_code}")
        return False, None, None
        
    except Exception as e:
        logger.error(f"Error checking open position for {stock_code}: {str(e)}")
        return False, None, None

async def send_admin_notification(message, admin_ids):
    """Send notification message to all admin users"""
    bot = Bot(bot_token)
    failed_admins = []
    
    for admin_id in admin_ids:
        try:
            await bot.send_message(chat_id=admin_id, text=message, parse_mode="MARKDOWN")
            logger.info(f"Admin notification sent to {admin_id}")
        except Exception as e:
            logger.error(f"Failed to send admin notification to {admin_id}: {e}")
            failed_admins.append(admin_id)
    
    return failed_admins

def check_achieved_targets(sheet, stock_code):
    """
    Check which targets have been achieved for a stock
    Returns a tuple: (has_achieved_targets, achieved_targets_list, target_details)
    """
    try:
        # Find the stock in the sheet
        all_data = sheet.get_all_values()
        
        for row_index, row in enumerate(all_data, start=1):
            if len(row) > 0 and str(row[0]).upper() == str(stock_code).upper():
                # Check the current status to see what targets have been achieved
                if len(row) > 2:
                    report_status = str(row[2]).lower().strip()
                    
                    achieved_targets = []
                    target_details = {}
                    
                    # Check if any targets have been achieved based on status
                    if report_status in ['t1done', 't2done', 't3done']:
                        if report_status == 't1done':
                            achieved_targets = ['t1']
                        elif report_status == 't2done':
                            achieved_targets = ['t1', 't2']
                        elif report_status == 't3done':
                            achieved_targets = ['t1', 't2', 't3']
                    
                    # Also check if there are achievement dates in the sheet
                    # Columns: [code, name, status, buy_price, buy_date, sell_price, sell_date, tp1, tp2, tp3, sl, t1_date, t2_date, t3_date, tsl_date]
                    if len(row) > 11:  # Check if t1_date exists (column 12)
                        t1_date = str(row[11]).strip() if len(row) > 11 else ""
                        t2_date = str(row[12]).strip() if len(row) > 12 else ""
                        t3_date = str(row[13]).strip() if len(row) > 13 else ""
                        
                        achieved_by_dates = []
                        if t1_date and t1_date != "":
                            achieved_by_dates.append('t1')
                            target_details['t1_date'] = t1_date
                        if t2_date and t2_date != "":
                            achieved_by_dates.append('t2')
                            target_details['t2_date'] = t2_date
                        if t3_date and t3_date != "":
                            achieved_by_dates.append('t3')
                            target_details['t3_date'] = t3_date
                        
                        # Use the more comprehensive list
                        if len(achieved_by_dates) > len(achieved_targets):
                            achieved_targets = achieved_by_dates
                    
                    # Get buy price and target prices for calculation
                    if len(row) > 3:
                        target_details['buy_price'] = str(row[3]) if len(row) > 3 else "0"
                        target_details['tp1'] = str(row[7]) if len(row) > 7 else "0"
                        target_details['tp2'] = str(row[8]) if len(row) > 8 else "0"
                        target_details['tp3'] = str(row[9]) if len(row) > 9 else "0"
                        target_details['buy_date'] = str(row[4]) if len(row) > 4 else ""
                    
                    has_achieved = len(achieved_targets) > 0
                    
                    logger.info(f"Stock {stock_code} - Achieved targets: {achieved_targets}, Status: {report_status}")
                    return has_achieved, achieved_targets, target_details
        
        logger.info(f"Stock {stock_code} not found or no targets achieved")
        return False, [], {}
        
    except Exception as e:
        logger.error(f"Error checking achieved targets for {stock_code}: {str(e)}")
        return False, [], {}

# Enhance the broadcast message templates with better formatting and emojis
async def alertMessage(broadcast_message, signal_type=None, stock_data=None):
  """
  Send broadcast messages to users based on their subscription type.
  Automatically removes users who have blocked the bot.
  """
  bot = Bot(bot_token)
  scope = [
    "https://spreadsheets.google.com/feeds",
    'https://www.googleapis.com/auth/spreadsheets',
    "https://www.googleapis.com/auth/drive.file",
    "https://www.googleapis.com/auth/drive"
  ]
  with open('json_file.json') as json_file:
    json_data = json.load(json_file)
    credentials = ServiceAccountCredentials.from_json_keyfile_dict(
      json_data, scope)
    client = gspread.authorize(credentials)
    sheet_name = "stock"
    sheet = client.open(sheet_name).worksheet("users")
    
    blocked_users = []  # Track users who have blocked the bot
    
    for row in sheet.get_all_values():
      chat_id = row[0]
      payment_status = row[2]
      if payment_status == "paid" or payment_status == "trail":
        try:
          print(f'Sending message to {chat_id}')
          await bot.send_message(chat_id,
                                 broadcast_message,
                                 parse_mode="MARKDOWN")
        except Exception as e:
          error_str = str(e).lower()
          print(f"[❌] Telegram Error while sending message to {chat_id}:\n>", e)
          
          # Check if the user blocked the bot
          if "forbidden: bot was blocked by the user" in error_str or "chat not found" in error_str:
            blocked_users.append(chat_id)
            logger.warning(f"User {chat_id} has blocked the bot. Will be removed from database.")
    
    # Remove users who blocked the bot
    for user_id in blocked_users:
      await remove_blocked_user(user_id, sheet, reason="Blocked the bot")


@app.route("/webhook", methods=["POST"])
async def handle_alert():
  try:
    jsonRequest = request.args.get("jsonRequest")
    if request.method == 'POST':
      payload = request.data
      print(payload)
      if jsonRequest == "true":
        jsonPayload = request.json
        alert_message = json.loads(payload)
      else:
        alert_message = json.loads(request.data)
        stock_report = alert_message["report"]
        stock_code = alert_message["stock_code"]
        scope = [
          "https://spreadsheets.google.com/feeds",
          'https://www.googleapis.com/auth/spreadsheets',
          "https://www.googleapis.com/auth/drive.file",
          "https://www.googleapis.com/auth/drive"
        ]
        with open('json_file.json') as json_file:
          json_data = json.load(json_file)
          credentials = ServiceAccountCredentials.from_json_keyfile_dict(
            json_data, scope)
          client = gspread.authorize(credentials)
          sheet_name = "stock"
          sheet = client.open(sheet_name).worksheet("Sheet4")
          sheet2 = client.open(sheet_name).worksheet("Sheet1")
          
          # Find the stock code in Sheet4
          cell = sheet.find(str(stock_code)) if sheet else None
          
          # Find the stock code in Sheet1 and handle if not found
          cell2 = sheet2.find(str(stock_code)) if sheet2 else None
          
          # Get stock name with error handling
          if cell2:
              stock_name = sheet2.cell(cell2.row, 2).value
          else:
              # If stock not found in Sheet1, use a default name or the code itself
              stock_name = f"Unknown ({stock_code})"
              logger.warning(f"Stock code {stock_code} not found in Sheet1, using default name")
          
          # If the value is found in Sheet4, update the corresponding cells
          if cell:
            # Process existing stock code
            if stock_report == "buy":
              # First, check if there's already an open position for this stock
              has_open_position, existing_row, existing_status = check_open_position(sheet, stock_code)
              
              if has_open_position:
                # Check for price enhancement opportunity
                enhancement_check = check_price_enhancement_opportunity(sheet, stock_code, alert_message["buy_price"])
                
                if enhancement_check['can_enhance']:
                  # تعزيز المراكز - السعر الجديد أفضل
                  success = update_position_with_enhancement(sheet, existing_row, alert_message, enhancement_check['improvement_pct'])
                  
                  if success:
                    # فحص ما إذا كانت فرصة التعزيز ذهبية
                    enhancement_metrics = calculate_metrics(
                        enhancement_check['new_price'],
                        [alert_message["tp1"], alert_message["tp2"], alert_message["tp3"]],
                        alert_message["sl"]
                    )
                    
                    # تحليل المخاطر للتعزيز
                    risk_analysis = analyze_technical_risk(stock_code)
                    
                    # فحص التعزيز الذهبي
                    golden_criteria = identify_golden_opportunity(stock_code, alert_message, risk_analysis, enhancement_metrics)
                    
                    if golden_criteria['is_golden'] and enhancement_check['improvement_pct'] >= 5:
                        # إنشاء رسالة تعزيز ذهبية مخصصة
                        golden_enhancement_message = f"""🔥💎 *تعزيز مراكز ذهبي استثنائي* 💎🔥

🚨🚨 *فرصة تعزيز نادرة* 🚨🚨

🔹 *السهم:* {stock_name} ({stock_code})
📊 *السعر السابق:* {enhancement_check['current_price']} جنيه
💰 *السعر المحسن:* {enhancement_check['new_price']} جنيه
📈 *تحسن استثنائي:* {enhancement_check['improvement_pct']}%

🎯 *الأهداف المحدثة:*
⚡ الهدف الأول: {alert_message["tp1"]} (ربح {enhancement_metrics['t1_pct']}%)
⚡ الهدف الثاني: {alert_message["tp2"]} (ربح {enhancement_metrics['t2_pct']}%)
⚡ الهدف الثالث: {alert_message["tp3"]} (ربح {enhancement_metrics['t3_pct']}%)

🛡️ *وقف الخسارة:* {alert_message["sl"]} (خسارة {enhancement_metrics['sl_pct']}%)

💎 *مميزات التعزيز الذهبي:*
{chr(10).join([f"🌟 {factor}" for factor in golden_criteria['factors']])}

📊 *نقاط القوة:* {golden_criteria['score']}/100
📈 *نسبة المخاطرة/العائد المحسنة:* 1:{enhancement_metrics['rr_ratio1']}

💡 *فرصة استثنائية لمضاعفة الأرباح بمخاطرة أقل!*

⏰ *تاريخ التعزيز الذهبي:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية
🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥"""
                        await alertMessage(golden_enhancement_message)
                    else:
                        # إنشاء رسالة تعزيز عادية
                        enhancement_message = create_enhancement_message(
                          stock_name, stock_code,
                          enhancement_check['current_price'],
                          enhancement_check['new_price'],
                          enhancement_check['improvement_pct'],
                          [alert_message["tp1"], alert_message["tp2"], alert_message["tp3"]],
                          alert_message["sl"]
                        )
                        
                        # إرسال رسالة التعزيز للأعضاء
                        await alertMessage(enhancement_message)
                    
                    # إرسال تنبيه للإدارة
                    enhancement_type = "ذهبي" if golden_criteria['is_golden'] and enhancement_check['improvement_pct'] >= 5 else "عادي"
                    admin_message = f"""✅ *تم تعزيز المراكز بنجاح* ✅

🔹 *السهم:* {stock_name} ({stock_code})
📈 *تحسن السعر:* {enhancement_check['improvement_pct']}%
💰 *السعر الجديد:* {enhancement_check['new_price']}
📍 *الصف:* {existing_row}
🏆 *نوع التعزيز:* {enhancement_type}

⏰ *وقت التعزيز:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                    
                    await send_admin_notification(admin_message, ADMIN_IDS)
                    logger.info(f"Position enhanced for {stock_code} with {enhancement_check['improvement_pct']}% improvement")
                  
                else:
                  # Send notification to admins only - don't process the duplicate signal
                  admin_message = f"""⚠️ *تنبيه: إشارة شراء مكررة* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
🔄 *الحالة الحالية:* {existing_status}
💰 *سعر الإشارة الجديدة:* {alert_message["buy_price"]}
📍 *الصف الموجود:* {existing_row}

❌ تم تجاهل الإشارة المكررة - يوجد صفقة مفتوحة بالفعل

⏰ *وقت الإشارة المكررة:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                  
                  await send_admin_notification(admin_message, ADMIN_IDS)
                  logger.warning(f"Duplicate buy signal ignored for {stock_code} - open position exists at row {existing_row}")
                
              else:
                # No open position, proceed with normal buy processing
                # أولاً: تحليل المؤشرات الفنية للمخاطر
                risk_analysis = analyze_technical_risk(stock_code)
                
                # تحديث بيانات السهم
                sheet.update_cell(cell.row, 1, alert_message["stock_code"])
                sheet.update_cell(cell.row, 3, alert_message["report"])
                sheet.update_cell(cell.row, 4, alert_message["buy_price"])
                sheet.update_cell(cell.row, 5, datetime.date.today().strftime("%Y-%m-%d"))
                sheet.update_cell(cell.row, 8, alert_message["tp1"])
                sheet.update_cell(cell.row, 9, alert_message["tp2"])
                sheet.update_cell(cell.row, 10, alert_message["tp3"])
                sheet.update_cell(cell.row, 11, alert_message["sl"])
                
                # حفظ معلومات المخاطرة في الأعمدة الجديدة (إذا كانت متاحة)
                try:
                  sheet.update_cell(cell.row, 18, risk_analysis['risk_level'])  # مستوى المخاطرة
                  if risk_analysis['risk_factors']:
                    sheet.update_cell(cell.row, 19, ' | '.join(risk_analysis['risk_factors']))  # عوامل المخاطرة
                except Exception as e:
                  logger.debug(f"Could not update risk columns: {e}")
                
                # Calculate metrics
                metrics = calculate_metrics(
                    alert_message["buy_price"], 
                    [alert_message["tp1"], alert_message["tp2"], alert_message["tp3"]], 
                    alert_message["sl"]
                )
                
                # فحص الفرصة الذهبية
                golden_criteria = identify_golden_opportunity(stock_code, alert_message, risk_analysis, metrics)
                
                if golden_criteria['is_golden']:
                    # إرسال تنبيه فوري للفرصة الذهبية
                    await send_golden_opportunity_alert(stock_name, stock_code, alert_message, metrics, golden_criteria)
                else:
                    # إنشاء الرسالة المناسبة حسب مستوى المخاطرة
                    if risk_analysis['risk_level'] in ['عالي جداً', 'عالي', 'متوسط'] and risk_analysis['risk_factors']:
                      broadcast_message = create_risk_warning_message(
                        stock_name, stock_code, alert_message["buy_price"],
                        [alert_message["tp1"], alert_message["tp2"], alert_message["tp3"]],
                        alert_message["sl"], risk_analysis, metrics
                      )
                    else:
                      broadcast_message = f"""🔔 *توصية جديدة* 🔔

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر الشراء:* {alert_message["buy_price"]}

🎯 *الأهداف:*
⭐️ الهدف الأول: {alert_message["tp1"]} (ربح {metrics['t1_pct']}%)
⭐️ الهدف الثاني: {alert_message["tp2"]} (ربح {metrics['t2_pct']}%)
⭐️ الهدف الثالث: {alert_message["tp3"]} (ربح {metrics['t3_pct']}%)

🛑 *وقف الخسارة:* {alert_message["sl"]} (خسارة {metrics['sl_pct']}%)

⏱ *الوقت المتوقع للتحقيق:*
⭐️ الهدف الأول: ~{metrics['t1_days']} يوم
⭐️ الهدف الثاني: ~{metrics['t2_days']} يوم
⭐️ الهدف الثالث: ~{metrics['t3_days']} يوم

🔍 *تقييم المخاطرة:* {risk_analysis['risk_emoji']} {risk_analysis['risk_level']}
📊 *نسبة المخاطرة/العائد:* 1:{metrics['rr_ratio1']} (للهدف الأول)

⏰ *تاريخ التوصية:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                    
                    await alertMessage(broadcast_message)
                
            elif stock_report == "t1done":
              sheet.update_cell(cell.row, 3, alert_message["report"])
              sheet.update_cell(cell.row, 11, alert_message["sl"])
              sheet.update_cell(cell.row, 12,
                                datetime.date.today().strftime("%Y-%m-%d"))
              
              # Get the original buy date and price
              buy_date = sheet.cell(cell.row, 5).value
              buy_price = float(sheet.cell(cell.row, 4).value)
              tp1_price = float(alert_message["tp1"]) if "tp1" in alert_message else float(sheet.cell(cell.row, 8).value)
              
              # Calculate profit percentage and days to achieve
              profit_pct = round((tp1_price - buy_price) / buy_price * 100, 2)
              days_taken = calculate_days_between(buy_date, datetime.date.today().strftime("%Y-%m-%d"))
              
              broadcast_message = f"""✅ *تحقق الهدف الأول* ✅

🔹 *السهم:* {stock_name} ({stock_code})
🎯 *الهدف المتحقق:* الهدف الأول بسعر {tp1_price}
💰 *سعر الشراء:* {buy_price}
📈 *نسبة الربح المحققة:* {profit_pct}%
⏱ *المدة المستغرقة:* {days_taken} يوم

📆 *تم تحقيق الهدف بتاريخ:* {datetime.date.today().strftime("%Y-%m-%d")}

🔴 تم رفع مستوى وقف الخسارة إلى {alert_message["sl"]} لحماية الأرباح

📊 المحلل الآلي لأسهم البورصة المصرية"""
              await alertMessage(broadcast_message)
              
            elif stock_report == "t2done":
              sheet.update_cell(cell.row, 3, alert_message["report"])
              sheet.update_cell(cell.row, 11, alert_message["sl"])
              sheet.update_cell(cell.row, 13,
                                datetime.date.today().strftime("%Y-%m-%d"))
              
              # Get the original buy date and price
              buy_date = sheet.cell(cell.row, 5).value
              buy_price = float(sheet.cell(cell.row, 4).value)
              tp2_price = float(alert_message["tp2"]) if "tp2" in alert_message else float(sheet.cell(cell.row, 9).value)
              
              # Calculate profit percentage and days to achieve
              profit_pct = round((tp2_price - buy_price) / buy_price * 100, 2)
              days_taken = calculate_days_between(buy_date, datetime.date.today().strftime("%Y-%m-%d"))
              
              broadcast_message = f"""✅✅ *تحقق الهدف الثاني* ✅✅

🔹 *السهم:* {stock_name} ({stock_code})
🎯 *الهدف المتحقق:* الهدف الثاني بسعر {tp2_price}
💰 *سعر الشراء:* {buy_price}
📈 *نسبة الربح المحققة:* {profit_pct}%
⏱ *المدة المستغرقة:* {days_taken} يوم

📆 *تم تحقيق الهدف بتاريخ:* {datetime.date.today().strftime("%Y-%m-%d")}

🔴 تم رفع مستوى وقف الخسارة إلى {alert_message["sl"]} لحماية الأرباح

📊 المحلل الآلي لأسهم البورصة المصرية"""
              await alertMessage(broadcast_message)
              
            elif stock_report == "t3done":
              sheet.update_cell(cell.row, 3, alert_message["report"])
              sheet.update_cell(cell.row, 11, alert_message["sl"])
              sheet.update_cell(cell.row, 14,
                                datetime.date.today().strftime("%Y-%m-%d"))
              
              # Get the original buy date and price
              buy_date = sheet.cell(cell.row, 5).value
              buy_price = float(sheet.cell(cell.row, 4).value)
              tp3_price = float(alert_message["tp3"]) if "tp3" in alert_message else float(sheet.cell(cell.row, 10).value)
              
              # Calculate profit percentage and days to achieve
              profit_pct = round((tp3_price - buy_price) / buy_price * 100, 2)
              days_taken = calculate_days_between(buy_date, datetime.date.today().strftime("%Y-%m-%d"))
              
              broadcast_message = f"""🏆 *تحقق الهدف الثالث* 🏆

🔹 *السهم:* {stock_name} ({stock_code})
🎯 *الهدف المتحقق:* الهدف الثالث (الأخير) بسعر {tp3_price}
💰 *سعر الشراء:* {buy_price}
📈 *نسبة الربح المحققة:* {profit_pct}%
⏱ *المدة المستغرقة:* {days_taken} يوم

📆 *تم تحقيق الهدف بتاريخ:* {datetime.date.today().strftime("%Y-%m-%d")}

🎉 *تهانينا!* تم تحقيق جميع الأهداف بنجاح

📊 المحلل الآلي لأسهم البورصة المصرية"""
              await alertMessage(broadcast_message)
              
            elif stock_report == "tsl":
              # First, check if there's an open position for this stock
              has_open_position, existing_row, existing_status = check_open_position(sheet, stock_code)
              
              if not has_open_position:
                # No open position found - ignore the signal and notify admins
                admin_message = f"""⚠️ *تنبيه: إشارة وقف خسارة على سهم بدون صفقة مفتوحة* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
🛑 *سعر وقف الخسارة:* {alert_message["sl"]}
❌ *السبب:* لا توجد صفقة مفتوحة للسهم

❌ تم تجاهل الإشارة - لا توجد صفقة مفتوحة

⏰ *وقت الإشارة المتجاهلة:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                await send_admin_notification(admin_message, ADMIN_IDS)
                logger.warning(f"TSL signal ignored for {stock_code} - no open position found")
                
              else:
                # Open position exists, proceed with normal processing
                sheet.update_cell(cell.row, 3, alert_message["report"])
                sheet.update_cell(cell.row, 15,
                                  datetime.date.today().strftime("%Y-%m-%d"))
                
                # Check if any targets have been achieved to determine message type
                has_achieved_targets, achieved_targets, target_details = check_achieved_targets(sheet, stock_code)
                
                if has_achieved_targets:
                  # حساب الربح الفعلي المحقق في حالة وقف الخسارة مع أهداف محققة
                  try:
                    buy_price = float(sheet.cell(cell.row, 4).value)
                    sl_price = float(alert_message["sl"])
                    
                    # الحصول على تاريخ وسعر آخر هدف محقق
                    last_target_date = get_last_achieved_target_date(sheet, cell.row, achieved_targets)
                    last_target_price = get_last_achieved_target_price(sheet, cell.row, achieved_targets)
                    
                    # حساب الربح الفعلي
                    actual_profit_data = calculate_actual_profit_with_highest_price(
                      buy_price,
                      last_target_date,
                      datetime.date.today().strftime("%Y-%m-%d"),
                      stock_code,
                      sl_price,
                      last_target_price
                    )
                    
                    if actual_profit_data:
                      best_exit_price = actual_profit_data['best_exit_price']
                      actual_profit_pct = actual_profit_data['actual_profit_pct']
                      calculation_note = actual_profit_data['calculation_note']
                      
                      # حفظ المعلومات في الشيت
                      try:
                        sheet.update_cell(cell.row, 20, actual_profit_data['highest_price_achieved'])
                        sheet.update_cell(cell.row, 21, actual_profit_pct)
                      except:
                        pass
                      
                      profit_details = f"""
💰 *الربح الفعلي المحقق:* {actual_profit_pct}%
🎯 *أفضل سعر محقق:* {best_exit_price} جنيه
📊 *{calculation_note}*"""
                    else:
                      profit_details = ""
                  except Exception as e:
                    logger.error(f"Error calculating actual profit for TSL: {e}")
                    profit_details = ""
                  
                  broadcast_message = f"""🛡️ *حماية الأرباح* 🛡️

🔹 *السهم:* {stock_name} ({stock_code})
🛑 *سعر وقف الخسارة:* {alert_message["sl"]}
⏰ *تاريخ التفعيل:* {datetime.date.today().strftime("%Y-%m-%d")}

✅ *الأهداف المحققة سابقاً:* {len(achieved_targets)} من 3{profit_details}

💡 *تم تفعيل وقف الخسارة لحماية الأرباح المحققة*

📊 المحلل الآلي لأسهم البورصة المصرية"""
                else:
                  broadcast_message = f"""⚠️ *تفعيل وقف الخسارة* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
🛑 *سعر وقف الخسارة:* {alert_message["sl"]}
⏰ *تاريخ التفعيل:* {datetime.date.today().strftime("%Y-%m-%d")}

💡 *ننصح بمراقبة السهم لاحتمال ارتداده أو الخروج وتقليل الخسائر*

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                await alertMessage(broadcast_message)
                
            elif stock_report == "sell":
              # First, check if there's an open position for this stock
              has_open_position, existing_row, existing_status = check_open_position(sheet, stock_code)
              
              if not has_open_position:
                # No open position found - ignore the signal and notify admins
                admin_message = f"""⚠️ *تنبيه: إشارة بيع على سهم بدون صفقة مفتوحة* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر البيع المقترح:* {alert_message["sell_price"]}
❌ *السبب:* لا توجد صفقة مفتوحة للسهم

❌ تم تجاهل الإشارة - لا توجد صفقة مفتوحة

⏰ *وقت الإشارة المتجاهلة:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                await send_admin_notification(admin_message, ADMIN_IDS)
                logger.warning(f"Sell signal ignored for {stock_code} - no open position found")
                
              else:
                # تحديث البيانات الأساسية للبيع
                sheet.update_cell(cell.row, 3, alert_message["report"])
                sheet.update_cell(cell.row, 6, alert_message["sell_price"])
                sheet.update_cell(cell.row, 7, datetime.date.today().strftime("%Y-%m-%d"))
                sheet.update_cell(cell.row, 15, "✓")  # إغلاق الصفقة
                
                # استخراج بيانات الصفقة
                row_data = sheet.row_values(cell.row)
                buy_price = float(row_data[3]) if row_data[3] else 0
                sell_price = float(alert_message["sell_price"])
                buy_date = row_data[4] if row_data[4] else None
                
                # تحديد الأهداف المحققة
                tp1_achieved = row_data[12] == "✓" if len(row_data) > 12 else False
                tp2_achieved = row_data[13] == "✓" if len(row_data) > 13 else False
                tp3_achieved = row_data[14] == "✓" if len(row_data) > 14 else False
                
                if buy_price > 0:
                  # حساب الربح الأساسي
                  basic_profit_pct = round(((sell_price - buy_price) / buy_price) * 100, 2)
                  
                  # إذا تم تحقيق أي هدف، احسب الربح الفعلي باستخدام أعلى سعر أو آخر هدف محقق
                  if (tp1_achieved or tp2_achieved or tp3_achieved) and buy_date:
                    try:
                      # تحديد الأهداف المحققة
                      achieved_targets = []
                      if tp1_achieved:
                        achieved_targets.append('t1')
                      if tp2_achieved:
                        achieved_targets.append('t2')
                      if tp3_achieved:
                        achieved_targets.append('t3')
                      
                      # الحصول على تاريخ وسعر آخر هدف محقق
                      last_target_date = get_last_achieved_target_date(sheet, cell.row, achieved_targets)
                      last_target_price = get_last_achieved_target_price(sheet, cell.row, achieved_targets)
                      
                      if last_target_date:
                        # حساب الربح الفعلي باستخدام أعلى سعر أو آخر هدف محقق
                        actual_profit_data = calculate_actual_profit_with_highest_price(
                          buy_price, 
                          last_target_date, 
                          datetime.date.today().strftime("%Y-%m-%d"),
                          stock_code, 
                          sell_price,
                          last_target_price
                        )
                        
                        if actual_profit_data:
                          best_exit_price = actual_profit_data['best_exit_price']
                          highest_price_achieved = actual_profit_data['highest_price_achieved']
                          actual_profit_pct = actual_profit_data['actual_profit_pct']
                          calculation_note = actual_profit_data['calculation_note']
                          
                          # حفظ المعلومات الإضافية في الأعمدة الجديدة
                          try:
                            sheet.update_cell(cell.row, 20, highest_price_achieved)  # أعلى سعر محقق
                            sheet.update_cell(cell.row, 21, actual_profit_pct)  # نسبة الربح الفعلية
                          except Exception as e:
                            logger.debug(f"Could not update profit columns: {e}")
                          
                          profit_pct = actual_profit_pct
                          profit_message = f"""💰 *الربح الفعلي المحقق:* {profit_pct}%
🎯 *أفضل سعر خروج:* {best_exit_price} جنيه
� *طريقة الحساب:* {calculation_note}"""
                        else:
                          profit_pct = basic_profit_pct
                          profit_message = f"💰 *الربح/الخسارة:* {profit_pct}%"
                      else:
                        profit_pct = basic_profit_pct  
                        profit_message = f"💰 *الربح/الخسارة:* {profit_pct}%"
                    except Exception as e:
                      logger.error(f"Error calculating actual profit for {stock_code}: {e}")
                      profit_pct = basic_profit_pct
                      profit_message = f"💰 *الربح/الخسارة:* {profit_pct}%"
                  else:
                    profit_pct = basic_profit_pct
                    profit_message = f"💰 *الربح/الخسارة:* {profit_pct}%"
                  
                  # إعداد الرموز التعبيرية والحالة
                  if profit_pct > 0:
                    profit_emoji = "📈"
                    status_emoji = "✅"
                    result_text = "ربح"
                  elif profit_pct == 0:
                    profit_emoji = "➡️"
                    status_emoji = "⚪"
                    result_text = "تعادل"
                  else:
                    profit_emoji = "📉"
                    status_emoji = "❌"
                    result_text = "خسارة"
                  
                  # تحديد الأهداف المحققة للعرض
                  achieved_targets_count = sum([tp1_achieved, tp2_achieved, tp3_achieved])
                  targets_info = f"✅ *الأهداف المحققة:* {achieved_targets_count} من 3" if achieved_targets_count > 0 else ""
                  
                  # رسالة البيع
                  broadcast_message = f"""{status_emoji} *إغلاق صفقة - بيع* {status_emoji}

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر الشراء:* {buy_price}
💱 *سعر البيع:* {sell_price}

{profit_message}

{profit_emoji} *النتيجة:* {result_text}

{targets_info}

⏰ *تاريخ الإغلاق:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                else:
                  broadcast_message = f"""⚠️ *خطأ في حساب الربح/الخسارة* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
� *سعر البيع:* {sell_price}

❌ لم يتم العثور على سعر الشراء الصحيح

⏰ *تاريخ الإغلاق:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                await alertMessage(broadcast_message)
          
          else:
            # Handle new stock code (not found in Sheet4)
            if stock_report == "buy":
              # First, check if there's already an open position for this stock anywhere in the sheet
              has_open_position, existing_row, existing_status = check_open_position(sheet, stock_code)
              
              if has_open_position:
                # Send notification to admins only - don't process the duplicate signal
                admin_message = f"""⚠️ *تنبيه: إشارة مكررة على سهم جديد* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
🔄 *الحالة الحالية:* {existing_status}
💰 *سعر الإشارة الجديدة:* {alert_message["buy_price"]}
📍 *الصف الموجود:* {existing_row}

❌ تم تجاهل الإشارة المكررة - يوجد صفقة مفتوحة بالفعل

⏰ *وقت الإشارة المكررة:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                
                await send_admin_notification(admin_message, ADMIN_IDS)
                logger.warning(f"Duplicate buy signal ignored for new stock {stock_code} - open position exists at row {existing_row}")
                
              else:
                # No open position, proceed with processing the new stock
                if cell2:
                  # Stock exists in Sheet1, proceed normally
                  sheet.append_row([
                    stock_code, '', alert_message["report"],
                    alert_message["buy_price"],
                    datetime.date.today().strftime("%Y-%m-%d"), '', '',
                    alert_message["tp1"], alert_message["tp2"],
                    alert_message["tp3"], alert_message["sl"]
                  ])
                  
                  # Calculate metrics
                  metrics = calculate_metrics(
                      alert_message["buy_price"], 
                      [alert_message["tp1"], alert_message["tp2"], alert_message["tp3"]], 
                      alert_message["sl"]
                  )
                  
                  # تحليل المخاطر للسهم الجديد
                  risk_analysis = analyze_technical_risk(stock_code)
                  
                  # فحص الفرصة الذهبية للسهم الجديد
                  golden_criteria = identify_golden_opportunity(stock_code, alert_message, risk_analysis, metrics)
                  
                  if golden_criteria['is_golden']:
                      # إرسال تنبيه فوري للفرصة الذهبية
                      await send_golden_opportunity_alert(stock_name, stock_code, alert_message, metrics, golden_criteria)
                  else:
                      broadcast_message = f"""🆕 *توصية جديدة* 🔔

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر الشراء:* {alert_message["buy_price"]}

🎯 *الأهداف:*
⭐️ الهدف الأول: {alert_message["tp1"]} (ربح {metrics['t1_pct']}%)
⭐️ الهدف الثاني: {alert_message["tp2"]} (ربح {metrics['t2_pct']}%)
⭐️ الهدف الثالث: {alert_message["tp3"]} (ربح {metrics['t3_pct']}%)

🛑 *وقف الخسارة:* {alert_message["sl"]} (خسارة {metrics['sl_pct']}%)

⏱ *الوقت المتوقع للتحقيق:*
⭐️ الهدف الأول: ~{metrics['t1_days']} يوم
⭐️ الهدف الثاني: ~{metrics['t2_days']} يوم
⭐️ الهدف الثالث: ~{metrics['t3_days']} يوم

🔍 *تقييم المخاطرة:* {risk_analysis['risk_emoji']} {risk_analysis['risk_level']}
📊 *نسبة المخاطرة/العائد:* 1:{metrics['rr_ratio1']} (للهدف الأول)

⏰ *تاريخ التوصية:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
                      await alertMessage(broadcast_message)
                else:
                  # Stock doesn't exist in Sheet1 - add to Sheet4 but don't broadcast
                  logger.warning(f"Adding new stock {stock_code} to Sheet4, but not broadcasting (not in Sheet1)")
                  sheet.append_row([
                    stock_code, '', alert_message["report"],
                    alert_message["buy_price"],
                    datetime.date.today().strftime("%Y-%m-%d"), '', '',
                    alert_message["tp1"], alert_message["tp2"],
                    alert_message["tp3"], alert_message["sl"]
                  ])
                  # Notify admins
                  admin_message = f"""🔔 *إنذار اختبار* 

🔹 تم استلام رمز سهم جديد: '{stock_code}'
⚠️ لم يتم العثور على السهم في قاعدة البيانات
✅ تمت إضافة السهم إلى Sheet4 دون إرسال تنبيهات للمستخدمين

📊 المحلل الآلي لأسهم البورصة المصرية"""
                  await send_admin_notification(admin_message, ADMIN_IDS)
            else:
              # Non-buy signals on new stocks should be ignored and reported to admins
              admin_message = f"""⚠️ *تنبيه: إشارة {stock_report} على سهم غير موجود* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
📊 *نوع الإشارة:* {stock_report}
❌ *السبب:* لا يوجد رقم قيد للسهم في النظام

❌ تم تجاهل الإشارة - سهم غير موجود

⏰ *وقت الإشارة المتجاهلة:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
              
              await send_admin_notification(admin_message, ADMIN_IDS)
              logger.warning(f"Non-buy signal ({stock_report}) ignored for new stock {stock_code}")
          
          return jsonify(success=True)
      return 'success', 200
    else:
      print("Get request")
      return 'success', 200
  except Exception as e:
    print("[❌] Exception Occured : ", e)
    # Add better error logging
    logger.error(f"Webhook error: {e}", exc_info=True)
    
    # Return a more informative error response for debugging
    error_details = {
      "error": str(e),
      "payload": request.data.decode('utf-8') if hasattr(request, 'data') else "No data",
      "trace": traceback.format_exc()
    }
  
    # Add missing import at the top of the file
    import traceback
    return jsonify(error=error_details), 500

@app.route('/dashboard')
def dashboard():
    """Render the dashboard template with user data"""
    try:
        # Get user ID from query parameter or session
        user_id = request.args.get('user_id')
        if not user_id:
            # Redirect to login if no user ID provided
            return redirect('/')
        
        # Get user subscription data
        # This is a placeholder - replace with actual data retrieval
        user_data = {
            'user_name': 'المستخدم',
            'subscription_end_date': '2025-12-31',
            'portfolio_value': '25,000',
            'profit_loss': '+2,500',
            'profit_loss_percentage': '10',
            'profit_class': 'text-success',
            'investment_opportunities': [
                {
                    'stock_code': 'COMI',
                    'stock_name': 'البنك التجاري الدولي',
                    'date': '2025-02-27',
                    'description': 'نموذج دعم قوي مع ارتفاع حجم التداول',
                    'recommendation': 'شراء'
                },
                {
                    'stock_code': 'SWDY',
                    'stock_name': 'السويدي اليكتريك',
                    'date': '2025-02-27',
                    'description': 'اختراق مقاومة هامة مع مؤشرات إيجابية',
                    'recommendation': 'شراء'
                }
            ]
        }
        
        return render_template('dashboard.html', **user_data)
    except Exception as e:
        logger.error(f"Dashboard rendering error: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/')
def main2():
    return """
    <html>
    <head>
        <title>Stock Analyzer Bot</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; background-color: white; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; }
            .status { color: #27ae60; font-weight: bold; }
            .footer { margin-top: 30px; font-size: 12px; color: #7f8c8d; }
            .dashboard-link { display: inline-block; margin-top: 20px; background-color: #3498db; color: white; padding: 10px 20px; border-radius: 5px; text-decoration: none; }
            .dashboard-link:hover { background-color: #2980b9; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🤖 المحلل الآلي لأسهم البورصة المصرية</h1>
            <p>حالة الخادم: <span class="status">✅ متصل</span></p>
            <p>Bot Status: <span class="status">ACTIVE</span></p>
            <p>API version: 1.0.0</p>
            <a href="/dashboard?user_id=demo" class="dashboard-link">عرض لوحة التحكم</a>
            <div class="footer">© 2023 Stock Analyzer Bot</div>
        </div>
    </body>
    </html>
    """

def run():
  app.run(host='0.0.0.0', port=9000)


def start_server_async():
  server = Thread(target=run)
  server.start()


if __name__ == '__main__':
    start_server()

# Enhanced functions for the new trading signal logic

def get_last_achieved_target_price(sheet, row, achieved_targets):
    """استخراج سعر آخر هدف محقق من بيانات الشيت"""
    try:
        last_target_price = None
        
        # ترتيب الأهداف حسب الترقيم
        sorted_targets = sorted(achieved_targets)
        
        if sorted_targets:
            last_target = sorted_targets[-1]  # آخر هدف محقق
            
            # استخراج السعر المناسب
            if last_target == 't1':
                last_target_price = sheet.cell(row, 8).value  # tp1 - العمود 8
            elif last_target == 't2':
                last_target_price = sheet.cell(row, 9).value  # tp2 - العمود 9
            elif last_target == 't3':
                last_target_price = sheet.cell(row, 10).value  # tp3 - العمود 10
            
            # التأكد من أن السعر صالح
            if last_target_price and str(last_target_price).strip():
                try:
                    return float(last_target_price)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid target price format: {last_target_price}")
                    return None
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting last achieved target price: {e}")
        return None

def get_last_achieved_target_date(sheet, row, achieved_targets):
    """استخراج تاريخ آخر هدف محقق من بيانات الشيت"""
    try:
        last_target_date = None
        
        # ترتيب الأهداف حسب الترقيم
        sorted_targets = sorted(achieved_targets)
        
        if sorted_targets:
            last_target = sorted_targets[-1]  # آخر هدف محقق
            
            # استخراج التاريخ المناسب (الفهرسة الصحيحة)
            if last_target == 't1':
                last_target_date = sheet.cell(row, 12).value  # t1_date - العمود 12
            elif last_target == 't2':
                last_target_date = sheet.cell(row, 13).value  # t2_date - العمود 13
            elif last_target == 't3':
                last_target_date = sheet.cell(row, 14).value  # t3_date - العمود 14
            
            # التأكد من أن التاريخ صالح
            if last_target_date and str(last_target_date).strip():
                return str(last_target_date).strip()
        
        # إذا لم نجد تاريخ آخر هدف، استخدم تاريخ الشراء
        buy_date = sheet.cell(row, 5).value
        if buy_date and str(buy_date).strip():
            return str(buy_date).strip()
        
        # كحل أخير، استخدم التاريخ الحالي
        return datetime.date.today().strftime("%Y-%m-%d")
        
    except Exception as e:
        logger.error(f"Error getting last achieved target date: {e}")
        return datetime.date.today().strftime("%Y-%m-%d")

def analyze_technical_risk(stock_code):
    """تحليل المؤشرات الفنية لتحديد مستوى المخاطرة باستخدام البيانات الحقيقية"""
    try:
        risk_factors = []
        risk_score = 0
        
        # إنشاء مثيل من محلل الأسهم للحصول على البيانات التاريخية
        analyzer = StockAnalyzer()
        historical_data = analyzer._load_historical_data(stock_code)
        
        if historical_data is None or len(historical_data) < 20:
            # محاولة مع إضافة 'D' للرمز
            historical_data = analyzer._load_historical_data(f"{stock_code}D")
        
        if historical_data is None or len(historical_data) < 20:
            logger.warning(f"Insufficient historical data for {stock_code}, using default risk assessment")
            return {
                'risk_level': "متوسط",
                'risk_score': 25,
                'risk_emoji': "🟡",
                'risk_factors': ["بيانات تاريخية غير كافية للتحليل الدقيق"],
                'technical_data': {
                    'rsi': 50,
                    'macd_diff': 0,
                    'volume_ratio': 1.0,
                    'adx': 25,
                    'bb_position': 0.5
                }
            }
        
        # حساب المؤشرات الفنية
        technical_indicators = analyzer._calculate_technical_indicators(historical_data)
        
        # 1. تحليل مؤشر RSI
        rsi = technical_indicators.get('rsi', 50)
        if rsi > 80:
            risk_score += 35
            risk_factors.append(f"تشبع شرائي شديد جداً (RSI: {rsi:.1f})")
        elif rsi > 75:
            risk_score += 30
            risk_factors.append(f"تشبع شرائي شديد (RSI: {rsi:.1f})")
        elif rsi > 70:
            risk_score += 20
            risk_factors.append(f"تشبع شرائي (RSI: {rsi:.1f})")
        elif rsi < 20:
            risk_score -= 15  # إشارة إيجابية قوية
        elif rsi < 30:
            risk_score -= 10  # إشارة إيجابية
        
        # 2. تحليل مؤشر MACD
        macd = technical_indicators.get('macd', 0)
        macd_signal = technical_indicators.get('macd_signal', 0)
        macd_diff = technical_indicators.get('macd_diff', 0)
        
        if macd_diff < -0.5:
            risk_score += 25
            risk_factors.append("إشارة MACD سلبية قوية")
        elif macd_diff < 0:
            risk_score += 15
            risk_factors.append("إشارة MACD سلبية")
        elif macd_diff > 0.5:
            risk_score -= 10  # إشارة إيجابية
        
        # 3. تحليل حجم التداول
        volume_ratio = 1.0
        if len(historical_data) >= 20:
            recent_volume = historical_data['VOL'].tail(5).mean()
            avg_volume = historical_data['VOL'].tail(20).mean()
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            
            if volume_ratio < 0.3:
                risk_score += 25
                risk_factors.append(f"حجم تداول ضعيف جداً ({volume_ratio*100:.0f}% من المتوسط)")
            elif volume_ratio < 0.6:
                risk_score += 15
                risk_factors.append(f"حجم تداول منخفض ({volume_ratio*100:.0f}% من المتوسط)")
            elif volume_ratio > 2.0:
                risk_score -= 5  # حجم مرتفع قد يكون إيجابي
        
        # 4. تحليل ADX (قوة الاتجاه)
        adx = technical_indicators.get('adx', 25)
        if adx < 20:
            risk_score += 10
            risk_factors.append(f"ضعف في الاتجاه (ADX: {adx:.1f})")
        elif adx > 40:
            risk_score -= 5  # اتجاه قوي
        
        # 5. تحليل Bollinger Bands
        current_price = historical_data['CLOSE'].iloc[-1]
        bb_upper = technical_indicators.get('bb_upper', current_price * 1.02)
        bb_lower = technical_indicators.get('bb_lower', current_price * 0.98)
        bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
        
        if bb_position > 0.9:
            risk_score += 15
            risk_factors.append("السعر قريب من الحد العلوي لـ Bollinger Bands")
        elif bb_position < 0.1:
            risk_score -= 10  # قريب من الحد السفلي - فرصة شراء
        
        # 6. تحليل المتوسطات المتحركة
        ma20 = technical_indicators.get('ma20', current_price)
        ma50 = technical_indicators.get('ma50', current_price)
        
        if current_price < ma20 and ma20 < ma50:
            risk_score += 15
            risk_factors.append("السعر تحت المتوسطات المتحركة (اتجاه هبوطي)")
        elif current_price > ma20 and ma20 > ma50:
            risk_score -= 10  # اتجاه صاعد
        
        # 7. تحليل Money Flow Index إذا توفر
        try:
            money_flow_data = analyze_money_flow(historical_data)
            if money_flow_data:
                mfi = money_flow_data.get('mfi', 50)
                if mfi > 80:
                    risk_score += 20
                    risk_factors.append(f"تدفق نقدي مفرط (MFI: {mfi:.1f})")
                elif mfi < 20:
                    risk_score -= 10
        except Exception as e:
            logger.debug(f"Money flow analysis failed: {e}")
        
        # تحديد مستوى المخاطرة النهائي
        if risk_score >= 70:
            risk_level = "عالي جداً"
            risk_emoji = "🔴"
        elif risk_score >= 45:
            risk_level = "عالي"
            risk_emoji = "🟠"
        elif risk_score >= 25:
            risk_level = "متوسط"
            risk_emoji = "🟡"
        elif risk_score >= 0:
            risk_level = "منخفض"
            risk_emoji = "🟢"
        else:
            risk_level = "منخفض جداً"
            risk_emoji = "🟢"
        
        logger.info(f"Risk analysis for {stock_code}: Level={risk_level}, Score={risk_score}, Factors={len(risk_factors)}")
        
        return {
            'risk_level': risk_level,
            'risk_score': risk_score,
            'risk_emoji': risk_emoji,
            'risk_factors': risk_factors,
            'technical_data': {
                'rsi': rsi,
                'macd_diff': macd_diff,
                'volume_ratio': volume_ratio,
                'adx': adx,
                'bb_position': bb_position
            }
        }
        
    except Exception as e:
        logger.error(f"Error in technical analysis for {stock_code}: {e}")
        return {
            'risk_level': "متوسط", 
            'risk_score': 25, 
            'risk_emoji': "🟡", 
            'risk_factors': [f"خطأ في التحليل الفني: {str(e)}"],
            'technical_data': {
                'rsi': 50,
                'macd_diff': 0,
                'volume_ratio': 1.0,
                'adx': 25,
                'bb_position': 0.5
            }
        }

def check_price_enhancement_opportunity(sheet, stock_code, new_buy_price):
    """فحص إمكانية تعزيز المراكز بسعر أفضل"""
    try:
        has_open_position, existing_row, existing_status = check_open_position(sheet, stock_code)
        
        if has_open_position:
            current_buy_price = float(sheet.cell(existing_row, 4).value)
            new_price = float(new_buy_price)
            
            if new_price < current_buy_price:
                improvement_pct = round((current_buy_price - new_price) / current_buy_price * 100, 2)
                return {
                    'can_enhance': True,
                    'existing_row': existing_row,
                    'current_price': current_buy_price,
                    'new_price': new_price,
                    'improvement_pct': improvement_pct
                }
        
        return {'can_enhance': False}
        
    except Exception as e:
        logger.error(f"Error checking price enhancement: {e}")
        return {'can_enhance': False}

def update_position_with_enhancement(sheet, existing_row, alert_message, improvement_pct):
    """تحديث المركز مع تعزيز السعر والأهداف"""
    try:
        new_buy_price = float(alert_message["buy_price"])
        
        # حفظ السعر الأصلي في عمود منفصل (العمود 17)
        original_price = sheet.cell(existing_row, 4).value
        sheet.update_cell(existing_row, 17, original_price)
        
        # تحديث السعر الجديد والأهداف
        sheet.update_cell(existing_row, 4, new_buy_price)
        sheet.update_cell(existing_row, 8, alert_message["tp1"])
        sheet.update_cell(existing_row, 9, alert_message["tp2"])
        sheet.update_cell(existing_row, 10, alert_message["tp3"])
        sheet.update_cell(existing_row, 11, alert_message["sl"])
        
        # تحديث عداد التعزيز (العمود 16)
        try:
            enhancement_count = int(sheet.cell(existing_row, 16).value or 0) + 1
        except:
            enhancement_count = 1
        sheet.update_cell(existing_row, 16, enhancement_count)
        
        # تحديث تاريخ آخر تعزيز
        sheet.update_cell(existing_row, 5, datetime.date.today().strftime("%Y-%m-%d"))
        
        return True
        
    except Exception as e:
        logger.error(f"Error updating position enhancement: {e}")
        return False

def get_highest_price_between_dates(stock_code, start_date, end_date, last_target_price=None):
    """تحديد أعلى سعر محقق للسهم بين تاريخين باستخدام البيانات التاريخية الحقيقية"""
    try:
        # إنشاء مثيل من محلل الأسهم
        analyzer = StockAnalyzer()
        historical_data = analyzer._load_historical_data(stock_code)
        
        if historical_data is None or len(historical_data) == 0:
            # محاولة مع إضافة 'D'
            historical_data = analyzer._load_historical_data(f"{stock_code}D")
        
        if historical_data is None or len(historical_data) == 0:
            logger.warning(f"No historical data available for {stock_code}")
            # إذا توفر سعر آخر هدف محقق، استخدمه كبديل
            if last_target_price is not None:
                logger.info(f"Using last achieved target price {last_target_price} as fallback for {stock_code}")
                return {
                    'highest_price': round(float(last_target_price), 2),
                    'date_achieved': start_date,
                    'period_days': 0,
                    'data_source': 'last_target_fallback'
                }
            return None
        
        # تحويل التواريخ إلى datetime للمقارنة
        start_dt = datetime.datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.datetime.strptime(end_date, "%Y-%m-%d")
        
        # إذا كانت البيانات تحتوي على عمود DATE
        if 'DATE' in historical_data.columns:
            # تحويل عمود DATE إلى datetime
            historical_data['DATE'] = pd.to_datetime(historical_data['DATE'], format='%Y%m%d', errors='coerce')
            
            # فلترة البيانات حسب النطاق الزمني
            mask = (historical_data['DATE'] >= start_dt) & (historical_data['DATE'] <= end_dt)
            filtered_data = historical_data.loc[mask]
            
            if not filtered_data.empty:
                # البحث عن أعلى سعر في الفترة المحددة
                highest_high = filtered_data['HIGH'].max()
                highest_close = filtered_data['CLOSE'].max()
                highest_price = max(highest_high, highest_close)
                
                # العثور على التاريخ الذي تحقق فيه أعلى سعر
                highest_date_row = filtered_data[
                    (filtered_data['HIGH'] == highest_high) | 
                    (filtered_data['CLOSE'] == highest_close)
                ].iloc[0]
                
                return {
                    'highest_price': round(highest_price, 2),
                    'date_achieved': highest_date_row['DATE'].strftime("%Y-%m-%d"),
                    'period_days': (end_dt - start_dt).days,
                    'data_source': 'historical'
                }
        
        # إذا لم توجد بيانات تاريخية مناسبة للفترة المحددة
        # استخدم سعر آخر هدف محقق كبديل أولاً قبل التقدير
        if last_target_price is not None:
            logger.info(f"No historical data for period, using last target price {last_target_price} for {stock_code}")
            return {
                'highest_price': round(float(last_target_price), 2),
                'date_achieved': start_date,
                'period_days': (end_dt - start_dt).days,
                'data_source': 'last_target_preferred'
            }
        
        # كحل أخير، استخدم آخر الأسعار المتاحة
        if len(historical_data) > 0:
            last_high = historical_data['HIGH'].iloc[-1]
            last_close = historical_data['CLOSE'].iloc[-1]
            estimated_highest = max(last_high, last_close)
            
            return {
                'highest_price': round(estimated_highest, 2),
                'date_achieved': end_date,
                'period_days': (end_dt - start_dt).days,
                'data_source': 'estimated'
            }
        
        return None
        
    except Exception as e:
        logger.error(f"Error getting highest price for {stock_code}: {e}")
        # في حالة خطأ، استخدم سعر آخر هدف إن وجد
        try:
            if last_target_price is not None:
                target_price_float = float(last_target_price)
                best_price = max(float(alert_price), target_price_float)
                profit_pct = round((best_price - float(buy_price)) / float(buy_price) * 100, 2)
                return {
                    'best_exit_price': best_price,
                    'highest_price_achieved': target_price_float,
                    'actual_profit_pct': profit_pct,
                    'calculation_method': 'last_target_used',
                    'calculation_note': f"تم استخدام سعر آخر هدف محقق ({target_price_float}) بسبب خطأ في البيانات",
                    'data_source': 'error_fallback_target'
                }
            else:
                profit_pct = round((float(alert_price) - float(buy_price)) / float(buy_price) * 100, 2)
                return {
                    'best_exit_price': float(alert_price),
                    'highest_price_achieved': float(alert_price),
                    'actual_profit_pct': profit_pct,
                    'calculation_method': 'signal_price',
                    'calculation_note': f"تم الخروج على سعر الإشارة ({alert_price}) بسبب خطأ في البيانات",
                    'data_source': 'error_fallback'
                }
        except:
            return None

def calculate_actual_profit_with_highest_price(buy_price, last_target_date, current_date, stock_code, alert_price, last_target_price=None):
    """حساب الربح الفعلي المحقق على أعلى سعر بين آخر هدف والخروج"""
    try:
        buy_price_float = float(buy_price)
        alert_price_float = float(alert_price)
        
        # الحصول على أعلى سعر محقق مع تمريرير سعر آخر هدف كبديل
        highest_data = get_highest_price_between_dates(
            stock_code, 
            last_target_date, 
            current_date, 
            last_target_price
        )
        
        if highest_data:
            highest_price = highest_data['highest_price']
            data_source = highest_data['data_source']
            
            # تحديد أفضل سعر خروج
            best_exit_price = max(alert_price_float, highest_price)
            actual_profit_pct = round((best_exit_price - buy_price_float) / buy_price_float * 100, 2)
            
            # تحديد طريقة الحساب والتوضيح
            if data_source in ['last_target_fallback', 'last_target_preferred', 'error_fallback']:
                calculation_note = f"تم استخدام سعر آخر هدف محقق ({highest_price}) لحساب الربح"
                calculation_method = 'last_target_used'
            elif best_exit_price == highest_price:
                calculation_note = f"تم تحقيق ربح على أعلى سعر ({highest_price}) خلال الفترة"
                calculation_method = 'highest_price'
            else:
                calculation_note = f"تم الخروج على سعر الإشارة ({alert_price_float})"
                calculation_method = 'signal_price'
            
            return {
                'best_exit_price': best_exit_price,
                'highest_price_achieved': highest_price,
                'actual_profit_pct': actual_profit_pct,
                'calculation_method': calculation_method,
                'calculation_note': calculation_note,
                'data_source': data_source
            }
        else:
            # إذا لم نحصل على أي بيانات ولا يوجد سعر هدف آخير، استخدم سعر الإشارة
            profit_pct = round((alert_price_float - buy_price_float) / buy_price_float * 100, 2)
            return {
                'best_exit_price': alert_price_float,
                'highest_price_achieved': alert_price_float,
                'actual_profit_pct': profit_pct,
                'calculation_method': 'signal_price',
                'calculation_note': f"تم الخروج على سعر الإشارة ({alert_price_float}) - لا توجد بيانات تاريخية",
                'data_source': 'fallback'
            }
            
    except Exception as e:
        logger.error(f"Error calculating actual profit: {e}")
        # في حالة خطأ، استخدم سعر آخر هدف إن وجد، وإلا سعر الإشارة
        try:
            if last_target_price is not None:
                target_price_float = float(last_target_price)
                best_price = max(float(alert_price), target_price_float)
                profit_pct = round((best_price - float(buy_price)) / float(buy_price) * 100, 2)
                return {
                    'best_exit_price': best_price,
                    'highest_price_achieved': target_price_float,
                    'actual_profit_pct': profit_pct,
                    'calculation_method': 'last_target_used',
                    'calculation_note': f"تم استخدام سعر آخر هدف محقق ({target_price_float}) بسبب خطأ في البيانات",
                    'data_source': 'error_fallback_target'
                }
            else:
                profit_pct = round((float(alert_price) - float(buy_price)) / float(buy_price) * 100, 2)
                return {
                    'best_exit_price': float(alert_price),
                    'highest_price_achieved': float(alert_price),
                    'actual_profit_pct': profit_pct,
                    'calculation_method': 'signal_price',
                    'calculation_note': f"تم الخروج على سعر الإشارة ({alert_price}) بسبب خطأ في البيانات",
                    'data_source': 'error_fallback'
                }
        except:
            return None

def create_enhancement_message(stock_name, stock_code, current_price, new_price, improvement_pct, new_targets, new_sl):
    """إنشاء رسالة تعزيز المراكز"""
    # حساب المقاييس الجديدة
    metrics = calculate_metrics(new_price, new_targets, new_sl)
    
    return f"""🔥 *فرصة تعزيز مراكز ممتازة* 🔥

🔹 *السهم:* {stock_name} ({stock_code})
📊 *السعر السابق:* {current_price} جنيه
💰 *السعر المحسن:* {new_price} جنيه
📈 *تحسن بنسبة:* {improvement_pct}%

🎯 *الأهداف المحدثة:*
⭐️ الهدف الأول: {new_targets[0]} جنيه (ربح {metrics['t1_pct']}%)
⭐️ الهدف الثاني: {new_targets[1]} جنيه (ربح {metrics['t2_pct']}%)
⭐️ الهدف الثالث: {new_targets[2]} جنيه (ربح {metrics['t3_pct']}%)

🛑 *وقف الخسارة المحدث:* {new_sl} جنيه (خسارة {metrics['sl_pct']}%)

📊 *نسبة المخاطرة/العائد المحدثة:* 1:{metrics['rr_ratio1']}

💡 *فرصة ذهبية لتحسين متوسط سعر الشراء وزيادة الأرباح المتوقعة*

⏰ *تاريخ التعزيز:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""

def create_risk_warning_message(stock_name, stock_code, buy_price, targets, sl, risk_analysis, metrics):
    """إنشاء رسالة توصية مع تحذير مخاطر"""
    risk_warnings = "\n".join([f"⚠️ {factor}" for factor in risk_analysis['risk_factors']])
    
    # تحديد التوصيات المناسبة حسب مستوى المخاطرة
    if risk_analysis['risk_level'] == "عالي جداً":
        position_recommendation = "تجنب الدخول أو انتظار تحسن الظروف"
        monitoring_recommendation = "مراقبة مستمرة ودخول حذر للغاية"
    elif risk_analysis['risk_level'] == "عالي":
        position_recommendation = "تقليل حجم المركز إلى 25-50% من المعتاد"
        monitoring_recommendation = "مراقبة لصيقة والخروج عند أول إشارة ضعف"
    else:
        position_recommendation = "حجم مركز محدود مع مراقبة مستمرة"
        monitoring_recommendation = "متابعة الأهداف بحذر"
    
    return f"""⚠️ *توصية شراء - مستوى مخاطرة {risk_analysis['risk_level']}* {risk_analysis['risk_emoji']}

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر الشراء:* {buy_price} جنيه

🎯 *الأهداف:*
⭐️ الهدف الأول: {targets[0]} جنيه (ربح {metrics['t1_pct']}%)
⭐️ الهدف الثاني: {targets[1]} جنيه (ربح {metrics['t2_pct']}%)
⭐️ الهدف الثالث: {targets[2]} جنيه (ربح {metrics['t3_pct']}%)

🛑 *وقف الخسارة:* {sl} جنيه (خسارة {metrics['sl_pct']}%)

🚨 *تحذيرات المخاطرة:*
{risk_warnings}

📊 *التحليل الفني:*
• مؤشر RSI: {risk_analysis['technical_data'].get('rsi', 'N/A'):.1f}
• إشارة MACD: {'سلبية' if risk_analysis['technical_data'].get('macd_diff', 0) < 0 else 'إيجابية'}
• نسبة حجم التداول: {risk_analysis['technical_data'].get('volume_ratio', 1)*100:.0f}%
• قوة الاتجاه ADX: {risk_analysis['technical_data'].get('adx', 'N/A'):.1f}

💡 *التوصيات:* 
• {position_recommendation}
• {monitoring_recommendation}
• وضع أوامر وقف خسارة صارمة

⏰ *تاريخ التوصية:* {datetime.date.today().strftime("%Y-%m-%d")}

📊 المحلل الآلي لأسهم البورصة المصرية"""

def identify_golden_opportunity(stock_code, alert_message, risk_analysis, metrics):
    """تحديد ما إذا كانت الفرصة ذهبية أم لا"""
    golden_criteria = {
        'is_golden': False,
        'score': 0,
        'factors': [],
        'priority': 'normal'
    }
    
    try:
        # المعايير الأساسية للفرص الذهبية:
        
        # 1. نسبة مخاطرة/عائد ممتازة (>= 3:1)
        if isinstance(metrics['rr_ratio1'], (int, float)) and metrics['rr_ratio1'] >= 3:
            golden_criteria['score'] += 30
            golden_criteria['factors'].append(f"نسبة مخاطرة/عائد ممتازة ({metrics['rr_ratio1']}:1)")
        elif isinstance(metrics['rr_ratio1'], (int, float)) and metrics['rr_ratio1'] >= 2:
            golden_criteria['score'] += 20
            golden_criteria['factors'].append(f"نسبة مخاطرة/عائد جيدة ({metrics['rr_ratio1']}:1)")
        
        # 2. مستوى مخاطرة منخفض أو منخفض جداً
        if risk_analysis['risk_level'] in ['منخفض جداً']:
            golden_criteria['score'] += 30
            golden_criteria['factors'].append(f"مخاطرة {risk_analysis['risk_level']}")
        elif risk_analysis['risk_level'] in ['منخفض']:
            golden_criteria['score'] += 25
            golden_criteria['factors'].append(f"مخاطرة {risk_analysis['risk_level']}")
        
        # 3. مؤشرات فنية قوية
        technical_data = risk_analysis.get('technical_data', {})
        
        # RSI في منطقة تشبع بيعي (فرصة شراء)
        rsi = technical_data.get('rsi', 50)
        if rsi <= 25:
            golden_criteria['score'] += 25
            golden_criteria['factors'].append(f"RSI في منطقة تشبع بيعي قوي ({rsi:.1f})")
        elif rsi <= 30:
            golden_criteria['score'] += 20
            golden_criteria['factors'].append(f"RSI في منطقة تشبع بيعي ({rsi:.1f})")
        
        # MACD إيجابي
        macd_diff = technical_data.get('macd_diff', 0)
        if macd_diff > 0.5:
            golden_criteria['score'] += 20
            golden_criteria['factors'].append("إشارة MACD إيجابية قوية")
        elif macd_diff > 0:
            golden_criteria['score'] += 15
            golden_criteria['factors'].append("إشارة MACD إيجابية")
        
        # حجم تداول مرتفع
        volume_ratio = technical_data.get('volume_ratio', 1)
        if volume_ratio >= 2.0:
            golden_criteria['score'] += 20
            golden_criteria['factors'].append(f"حجم تداول مرتفع جداً ({volume_ratio*100:.0f}%)")
        elif volume_ratio >= 1.5:
            golden_criteria['score'] += 15
            golden_criteria['factors'].append(f"حجم تداول مرتفع ({volume_ratio*100:.0f}%)")
        
        # ADX قوي
        adx = technical_data.get('adx', 25)
        if adx >= 40:
            golden_criteria['score'] += 15
            golden_criteria['factors'].append(f"اتجاه قوي (ADX: {adx:.1f})")
        
        # 4. الهدف الأول قريب ومحقق بسهولة
        if metrics['t1_pct'] <= 4 and metrics['t1_pct'] > 0:
            golden_criteria['score'] += 15
            golden_criteria['factors'].append(f"هدف أول سريع التحقيق ({metrics['t1_pct']}%)")
        elif metrics['t1_pct'] <= 7 and metrics['t1_pct'] > 0:
            golden_criteria['score'] += 10
            golden_criteria['factors'].append(f"هدف أول قريب ({metrics['t1_pct']}%)")
        
        # 5. نسبة الربح الإجمالية جيدة
        if metrics['t3_pct'] >= 20:
            golden_criteria['score'] += 15
            golden_criteria['factors'].append(f"إمكانية ربح عالية ({metrics['t3_pct']}%)")
        elif metrics['t3_pct'] >= 15:
            golden_criteria['score'] += 10
            golden_criteria['factors'].append(f"إمكانية ربح جيدة ({metrics['t3_pct']}%)")
        
        # 6. وقف خسارة محدود
        if metrics['sl_pct'] <= 3:
            golden_criteria['score'] += 10
            golden_criteria['factors'].append(f"وقف خسارة محدود ({metrics['sl_pct']}%)")
        
        # تحديد مستوى الأولوية
        if golden_criteria['score'] >= 85:
            golden_criteria['is_golden'] = True
            golden_criteria['priority'] = 'ultra_high'  # فرصة ذهبية استثنائية
        elif golden_criteria['score'] >= 65:
            golden_criteria['is_golden'] = True
            golden_criteria['priority'] = 'high'  # فرصة ذهبية قوية
        elif golden_criteria['score'] >= 45:
            golden_criteria['is_golden'] = True
            golden_criteria['priority'] = 'medium'  # فرصة ذهبية متوسطة
        
        logger.info(f"Golden opportunity analysis for {stock_code}: Score={golden_criteria['score']}, Is_Golden={golden_criteria['is_golden']}, Priority={golden_criteria['priority']}")
        
        return golden_criteria
        
    except Exception as e:
        logger.error(f"Error in golden opportunity analysis for {stock_code}: {e}")
        return golden_criteria

def create_golden_opportunity_message(stock_name, stock_code, alert_message, metrics, golden_criteria):
    """إنشاء رسالة تنبيه فوري للفرصة الذهبية"""
    
    # تحديد الرموز والعناوين حسب مستوى الأولوية
    if golden_criteria['priority'] == 'ultra_high':
        header = "🔥💎 *فرصة ذهبية استثنائية* 💎🔥"
        priority_emoji = "🚨🚨🚨"
        urgency_text = "عاجل جداً - فرصة نادرة"
        border = "🔥" * 10
    elif golden_criteria['priority'] == 'high':
        header = "⭐🥇 *فرصة ذهبية قوية* 🥇⭐"
        priority_emoji = "🚨🚨"
        urgency_text = "عاجل - فرصة ممتازة"
        border = "⭐" * 10
    else:
        header = "✨💰 *فرصة ذهبية* 💰✨"
        priority_emoji = "🚨"
        urgency_text = "فرصة مميزة"
        border = "✨" * 10
    
    # قائمة العوامل الذهبية
    golden_factors = "\n".join([f"🌟 {factor}" for factor in golden_criteria['factors']])
    
    return f"""{border}
{header}

{priority_emoji} *{urgency_text}* {priority_emoji}

🔹 *السهم:* {stock_name} ({stock_code})
💰 *سعر الشراء:* {alert_message["buy_price"]} جنيه

🎯 *الأهداف السريعة:*
⚡ الهدف الأول: {alert_message["tp1"]} (ربح {metrics['t1_pct']}%)
⚡ الهدف الثاني: {alert_message["tp2"]} (ربح {metrics['t2_pct']}%)
⚡ الهدف الثالث: {alert_message["tp3"]} (ربح {metrics['t3_pct']}%)

🛡️ *وقف الخسارة:* {alert_message["sl"]} (خسارة {metrics['sl_pct']}%)

💎 *عوامل الفرصة الذهبية:*
{golden_factors}

📊 *نقاط القوة:* {golden_criteria['score']}/100
📈 *نسبة المخاطرة/العائد:* 1:{metrics['rr_ratio1']}

⏰ *وقت التنبيه:* {datetime.datetime.now().strftime("%H:%M:%S")}
📅 *تاريخ التوصية:* {datetime.date.today().strftime("%Y-%m-%d")}

💡 *نصيحة:* استغل هذه الفرصة الذهبية بسرعة قبل فوات الوقت!

📊 المحلل الآلي لأسهم البورصة المصرية
{border}"""

async def send_golden_opportunity_alert(stock_name, stock_code, alert_message, metrics, golden_criteria):
    """إرسال تنبيه فوري للفرصة الذهبية مع أولوية عالية"""
    
    try:
        # إنشاء رسالة الفرصة الذهبية
        golden_message = create_golden_opportunity_message(
            stock_name, stock_code, alert_message, metrics, golden_criteria
        )
        
        # إرسال للمشتركين مع أولوية عالية
        await alertMessage(golden_message, signal_type="golden_opportunity")
        
        # إرسال تنبيه مفصل للإدارة
        admin_golden_message = f"""💎 *تم تحديد فرصة ذهبية* 💎

🔹 *السهم:* {stock_name} ({stock_code})
📊 *مستوى الأولوية:* {golden_criteria['priority'].upper()}
🎯 *نقاط القوة:* {golden_criteria['score']}/100

💎 *العوامل الذهبية:*
{chr(10).join([f"• {factor}" for factor in golden_criteria['factors']])}

📈 *المقاييس المالية:*
• نسبة المخاطرة/العائد: 1:{metrics['rr_ratio1']}
• ربح الهدف الأول: {metrics['t1_pct']}%
• ربح الهدف الثالث: {metrics['t3_pct']}%
• وقف الخسارة: {metrics['sl_pct']}%

⏰ *تم الإرسال:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 المحلل الآلي لأسهم البورصة المصرية"""
        
        await send_admin_notification(admin_golden_message, ADMIN_IDS)
        
        # تسجيل في اللوج
        logger.info(f"Golden opportunity alert sent for {stock_code} - Priority: {golden_criteria['priority']}, Score: {golden_criteria['score']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error sending golden opportunity alert for {stock_code}: {e}")
        return False

def set_golden_opportunity_timer(stock_code, priority, expiry_minutes=30):
    """تعيين مؤقت لانتهاء صلاحية الفرصة الذهبية"""
    import threading
    
    def expire_opportunity():
        try:
            # إرسال تنبيه انتهاء الفرصة بعد المدة المحددة
            expiry_message = f"""⏰ *انتهت صلاحية الفرصة الذهبية* ⏰

🔹 *السهم:* {stock_code}
📊 *مستوى الأولوية:* {priority.upper()}
⏱️ *انتهت الصلاحية بعد:* {expiry_minutes} دقيقة

💡 *تذكير:* ابق منتبهاً للفرص القادمة!

📊 المحلل الآلي لأسهم البورصة المصرية"""
            
            # يمكن إرسالها للإدارة فقط
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(send_admin_notification(expiry_message, ADMIN_IDS))
            loop.close()
            
            logger.info(f"Golden opportunity expired for {stock_code} after {expiry_minutes} minutes")
            
        except Exception as e:
            logger.error(f"Error in golden opportunity timer for {stock_code}: {e}")
    
    try:
        timer = threading.Timer(expiry_minutes * 60, expire_opportunity)
        timer.start()
        logger.info(f"Golden opportunity timer set for {stock_code} - expires in {expiry_minutes} minutes")
        return timer
    except Exception as e:
        logger.error(f"Failed to set timer for {stock_code}: {e}")
        return None

def add_notification_enhancements(message, priority):
    """إضافة تحسينات للتنبيهات حسب الأولوية"""
    try:
        if priority == 'ultra_high':
            # إشعار بـ 3 رسائل متتالية للتأكيد
            return [
                message, 
                "🔔 تذكير: فرصة ذهبية استثنائية!", 
                "⏰ آخر تذكير: لا تفوت الفرصة!"
            ]
        elif priority == 'high':
            return [
                message, 
                "🔔 تذكير: فرصة ذهبية قوية!"
            ]
        else:
            return [message]
    except Exception as e:
        logger.error(f"Error in notification enhancements: {e}")
        return [message]

async def send_enhanced_golden_alert(stock_name, stock_code, alert_message, metrics, golden_criteria):
    """إرسال تنبيه فوري محسن للفرصة الذهبية مع تحسينات إضافية"""
    try:
        # إنشاء رسالة الفرصة الذهبية
        golden_message = create_golden_opportunity_message(
            stock_name, stock_code, alert_message, metrics, golden_criteria
        )
        
        # إضافة تحسينات التنبيه
        enhanced_messages = add_notification_enhancements(golden_message, golden_criteria['priority'])
        
        # إرسال الرسائل المحسنة
        for i, message in enumerate(enhanced_messages):
            await alertMessage(message, signal_type=f"golden_opportunity_{i+1}")
            
            # تأخير قصير بين الرسائل للفرص عالية الأولوية
            if len(enhanced_messages) > 1 and i < len(enhanced_messages) - 1:
                import asyncio
                await asyncio.sleep(2)  # تأخير ثانيتين
        
        # تعيين مؤقت انتهاء الصلاحية
        if golden_criteria['priority'] == 'ultra_high':
            timer = set_golden_opportunity_timer(stock_code, golden_criteria['priority'], 20)  # 20 دقيقة
        elif golden_criteria['priority'] == 'high':
            timer = set_golden_opportunity_timer(stock_code, golden_criteria['priority'], 30)  # 30 دقيقة
        else:
            timer = set_golden_opportunity_timer(stock_code, golden_criteria['priority'], 45)  # 45 دقيقة
        
        # إرسال تنبيه مفصل للإدارة
        admin_golden_message = f"""💎 *تم تحديد فرصة ذهبية محسنة* 💎

🔹 *السهم:* {stock_name} ({stock_code})
📊 *مستوى الأولوية:* {golden_criteria['priority'].upper()}
🎯 *نقاط القوة:* {golden_criteria['score']}/100
📱 *عدد الرسائل المرسلة:* {len(enhanced_messages)}

💎 *العوامل الذهبية:*
{chr(10).join([f"• {factor}" for factor in golden_criteria['factors']])}

📈 *المقاييس المالية:*
• نسبة المخاطرة/العائد: 1:{metrics['rr_ratio1']}
• ربح الهدف الأول: {metrics['t1_pct']}%
• ربح الهدف الثالث: {metrics['t3_pct']}%
• وقف الخسارة: {metrics['sl_pct']}%

⏰ *تم الإرسال:* {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
⏱️ *المؤقت:* {'نشط' if timer else 'غير نشط'}

📊 المحلل الآلي لأسهم البورصة المصرية"""
        
        await send_admin_notification(admin_golden_message, ADMIN_IDS)
        
        # تسجيل في اللوج
        logger.info(f"Enhanced golden opportunity alert sent for {stock_code} - Priority: {golden_criteria['priority']}, Score: {golden_criteria['score']}, Messages: {len(enhanced_messages)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error sending enhanced golden opportunity alert for {stock_code}: {e}")
        return False
