import os
import telegram
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mpl_dates
from datetime import datetime, timedelta
from mplfinance.original_flavor import candlestick_ohlc
from aiogram import Bo<PERSON>, Dispatcher, types
from aiogram.types import Message
from auth import dp, bot
from config import chart_data_dir
from chart8 import get_stock_price, plot_all, is_support, is_resistance, is_far_from_level
from chart import plot_stock_data, MACD, Stochastic, bollinger_bands, calculate_rsi, linear_regression_channel
from user_limit import check_user_limit, DAILY_FREE_LIMIT

@dp.message_handler(commands=['chart'])
async def chart_handler(message: Message):
    subscriber_type, count = await check_user_limit(message)
    if subscriber_type == "free" and count >= DAILY_FREE_LIMIT:
        await message.answer(f"Sorry, more than {DAILY_FREE_LIMIT} reports per day only available for paid subscribers to continue using the service send PM to  @elborsa_bot.")
        return
    try:
        # Get the stock code from the message text
        stock_code = message.text.split()[1].upper()
        
        # Define the file path for the stock data file
        chart_data_path = f'{chart_data_dir}/{stock_code}D.TXT'
        
        # Check if the file exists
        if not os.path.exists(chart_data_path):
            await message.answer('Stock data not found.')
            return
        
        # Call the plot_stock_data function to generate the stock chart
        plot_stock_data(chart_data_path)
        
        # Send the stock chart image to the user
        stock_chart_path = chart_data_path.replace('.TXT', '.png')
        with open(stock_chart_path, "rb") as f:
            await bot.send_photo(chat_id=message.chat.id, photo=f)
        
        # Get the stock data from the file
        df = get_stock_price(chart_data_path)
        
        # Find the support and resistance levels
        levels = []
        for i in range(2, len(df)-2):
            if is_support(df, i):
                l = df['LOW'][i]
                if is_far_from_level(l, levels, df):
                    levels.append((i, l))
            elif is_resistance(df, i):
                l = df['HIGH'][i]
                if is_far_from_level(l, levels, df):
                    levels.append((i, l))
        
        # Create the support/resistance chart and save it as an image
        plot_all(levels, df, chart_data_path)
        sr_chart_path = chart_data_path.replace('.TXT', '.png')
        
        # Send the support/resistance chart to the user
        with open(sr_chart_path, 'rb') as f:
            await bot.send_photo(chat_id=message.chat.id, photo=f)
        
        # Generate recommendations based on technical analysis
        recommendations = generate_recommendations(df, levels)
        
        # Send the recommendations to the user
        await message.answer(recommendations)
    
    except IndexError:
        await message.answer('Please enter a stock code after the /chart command.')
        return
    
    except Exception as e:
        await message.answer(f'Error: {e}')
        return

def generate_recommendations(df, levels):
    # Calculate technical indicators
    rsi = calculate_rsi(df, period=14)
    macd = MACD(df, 12, 26, 9)
    stochastic = Stochastic(df, 14, 3)
    upper_band, lower_band, rolling_mean = bollinger_bands(df['CLOSE'])
    
    # Current price and analysis
    current_price = df['CLOSE'].iloc[-1]
    volatility = df['CLOSE'].pct_change().std() * 100
    
    # Calculate SMA 50
    sma_50 = df['CLOSE'].rolling(window=50).mean().iloc[-1]
    sma_signal = "Price is " + ("above" if current_price > sma_50 else "below") + " SMA"
    
    # Volume analysis
    volume_text = ""
    if 'VOLUME' in df.columns:
        current_volume = df['VOLUME'].iloc[-1]
        volume_text = f"\n📊Volume: {current_volume:,.0f}"
    
    # MACD signal
    macd_signal = "Bullish Crossover Mode" if macd['macd'][-1] > macd['signal'][-1] else "Bearish Crossover Mode"
    
    # BBands signal
    bb_signal = get_bb_signal(current_price, upper_band[-1], lower_band[-1])
    
    recommendations = f"""💰Price: {current_price:.2f}{volume_text}
🔥 Average Volatility: {volatility:.2f}%
⚪ RSI(14): {rsi[-1]:.2f}
⚪ BBands(20,2): {bb_signal}
❇ MACD: {macd_signal}
🔴 SMA(50): {sma_signal}"""

    # Add support/resistance levels if available
    support_levels = [level[1] for level in levels if current_price > level[1]]
    resistance_levels = [level[1] for level in levels if current_price < level[1]]
    
    if support_levels or resistance_levels:
        recommendations += "\n\n📈 Levels:"
        if support_levels:
            recommendations += f"\n💠 Support: {', '.join([f'{level:.2f}' for level in support_levels])}"
        if resistance_levels:
            recommendations += f"\n💠 Resistance: {', '.join([f'{level:.2f}' for level in resistance_levels])}"

    return recommendations

def get_bb_signal(price, upper, lower):
    if price > upper:
        return "Overbought"
    elif price < lower:
        return "Oversold"
    else:
        return "Normal Range"

def get_rsi_description(rsi_value):
    if rsi_value > 70:
        return "يشير إلى تشبع شرائي"
    elif rsi_value < 30:
        return "يشير إلى تشبع بيعي"
    else:
        return "في المنطقة المحايدة"

def get_macd_description(macd_data):
    if macd_data['macd'][-1] > macd_data['signal'][-1]:
        return "تقاطع إيجابي يشير إلى زخم صعودي"
    else:
        return "تقاطع سلبي يشير إلى زخم هبوطي"

def get_bb_description(price, upper, lower, mean):
    if price > upper:
        return "السعر فوق النطاق العلوي، احتمال تصحيح هبوطي"
    elif price < lower:
        return "السعر تحت النطاق السفلي، احتمال ارتداد صعودي"
    else:
        return "السعر ضمن النطاق الطبيعي"