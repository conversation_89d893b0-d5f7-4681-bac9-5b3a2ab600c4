#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من حل مشكلة الدالة المكررة
"""

import re

def test_process_data_fix():
    """فحص أن الدالة المكررة تم حذفها"""
    try:
        with open('process_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص عدد الدوال
        function_matches = re.findall(r'async def process_callback_messages', content)
        print(f"🔍 عدد دوال process_callback_messages: {len(function_matches)}")
        
        if len(function_matches) == 1:
            print("✅ توجد دالة واحدة فقط - المشكلة تم حلها!")
        else:
            print(f"❌ توجد {len(function_matches)} دالة - لا تزال هناك مشكلة")
        
        # فحص الـ decorator
        decorator_matches = re.findall(r'@dp\.message_handler\(content_types=types\.ContentType\.TEXT\)', content)
        print(f"🔍 عدد decorators المُعرِّضة للخطر: {len(decorator_matches)}")
        
        if len(decorator_matches) == 0:
            print("✅ لا توجد decorators خطيرة - ممتاز!")
        else:
            print(f"❌ توجد {len(decorator_matches)} decorators خطيرة")
        
        # فحص وجود فحص الأوامر
        command_check = "if message.text.startswith('/'):" in content
        if command_check:
            print("✅ فحص الأوامر موجود في الدالة")
        else:
            print("❌ فحص الأوامر غير موجود")
        
        return len(function_matches) == 1 and len(decorator_matches) == 0 and command_check
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        return False

def test_main_py_structure():
    """فحص بنية main.py"""
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص وجود تسجيل الأوامر
        promo_commands = [
            "dp.message_handler(commands=['create_trial_code'])",
            "dp.message_handler(commands=['create_discount_code'])",
            "dp.message_handler(commands=['redeem'])",
            "dp.message_handler(commands=['list_codes'])"
        ]
        
        print("\n🔍 فحص تسجيل أوامر الأكواد:")
        all_commands_registered = True
        for cmd in promo_commands:
            if cmd in content:
                print(f"✅ {cmd}")
            else:
                print(f"❌ {cmd}")
                all_commands_registered = False
        
        # فحص وجود معالج الرسائل العام في النهاية
        general_handler = "@dp.message_handler()" in content and "handle_all_messages" in content
        if general_handler:
            print("✅ معالج الرسائل العام مُسجل بشكل صحيح")
        else:
            print("❌ معالج الرسائل العام غير مُسجل بشكل صحيح")
        
        return all_commands_registered and general_handler
        
    except Exception as e:
        print(f"❌ خطأ في فحص main.py: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء فحص الإصلاح...")
    
    process_data_ok = test_process_data_fix()
    main_py_ok = test_main_py_structure()
    
    print(f"\n📊 النتائج:")
    print(f"process_data.py: {'✅ سليم' if process_data_ok else '❌ يحتاج إصلاح'}")
    print(f"main.py: {'✅ سليم' if main_py_ok else '❌ يحتاج إصلاح'}")
    
    if process_data_ok and main_py_ok:
        print("\n🎉 الإصلاح تم بنجاح! الأوامر يجب أن تعمل الآن.")
        print("\n📋 اختبر الأوامر التالية:")
        print("- /create_discount_code 50 15")
        print("- /create_trial_code 7 30") 
        print("- /redeem TEST123")
        print("- /list_codes")
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إصلاح")
