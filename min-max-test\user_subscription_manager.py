#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير اشتراكات المستخدمين - تحديث بيانات المستخدمين في الجدول الصحيح
User Subscription Manager - Update user data in correct sheet
"""

import logging
import datetime
from typing import Dict, Optional, Tuple
from auth import open_google_sheet

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

class UserSubscriptionManager:
    """مدير اشتراكات المستخدمين"""
    
    @staticmethod
    def find_user_in_sheet(user_id: str) -> Optional[Dict]:
        """البحث عن المستخدم في الجدول الصحيح"""
        try:
            # البحث في sheet2 (الجدول الحالي)
            all_records = sheet2.get_all_records()
            
            for row_index, record in enumerate(all_records, start=2):  # البداية من الصف 2
                values = list(record.values())
                
                if len(values) >= 1:
                    record_user_id = str(values[0]) if values[0] else ""
                    
                    if record_user_id == user_id:
                        return {
                            'row': row_index,
                            'user_id': record_user_id,
                            'count': int(values[1]) if len(values) > 1 and str(values[1]).isdigit() else 0,
                            'subscription_type': str(values[2]).lower() if len(values) > 2 and values[2] else "free",
                            'end_date': str(values[3]) if len(values) > 3 and values[3] else "",
                            'first_name': str(values[4]) if len(values) > 4 and values[4] else "",
                            'last_name': str(values[5]) if len(values) > 5 and values[5] else ""
                        }
            
            logger.warning(f"المستخدم {user_id} غير موجود في الجدول")
            return None
            
        except Exception as e:
            logger.error(f"خطأ في البحث عن المستخدم {user_id}: {e}")
            return None
    
    @staticmethod
    def update_user_subscription(user_id: str, subscription_type: str, end_date: str = None) -> bool:
        """تحديث اشتراك المستخدم"""
        try:
            user_data = UserSubscriptionManager.find_user_in_sheet(user_id)
            
            if not user_data:
                logger.error(f"لا يمكن العثور على المستخدم {user_id} لتحديث الاشتراك")
                return False
            
            row = user_data['row']
            
            # تحديث نوع الاشتراك (العمود 3)
            sheet2.update_cell(row, 3, subscription_type)
            logger.info(f"تم تحديث نوع الاشتراك للمستخدم {user_id} إلى {subscription_type}")
            
            # تحديث تاريخ الانتهاء (العمود 4) إذا تم توفيره
            if end_date:
                sheet2.update_cell(row, 4, end_date)
                logger.info(f"تم تحديث تاريخ انتهاء الاشتراك للمستخدم {user_id} إلى {end_date}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث اشتراك المستخدم {user_id}: {e}")
            return False
    
    @staticmethod
    def activate_trial_subscription(user_id: str, trial_days: int) -> bool:
        """تفعيل اشتراك تجريبي للمستخدم"""
        try:
            # حساب تاريخ انتهاء التجربة
            end_date = (datetime.date.today() + datetime.timedelta(days=trial_days)).strftime("%Y-%m-%d")
            
            # تحديث الاشتراك
            success = UserSubscriptionManager.update_user_subscription(
                user_id=user_id,
                subscription_type="trail",  # استخدام trail كما هو في النظام
                end_date=end_date
            )
            
            if success:
                logger.info(f"تم تفعيل التجربة المجانية للمستخدم {user_id} لمدة {trial_days} أيام حتى {end_date}")
                return True
            else:
                logger.error(f"فشل في تفعيل التجربة المجانية للمستخدم {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في تفعيل التجربة المجانية للمستخدم {user_id}: {e}")
            return False
    
    @staticmethod
    def get_user_subscription_info(user_id: str) -> Optional[Dict]:
        """الحصول على معلومات اشتراك المستخدم"""
        try:
            user_data = UserSubscriptionManager.find_user_in_sheet(user_id)
            
            if not user_data:
                return None
            
            # التحقق من حالة الاشتراك
            subscription_type = user_data['subscription_type']
            end_date_str = user_data['end_date']
            
            is_active = False
            days_remaining = 0
            
            if subscription_type in ['trail', 'paid'] and end_date_str:
                try:
                    end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                    today = datetime.date.today()
                    
                    if end_date >= today:
                        is_active = True
                        days_remaining = (end_date - today).days
                    
                except ValueError:
                    logger.warning(f"تاريخ غير صحيح للمستخدم {user_id}: {end_date_str}")
            
            return {
                'user_id': user_id,
                'subscription_type': subscription_type,
                'end_date': end_date_str,
                'is_active': is_active,
                'days_remaining': days_remaining,
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name']
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات اشتراك المستخدم {user_id}: {e}")
            return None
    
    @staticmethod
    def add_new_user_to_sheet(user_id: str, first_name: str = "", last_name: str = "") -> bool:
        """إضافة مستخدم جديد للجدول"""
        try:
            # التحقق من عدم وجود المستخدم مسبقاً
            existing_user = UserSubscriptionManager.find_user_in_sheet(user_id)
            if existing_user:
                logger.warning(f"المستخدم {user_id} موجود بالفعل")
                return True
            
            # إضافة المستخدم الجديد
            today = datetime.date.today().strftime("%Y-%m-%d")
            new_user_data = [
                user_id,           # العمود 1: user_id
                0,                 # العمود 2: count
                "free",            # العمود 3: subscription_type
                today,             # العمود 4: end_date
                first_name,        # العمود 5: first_name
                last_name          # العمود 6: last_name
            ]
            
            sheet2.append_row(new_user_data)
            logger.info(f"تم إضافة المستخدم الجديد {user_id} ({first_name} {last_name})")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة المستخدم الجديد {user_id}: {e}")
            return False

# دوال مساعدة للاستخدام السهل
def activate_user_trial(user_id: str, trial_days: int) -> Tuple[bool, str]:
    """تفعيل تجربة مجانية للمستخدم"""
    try:
        success = UserSubscriptionManager.activate_trial_subscription(user_id, trial_days)
        
        if success:
            end_date = (datetime.date.today() + datetime.timedelta(days=trial_days)).strftime("%Y-%m-%d")
            return True, f"تم تفعيل التجربة المجانية لمدة {trial_days} أيام حتى {end_date}"
        else:
            return False, "فشل في تفعيل التجربة المجانية"
            
    except Exception as e:
        logger.error(f"خطأ في تفعيل التجربة للمستخدم {user_id}: {e}")
        return False, f"خطأ في النظام: {str(e)}"

def get_user_info(user_id: str) -> Optional[Dict]:
    """الحصول على معلومات المستخدم"""
    return UserSubscriptionManager.get_user_subscription_info(user_id)

def update_subscription(user_id: str, subscription_type: str, end_date: str = None) -> bool:
    """تحديث اشتراك المستخدم"""
    return UserSubscriptionManager.update_user_subscription(user_id, subscription_type, end_date)

# اختبار النظام
def test_subscription_manager():
    """اختبار مدير الاشتراكات"""
    try:
        print("🧪 اختبار مدير الاشتراكات...")
        
        # اختبار البحث عن مستخدم موجود
        test_user_id = "1078882205"  # من البيانات الحقيقية
        
        print(f"\n🔍 البحث عن المستخدم {test_user_id}:")
        user_data = UserSubscriptionManager.find_user_in_sheet(test_user_id)
        
        if user_data:
            print(f"✅ تم العثور على المستخدم:")
            print(f"   - الاسم: {user_data['first_name']} {user_data['last_name']}")
            print(f"   - نوع الاشتراك: {user_data['subscription_type']}")
            print(f"   - تاريخ الانتهاء: {user_data['end_date']}")
            print(f"   - الصف: {user_data['row']}")
            
            # اختبار تحديث الاشتراك (محاكاة)
            print(f"\n🧪 محاكاة تحديث الاشتراك...")
            print(f"   - سيتم تحديث المستخدم {test_user_id} إلى trail لمدة 7 أيام")
            print(f"   - هذا اختبار محاكاة فقط - لن يتم التحديث الفعلي")
            
            return True
        else:
            print(f"❌ لم يتم العثور على المستخدم {test_user_id}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

if __name__ == "__main__":
    test_subscription_manager()
