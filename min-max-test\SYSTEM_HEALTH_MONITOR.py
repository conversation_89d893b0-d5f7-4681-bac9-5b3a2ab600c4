#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة مراقبة مستمرة لنظام إشارات التداول
Continuous Health Monitor for Trading Signals System
"""

import requests
import json
import time
import logging
from datetime import datetime, timedelta
import threading
import sys
import signal
import os

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_monitor.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TradingSystemMonitor:
    def __init__(self, base_url="http://localhost:9000", check_interval=30):
        self.base_url = base_url
        self.check_interval = check_interval  # ثواني
        self.running = False
        self.stats = {
            "start_time": None,
            "total_checks": 0,
            "successful_checks": 0,
            "failed_checks": 0,
            "response_times": [],
            "errors": [],
            "uptime_percentage": 0,
            "last_successful_check": None,
            "last_failed_check": None
        }
        self.alerts = []
        
    def start_monitoring(self):
        """بدء المراقبة المستمرة"""
        self.running = True
        self.stats["start_time"] = datetime.now()
        
        logger.info("🚀 بدء مراقبة نظام إشارات التداول")
        logger.info(f"📡 رابط النظام: {self.base_url}")
        logger.info(f"⏱️ فترة الفحص: {self.check_interval} ثانية")
        logger.info("=" * 60)
        
        # إعداد معالج إشارة التوقف
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        try:
            while self.running:
                self._perform_health_check()
                self._update_statistics()
                self._check_for_alerts()
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            self._stop_monitoring()
        except Exception as e:
            logger.error(f"🚨 خطأ في المراقبة: {e}")
            self._stop_monitoring()
    
    def _signal_handler(self, signum, frame):
        """معالج إشارات التوقف"""
        logger.info(f"\n📨 تم استقبال إشارة التوقف ({signum})")
        self._stop_monitoring()
    
    def _stop_monitoring(self):
        """إيقاف المراقبة وإنشاء التقرير النهائي"""
        self.running = False
        logger.info("\n🛑 إيقاف مراقبة النظام...")
        self._generate_final_report()
        sys.exit(0)
    
    def _perform_health_check(self):
        """إجراء فحص صحة النظام"""
        self.stats["total_checks"] += 1
        check_time = datetime.now()
        
        # اختبارات متعددة
        checks = {
            "basic_connectivity": self._check_basic_connectivity,
            "health_endpoint": self._check_health_endpoint,
            "webhook_response": self._check_webhook_responsiveness,
            "dashboard_access": self._check_dashboard_access
        }
        
        check_results = {}
        overall_success = True
        total_response_time = 0
        
        for check_name, check_func in checks.items():
            try:
                start_time = time.time()
                success, details = check_func()
                response_time = (time.time() - start_time) * 1000  # milliseconds
                
                check_results[check_name] = {
                    "success": success,
                    "details": details,
                    "response_time": response_time
                }
                
                total_response_time += response_time
                if not success:
                    overall_success = False
                    
            except Exception as e:
                check_results[check_name] = {
                    "success": False,
                    "details": f"Exception: {str(e)}",
                    "response_time": 0
                }
                overall_success = False
        
        # تسجيل النتائج
        if overall_success:
            self.stats["successful_checks"] += 1
            self.stats["last_successful_check"] = check_time
            self.stats["response_times"].append(total_response_time)
            
            status = "🟢 صحي"
            logger.info(f"{status} | الفحص #{self.stats['total_checks']} | الاستجابة: {total_response_time:.0f}ms")
            
        else:
            self.stats["failed_checks"] += 1
            self.stats["last_failed_check"] = check_time
            
            # تسجيل الأخطاء
            failed_checks = [name for name, result in check_results.items() if not result["success"]]
            error_details = {
                "timestamp": check_time.isoformat(),
                "failed_checks": failed_checks,
                "details": {name: result["details"] for name, result in check_results.items() if not result["success"]}
            }
            self.stats["errors"].append(error_details)
            
            status = "🔴 فشل"
            logger.warning(f"{status} | الفحص #{self.stats['total_checks']} | فشل في: {', '.join(failed_checks)}")
            
            # إرسال تنبيه
            self._add_alert("HEALTH_CHECK_FAILED", f"فشل فحص الصحة: {', '.join(failed_checks)}")
    
    def _check_basic_connectivity(self):
        """فحص الاتصال الأساسي"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            if response.status_code in [200, 404]:  # 404 مقبول إذا لم يكن هناك route للـ root
                return True, f"Status: {response.status_code}"
            else:
                return False, f"Status: {response.status_code}"
        except requests.exceptions.ConnectionError:
            return False, "Connection refused"
        except requests.exceptions.Timeout:
            return False, "Request timeout"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def _check_health_endpoint(self):
        """فحص endpoint الصحة"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                try:
                    data = response.json()
                    return True, f"Health: {data.get('status', 'unknown')}"
                except json.JSONDecodeError:
                    return True, "Health endpoint accessible (non-JSON)"
            else:
                return False, f"Status: {response.status_code}"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def _check_webhook_responsiveness(self):
        """فحص استجابة webhook"""
        try:
            # إرسال طلب فارغ للتحقق من الاستجابة السريعة
            response = requests.post(f"{self.base_url}/webhook", timeout=5)
            # أي استجابة تدل على أن الـ webhook يعمل
            if response.status_code in [200, 400, 405]:  # 400/405 مقبولان للطلبات الفارغة
                return True, f"Webhook responsive: {response.status_code}"
            else:
                return False, f"Unexpected status: {response.status_code}"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def _check_dashboard_access(self):
        """فحص الوصول للوحة التحكم"""
        try:
            response = requests.get(f"{self.base_url}/dashboard?user_id=monitor", timeout=5)
            if response.status_code in [200, 404]:  # 404 مقبول إذا لم يكن Dashboard موجود
                return True, f"Dashboard accessible: {response.status_code}"
            else:
                return False, f"Status: {response.status_code}"
        except Exception as e:
            return False, f"Error: {str(e)}"
    
    def _update_statistics(self):
        """تحديث الإحصائيات"""
        if self.stats["total_checks"] > 0:
            self.stats["uptime_percentage"] = (self.stats["successful_checks"] / self.stats["total_checks"]) * 100
        
        # الحفاظ على آخر 100 وقت استجابة فقط لتوفير الذاكرة
        if len(self.stats["response_times"]) > 100:
            self.stats["response_times"] = self.stats["response_times"][-100:]
        
        # الحفاظ على آخر 50 خطأ فقط
        if len(self.stats["errors"]) > 50:
            self.stats["errors"] = self.stats["errors"][-50:]
    
    def _check_for_alerts(self):
        """فحص الحاجة لإرسال تنبيهات"""
        # تنبيه إذا انخفض معدل التشغيل
        if self.stats["uptime_percentage"] < 90 and self.stats["total_checks"] >= 5:
            self._add_alert("LOW_UPTIME", f"معدل التشغيل منخفض: {self.stats['uptime_percentage']:.1f}%")
        
        # تنبيه إذا فشل أكثر من 3 فحوصات متتالية
        if self.stats["failed_checks"] >= 3:
            recent_failures = self.stats["errors"][-3:] if len(self.stats["errors"]) >= 3 else []
            if len(recent_failures) == 3:
                last_times = [datetime.fromisoformat(err["timestamp"]) for err in recent_failures]
                if all((last_times[i+1] - last_times[i]).total_seconds() < self.check_interval * 2 for i in range(len(last_times)-1)):
                    self._add_alert("CONSECUTIVE_FAILURES", "3 فشوليات متتالية في فحص النظام")
        
        # تنبيه إذا ارتفع وقت الاستجابة بشكل كبير
        if len(self.stats["response_times"]) >= 5:
            recent_times = self.stats["response_times"][-5:]
            avg_recent = sum(recent_times) / len(recent_times)
            if avg_recent > 10000:  # أكثر من 10 ثواني
                self._add_alert("HIGH_RESPONSE_TIME", f"وقت استجابة عالي: {avg_recent:.0f}ms")
    
    def _add_alert(self, alert_type, message):
        """إضافة تنبيه جديد"""
        # تجنب التنبيهات المكررة
        recent_alerts = [a for a in self.alerts if a["type"] == alert_type and 
                        (datetime.now() - datetime.fromisoformat(a["timestamp"])).total_seconds() < 300]
        
        if not recent_alerts:
            alert = {
                "type": alert_type,
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
            self.alerts.append(alert)
            logger.warning(f"🚨 تنبيه: {message}")
    
    def _generate_status_report(self):
        """إنشاء تقرير الحالة الحالية"""
        if not self.stats["start_time"]:
            return "المراقبة لم تبدأ بعد"
        
        runtime = datetime.now() - self.stats["start_time"]
        
        report = f"""
📊 تقرير حالة نظام إشارات التداول
{'=' * 50}
⏱️ مدة التشغيل: {runtime}
🔢 إجمالي الفحوصات: {self.stats['total_checks']}
✅ فحوصات ناجحة: {self.stats['successful_checks']}
❌ فحوصات فاشلة: {self.stats['failed_checks']}
📈 معدل التشغيل: {self.stats['uptime_percentage']:.2f}%
"""
        
        if self.stats["response_times"]:
            avg_response = sum(self.stats["response_times"]) / len(self.stats["response_times"])
            max_response = max(self.stats["response_times"])
            min_response = min(self.stats["response_times"])
            report += f"""
⚡ متوسط وقت الاستجابة: {avg_response:.0f}ms
📊 نطاق الاستجابة: {min_response:.0f}ms - {max_response:.0f}ms
"""
        
        if self.stats["last_successful_check"]:
            last_success = datetime.now() - self.stats["last_successful_check"]
            report += f"🟢 آخر فحص ناجح: منذ {last_success}\n"
        
        if self.stats["last_failed_check"]:
            last_failure = datetime.now() - self.stats["last_failed_check"]
            report += f"🔴 آخر فحص فاشل: منذ {last_failure}\n"
        
        if self.alerts:
            recent_alerts = [a for a in self.alerts[-5:]]
            report += f"\n🚨 آخر التنبيهات:\n"
            for alert in recent_alerts:
                timestamp = datetime.fromisoformat(alert["timestamp"])
                time_ago = datetime.now() - timestamp
                report += f"  • {alert['message']} (منذ {time_ago})\n"
        
        return report
    
    def _generate_final_report(self):
        """إنشاء التقرير النهائي"""
        logger.info("\n" + "=" * 60)
        logger.info("📋 التقرير النهائي لمراقبة النظام")
        logger.info("=" * 60)
        
        print(self._generate_status_report())
        
        # حفظ التقرير المفصل
        final_report = {
            "monitoring_session": {
                "start_time": self.stats["start_time"].isoformat() if self.stats["start_time"] else None,
                "end_time": datetime.now().isoformat(),
                "duration_seconds": (datetime.now() - self.stats["start_time"]).total_seconds() if self.stats["start_time"] else 0
            },
            "statistics": self.stats,
            "alerts": self.alerts
        }
        
        with open('monitoring_report.json', 'w', encoding='utf-8') as f:
            json.dump(final_report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info("📄 تم حفظ التقرير المفصل في: monitoring_report.json")
        logger.info("📋 تم حفظ سجل المراقبة في: system_monitor.log")
    
    def print_live_status(self):
        """طباعة الحالة المباشرة (للاستخدام في thread منفصل)"""
        def status_printer():
            while self.running:
                time.sleep(60)  # تحديث كل دقيقة
                if self.running:
                    os.system('cls' if os.name == 'nt' else 'clear')  # مسح الشاشة
                    print(self._generate_status_report())
        
        status_thread = threading.Thread(target=status_printer, daemon=True)
        status_thread.start()

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة مراقبة نظام إشارات التداول")
    print("=" * 50)
    print("⌨️ اضغط Ctrl+C لإيقاف المراقبة")
    print()
    
    # يمكن تخصيص المعاملات حسب الحاجة
    monitor = TradingSystemMonitor(
        base_url="http://localhost:9000",
        check_interval=30  # فحص كل 30 ثانية
    )
    
    # بدء طباعة الحالة المباشرة
    monitor.print_live_status()
    
    # بدء المراقبة
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
