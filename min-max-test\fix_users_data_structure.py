#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بنية بيانات المستخدمين في Google Sheets
Fix user data structure in Google Sheets
"""

import logging
import datetime
from typing import List, Dict

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_current_structure():
    """تحليل البنية الحالية للبيانات"""
    try:
        from auth import open_google_sheet
        
        print("🔍 تحليل البنية الحالية للبيانات...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        # جلب جميع البيانات
        all_records = sheet2.get_all_records()
        headers = sheet2.row_values(1)
        
        print(f"📋 الأعمدة الحالية: {headers}")
        print(f"📊 عدد السجلات: {len(all_records)}")
        
        if all_records:
            print("\n📝 تحليل أول سجل:")
            first_record = all_records[0]
            for key, value in first_record.items():
                print(f"   '{key}': '{value}'")
            
            # محاولة تحديد الأعمدة الصحيحة
            print("\n🔍 محاولة تحديد الأعمدة:")
            
            # البحث عن عمود معرف المستخدم (أرقام طويلة)
            user_id_column = None
            for key, value in first_record.items():
                if isinstance(value, (int, str)) and str(value).isdigit() and len(str(value)) >= 8:
                    user_id_column = key
                    print(f"   🆔 عمود معرف المستخدم المحتمل: '{key}' = {value}")
                    break
            
            # البحث عن عمود نوع الاشتراك
            subscription_column = None
            for key, value in first_record.items():
                if str(value).lower() in ['free', 'paid', 'trail', 'premium']:
                    subscription_column = key
                    print(f"   📋 عمود نوع الاشتراك: '{key}' = {value}")
                    break
            
            # البحث عن عمود العدد
            count_column = None
            for key, value in first_record.items():
                if isinstance(value, int) and 0 <= value <= 1000:
                    count_column = key
                    print(f"   🔢 عمود العدد المحتمل: '{key}' = {value}")
                    break
            
            return {
                'headers': headers,
                'total_records': len(all_records),
                'user_id_column': user_id_column,
                'subscription_column': subscription_column,
                'count_column': count_column,
                'sample_record': first_record
            }
        
        return None
        
    except Exception as e:
        print(f"❌ خطأ في تحليل البنية: {e}")
        return None

def create_proper_user_sheet():
    """إنشاء جدول مستخدمين بالبنية الصحيحة"""
    try:
        from auth import open_google_sheet
        
        print("\n🔧 إنشاء جدول مستخدمين بالبنية الصحيحة...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        # تحليل البيانات الحالية
        analysis = analyze_current_structure()
        if not analysis:
            print("❌ فشل في تحليل البيانات الحالية")
            return False
        
        # إنشاء جدول جديد للمستخدمين
        try:
            # محاولة إنشاء ورقة جديدة
            spreadsheet = sheet2.spreadsheet
            new_sheet = spreadsheet.add_worksheet(title="users_data", rows="1000", cols="10")
            print("✅ تم إنشاء ورقة جديدة: users_data")
        except:
            # إذا كانت الورقة موجودة، استخدمها
            try:
                new_sheet = spreadsheet.worksheet("users_data")
                print("✅ تم العثور على ورقة users_data موجودة")
            except:
                print("❌ فشل في إنشاء أو العثور على ورقة users_data")
                return False
        
        # إضافة الرؤوس الصحيحة
        correct_headers = [
            'user_id', 'first_name', 'last_name', 'subscription_type', 
            'count', 'end_date', 'created_date', 'last_activity'
        ]
        
        # مسح المحتوى الحالي وإضافة الرؤوس
        new_sheet.clear()
        new_sheet.insert_row(correct_headers, 1)
        print(f"✅ تم إضافة الرؤوس: {correct_headers}")
        
        # تحويل البيانات الحالية إلى البنية الجديدة
        all_records = sheet2.get_all_records()
        converted_count = 0
        
        print(f"🔄 تحويل {len(all_records)} سجل...")
        
        for record in all_records[:50]:  # تحويل أول 50 سجل للاختبار
            try:
                # استخراج البيانات من البنية القديمة
                user_id = None
                subscription_type = 'free'
                count = 0
                first_name = ''
                last_name = ''
                
                # البحث عن معرف المستخدم
                for key, value in record.items():
                    if isinstance(value, (int, str)) and str(value).isdigit() and len(str(value)) >= 8:
                        user_id = str(value)
                        break
                
                # البحث عن نوع الاشتراك
                for key, value in record.items():
                    if str(value).lower() in ['free', 'paid', 'trail', 'premium']:
                        subscription_type = str(value).lower()
                        break
                
                # البحث عن العدد
                for key, value in record.items():
                    if isinstance(value, int) and 0 <= value <= 1000:
                        count = value
                        break
                
                # البحث عن الأسماء
                name_values = []
                for key, value in record.items():
                    if isinstance(value, str) and value and not value.isdigit() and len(value) > 1:
                        name_values.append(value)
                
                if name_values:
                    first_name = name_values[0] if len(name_values) > 0 else ''
                    last_name = name_values[1] if len(name_values) > 1 else ''
                
                # إضافة السجل إذا كان معرف المستخدم صحيح
                if user_id and len(user_id) >= 8:
                    new_row = [
                        user_id,
                        first_name,
                        last_name,
                        subscription_type,
                        count,
                        '',  # end_date
                        datetime.date.today().strftime('%Y-%m-%d'),  # created_date
                        datetime.date.today().strftime('%Y-%m-%d')   # last_activity
                    ]
                    
                    new_sheet.append_row(new_row)
                    converted_count += 1
                    
                    if converted_count % 10 == 0:
                        print(f"   تم تحويل {converted_count} سجل...")
                
            except Exception as e:
                logger.warning(f"خطأ في تحويل سجل: {e}")
                continue
        
        print(f"✅ تم تحويل {converted_count} سجل بنجاح")
        
        # إضافة مستخدمين تجريبيين
        test_users = [
            ['111111111', 'أحمد', 'محمد', 'free', 3, '', datetime.date.today().strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d')],
            ['222222222', 'فاطمة', 'علي', 'free', 1, '', datetime.date.today().strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d')],
            ['333333333', 'محمد', 'حسن', 'free', 5, '', datetime.date.today().strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d')],
            ['444444444', 'مريم', 'أحمد', 'trail', 2, (datetime.date.today() - datetime.timedelta(days=5)).strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d')],
            ['555555555', 'خالد', 'سالم', 'free', 0, '', datetime.date.today().strftime('%Y-%m-%d'), datetime.date.today().strftime('%Y-%m-%d')]
        ]
        
        for user in test_users:
            new_sheet.append_row(user)
        
        print(f"✅ تم إضافة {len(test_users)} مستخدم تجريبي")
        
        return True, new_sheet
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول المستخدمين: {e}")
        return False, None

def update_free_users_manager():
    """تحديث FreeUsersManager لاستخدام الجدول الجديد"""
    try:
        print("\n🔧 تحديث FreeUsersManager...")
        
        # إنشاء نسخة محدثة من free_users_manager
        updated_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشتركين المجانيين وإرسال أكواد البرومو - محدث
Free Users Management and Promo Code Distribution System - Updated
"""

import logging
import datetime
from typing import List, Dict, Optional
from auth import open_google_sheet, bot
from user_limit import UserManager
from promo_codes import PromoCodeManager

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

# محاولة استخدام جدول المستخدمين الجديد
try:
    users_sheet = sheet2.spreadsheet.worksheet("users_data")
    print("✅ تم العثور على جدول users_data")
except:
    users_sheet = sheet2  # استخدام الجدول الافتراضي
    print("⚠️ استخدام الجدول الافتراضي")

class FreeUsersManager:
    """مدير المشتركين المجانيين - محدث"""
    
    @staticmethod
    def get_all_free_users() -> List[Dict]:
        """الحصول على جميع المشتركين المجانيين"""
        try:
            all_records = users_sheet.get_all_records()
            free_users = []
            
            for record in all_records:
                user_id = str(record.get('user_id', ''))
                subscription_type = record.get('subscription_type', 'free')
                end_date_str = record.get('end_date', '')
                
                if not user_id or len(user_id) < 8:
                    continue
                
                # التحقق من انتهاء الاشتراك
                is_expired = False
                if subscription_type in ['trail', 'paid'] and end_date_str:
                    try:
                        end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                        is_expired = datetime.date.today() > end_date
                    except ValueError:
                        is_expired = True
                
                # إضافة المستخدمين المجانيين أو المنتهي اشتراكهم
                if subscription_type == 'free' or is_expired:
                    free_users.append({
                        'user_id': user_id,
                        'subscription_type': subscription_type,
                        'end_date': end_date_str,
                        'first_name': record.get('first_name', ''),
                        'last_name': record.get('last_name', ''),
                        'count': int(record.get('count', 0)),
                        'is_expired': is_expired
                    })
            
            logger.info(f"تم العثور على {len(free_users)} مستخدم مجاني")
            return free_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين المجانيين: {e}")
            return []
'''
        
        # حفظ النسخة المحدثة
        with open('free_users_manager_updated.py', 'w', encoding='utf-8') as f:
            f.write(updated_code)
        
        print("✅ تم إنشاء free_users_manager_updated.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث FreeUsersManager: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح بنية بيانات المستخدمين")
    print("=" * 50)
    
    # 1. تحليل البنية الحالية
    analysis = analyze_current_structure()
    
    if not analysis:
        print("❌ فشل في تحليل البيانات")
        return
    
    print(f"📊 تم العثور على {analysis['total_records']} سجل")
    
    # 2. إنشاء جدول بالبنية الصحيحة
    success, new_sheet = create_proper_user_sheet()
    
    if success:
        print("✅ تم إنشاء جدول المستخدمين بالبنية الصحيحة")
        
        # 3. اختبار النظام الجديد
        print("\n🧪 اختبار النظام الجديد...")
        
        try:
            # اختبار جلب البيانات من الجدول الجديد
            test_records = new_sheet.get_all_records()
            free_count = 0
            
            for record in test_records:
                subscription_type = record.get('subscription_type', 'free')
                user_id = str(record.get('user_id', ''))
                
                if user_id and len(user_id) >= 8 and subscription_type == 'free':
                    free_count += 1
            
            print(f"✅ تم العثور على {free_count} مستخدم مجاني في الجدول الجديد")
            
            if free_count > 0:
                print("🎉 النظام جاهز لإرسال أكواد البرومو!")
                print("\n📋 للاستخدام:")
                print("   1. استخدم جدول 'users_data' بدلاً من الجدول الافتراضي")
                print("   2. قم بتحديث free_users_manager.py لاستخدام الجدول الجديد")
                print("   3. اختبر الأوامر: /list_free_users, /send_promo_active")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار النظام الجديد: {e}")
    
    else:
        print("❌ فشل في إنشاء جدول المستخدمين")

if __name__ == "__main__":
    main()
'''

        # حفظ النسخة المحدثة
        with open('free_users_manager_updated.py', 'w', encoding='utf-8') as f:
            f.write(updated_code)

        print("✅ تم إنشاء free_users_manager_updated.py")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث FreeUsersManager: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح بنية بيانات المستخدمين")
    print("=" * 50)

    # 1. تحليل البنية الحالية
    analysis = analyze_current_structure()

    if not analysis:
        print("❌ فشل في تحليل البيانات")
        return

    print(f"📊 تم العثور على {analysis['total_records']} سجل")

    # 2. إنشاء جدول بالبنية الصحيحة
    success, new_sheet = create_proper_user_sheet()

    if success:
        print("✅ تم إنشاء جدول المستخدمين بالبنية الصحيحة")

        # 3. اختبار النظام الجديد
        print("\n🧪 اختبار النظام الجديد...")

        try:
            # اختبار جلب البيانات من الجدول الجديد
            test_records = new_sheet.get_all_records()
            free_count = 0

            for record in test_records:
                subscription_type = record.get('subscription_type', 'free')
                user_id = str(record.get('user_id', ''))

                if user_id and len(user_id) >= 8 and subscription_type == 'free':
                    free_count += 1

            print(f"✅ تم العثور على {free_count} مستخدم مجاني في الجدول الجديد")

            if free_count > 0:
                print("🎉 النظام جاهز لإرسال أكواد البرومو!")
                print("\n📋 للاستخدام:")
                print("   1. استخدم جدول 'users_data' بدلاً من الجدول الافتراضي")
                print("   2. قم بتحديث free_users_manager.py لاستخدام الجدول الجديد")
                print("   3. اختبر الأوامر: /list_free_users, /send_promo_active")

        except Exception as e:
            print(f"❌ خطأ في اختبار النظام الجديد: {e}")

    else:
        print("❌ فشل في إنشاء جدول المستخدمين")

if __name__ == "__main__":
    main()
