import requests
import json
import argparse
import sys

def test_webhook(url, stock_code="TEST001", report_type="buy", buy_price="10.5", 
                tp1="11.0", tp2="11.5", tp3="12.0", sl="10.0", verbose=False):
    """
    Test the webhook functionality with sample data
    """
    # Prepare payload based on report type
    if report_type == "buy":
        payload = {
            "report": report_type,
            "stock_code": stock_code,
            "buy_price": buy_price,
            "tp1": tp1,
            "tp2": tp2,
            "tp3": tp3,
            "sl": sl
        }
    elif report_type == "t1done":
        payload = {
            "report": report_type,
            "stock_code": stock_code,
            "sl": sl
        }
    elif report_type in ["t2done", "t3done"]:
        payload = {
            "report": report_type,
            "stock_code": stock_code,
            "sl": sl
        }
    elif report_type == "tsl":
        payload = {
            "report": report_type,
            "stock_code": stock_code,
            "sl": sl
        }
    elif report_type == "sell":
        payload = {
            "report": report_type,
            "stock_code": stock_code,
            "sell_price": buy_price  # Reusing buy_price for sell_price
        }
    else:
        print(f"Error: Unknown report type '{report_type}'")
        return
    
    # Print debugging info if verbose
    if verbose:
        print(f"Testing webhook URL: {url}")
        print(f"Sending payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Send the POST request
        response = requests.post(url, json=payload)
        
        # Print response details
        print(f"\nResponse status code: {response.status_code}")
        if response.status_code == 200:
            print("✅ Webhook test successful!")
        else:
            print(f"❌ Webhook test failed with status code {response.status_code}")
        
        if verbose:
            print(f"Response headers: {dict(response.headers)}")
        
        # Try to parse and print JSON response
        try:
            json_response = response.json()
            print(f"Response body: {json.dumps(json_response, indent=2)}")
        except:
            # If not JSON, print raw text
            print(f"Response body: {response.text}")
        
    except Exception as e:
        print(f"Error while testing webhook: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test Stock Analyzer Webhook')
    parser.add_argument('url', type=str, help='Webhook URL to test')
    parser.add_argument('--code', '-c', type=str, default="TEST001", help='Stock code')
    parser.add_argument('--type', '-t', type=str, default="buy", 
                        choices=["buy", "t1done", "t2done", "t3done", "tsl", "sell"],
                        help='Report type')
    parser.add_argument('--price', '-p', type=str, default="10.5", help='Buy/Sell price')
    parser.add_argument('--tp1', type=str, default="11.0", help='Target price 1')
    parser.add_argument('--tp2', type=str, default="11.5", help='Target price 2')
    parser.add_argument('--tp3', type=str, default="12.0", help='Target price 3')
    parser.add_argument('--sl', type=str, default="10.0", help='Stop loss')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    test_webhook(args.url, args.code, args.type, args.price, 
                args.tp1, args.tp2, args.tp3, args.sl, args.verbose)
