# 🔧 إصلاح سريع لمشكلة worksheet في promo_codes.py

## المشكلة:
```python
AttributeError: 'Worksheet' object has no attribute 'add_worksheet'
```

## السبب:
- نحاول استخدام `sheet3.add_worksheet()` بينما `sheet3` هو worksheet وليس spreadsheet
- للإضافة worksheet جديد نحتاج للـ spreadsheet object

## الحل السريع:

### 1. إنشاء worksheet يدوياً:
1. افتح Google Sheets في المتصفح
2. اذهب إلى ملف "stock" 
3. أضف worksheet جديد بالاسم `promo_codes`
4. أضف العناوين التالية في الصف الأول:

| A | B | C | D | E | F | G | H | I | J |
|---|---|---|---|---|---|---|---|---|---|
| كود البرومو | نوع الكود | قيمة الخصم/الأيام | مستخدم بواسطة | تاريخ الاستخدام | تاريخ الإنشاء | حالة الكود | تاريخ الانتهاء | عدد مرات الاستخدام | ملاحظات |

### 2. استخدام الكود المُصحح:

تم تحديث `promo_codes.py` ليتعامل مع هذه المشكلة:
- إذا لم يجد worksheet `promo_codes` سيحاول إنشاؤه
- إذا فشل في الإنشاء سيعطي رسالة خطأ واضحة
- إضافة حماية في جميع الدوال للتعامل مع `promo_sheet = None`

### 3. تحديث auth.py (اختياري):

إذا كنت تريد حل أكثر اكتمالاً، يمكن تحديث دالة `open_google_sheet` لترجع الـ spreadsheet أيضاً:

```python
def open_google_sheet(sheet_name):
    # ... existing code ...
    
    try:
        client = gspread.authorize(credentials)
        spreadsheet = client.open(sheet_name)  # إضافة هذا السطر
        sheet = spreadsheet.worksheet('Sheet1')
        sheet2 = spreadsheet.worksheet("users")
        sheet3 = spreadsheet.worksheet("Sheet4")
    except gspread.exceptions.SpreadsheetNotFound:
        logging.error('sheet not found')
        raise
    except gspread.exceptions.APIError:
        logging.error('invalid credentials')
        raise

    return sheet, sheet2, sheet3, spreadsheet  # إضافة spreadsheet للمرجع
```

## الحل المؤقت الحالي:

الكود الآن يتعامل مع الخطأ بذكاء:
- ✅ يحاول الوصول للـ worksheet الموجود
- ✅ إذا لم يجده يحاول إنشاؤه (قد يفشل)
- ✅ إذا فشل يعطي رسائل خطأ واضحة
- ✅ جميع الدوال محمية ولن تُحدث crash

## للاختبار:

1. **قم بإنشاء worksheet `promo_codes` يدوياً** (الطريقة الأسرع)
2. أو اتبع خطوات تحديث `auth.py` أعلاه
3. شغل البوت مرة أخرى

الآن النظام سيعمل بدون مشاكل! 🚀
