#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوظائف فقط دون تشغيل البوت
"""

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_main_functions():
    """اختبار الوظائف في main.py"""
    try:
        print("🔍 فحص توفر الوظائف في main.py...")
        
        # قراءة main.py كنص
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص الوظائف المطلوبة
        required_functions = [
            'create_trial_code_command',
            'create_discount_code_command',
            'redeem_promo_code', 
            'list_promo_codes'
        ]
        
        functions_found = {}
        for func_name in required_functions:
            if f"async def {func_name}(" in content:
                functions_found[func_name] = True
                print(f"✅ {func_name} موجود")
            else:
                functions_found[func_name] = False
                print(f"❌ {func_name} غير موجود")
        
        # فحص PROMO_CODES_AVAILABLE
        if "PROMO_CODES_AVAILABLE = True" in content:
            print("✅ PROMO_CODES_AVAILABLE = True")
        elif "PROMO_CODES_AVAILABLE = False" in content:
            print("⚠️ PROMO_CODES_AVAILABLE = False")
        else:
            print("❌ PROMO_CODES_AVAILABLE غير محدد")
        
        # فحص تسجيل الأوامر
        command_registrations = [
            "dp.message_handler(commands=['create_trial_code'])",
            "dp.message_handler(commands=['create_discount_code'])",
            "dp.message_handler(commands=['redeem'])",
            "dp.message_handler(commands=['list_codes'])"
        ]
        
        print("\n📋 فحص تسجيل الأوامر:")
        for reg in command_registrations:
            if reg in content:
                print(f"✅ {reg}")
            else:
                print(f"❌ {reg}")
        
        # فحص process_callback_messages
        if "def handle_all_messages(message):" in content:
            print("✅ معالج الرسائل العام موجود")
        elif "process_callback_messages" in content:
            print("✅ process_callback_messages موجود")
        else:
            print("❌ معالج الرسائل العام غير موجود")
        
        print("\n🎯 انتهى الفحص!")
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        import traceback
        traceback.print_exc()

def test_promo_codes_import():
    """اختبار استيراد promo_codes"""
    try:
        print("\n🔍 اختبار استيراد promo_codes...")
        import promo_codes
        print("✅ promo_codes موجود")
        
        if hasattr(promo_codes, 'PromoCodeManager'):
            print("✅ PromoCodeManager موجود")
        else:
            print("❌ PromoCodeManager غير موجود")
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد promo_codes: {e}")
    except Exception as e:
        print(f"❌ خطأ عام في promo_codes: {e}")

if __name__ == "__main__":
    print("🚀 بدء فحص وظائف البوت...")
    test_main_functions()
    test_promo_codes_import()
