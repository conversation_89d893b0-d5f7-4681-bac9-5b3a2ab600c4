import os
from flask import Flask
from security import security_middleware
from setup_dirs import setup_directories
import logging

logger = logging.getLogger(__name__)

def create_app():
    """Create and configure the Flask application"""
    # Set up directories first
    templates_dir, static_dir = setup_directories()
    
    # Create Flask app with correct template and static paths
    app = Flask(__name__, 
                template_folder=templates_dir,
                static_folder=static_dir)
    
    # Apply security middleware
    app = security_middleware(app)
    
    # Configure app
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'default-dev-key')
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # Limit upload size to 16 MB
    
    # Return configured app
    return app
