#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لحل مشكلة worksheet في نظام الأكواد
"""

import os
import sys
import logging

# إعداد الـ logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """اختبار استيراد الملفات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from user_limit import UserManager, PROMO_CODES_AVAILABLE
        print(f"✅ user_limit.py: تم بنجاح - PROMO_CODES_AVAILABLE = {PROMO_CODES_AVAILABLE}")
    except Exception as e:
        print(f"❌ user_limit.py: خطأ - {e}")
        return False
    
    try:
        import main
        print("✅ main.py: تم بنجاح")
    except Exception as e:
        print(f"❌ main.py: خطأ - {e}")
        return False
    
    try:
        from promo_codes import PromoCodeManager
        print("✅ promo_codes.py: تم بنجاح")
        return True
    except Exception as e:
        print(f"❌ promo_codes.py: خطأ - {e}")
        return False

def test_promo_codes_functions():
    """اختبار وظائف الأكواد"""
    print("\n🧪 اختبار وظائف الأكواد...")
    
    try:
        from promo_codes import PromoCodeManager
        
        # اختبار إنشاء كود تجربة
        trial_code = PromoCodeManager.create_trial_code(days=7, expiry_days=30, note="كود اختبار")
        if trial_code:
            print(f"✅ إنشاء كود تجربة: {trial_code}")
        else:
            print("⚠️ إنشاء كود تجربة: فشل (worksheet غير متاح)")
          # اختبار إنشاء كود خصم
        discount_code = PromoCodeManager.create_discount_code(discount_percent=50, expiry_days=15, note="كود خصم اختبار")
        if discount_code:
            print(f"✅ إنشاء كود خصم: {discount_code}")
        else:
            print("⚠️ إنشاء كود خصم: فشل (worksheet غير متاح)")
        
        # اختبار عرض الأكواد
        codes = PromoCodeManager.list_active_codes()
        if codes is not None:
            print(f"✅ عرض الأكواد: {len(codes)} كود نشط")
        else:
            print("⚠️ عرض الأكواد: فشل (worksheet غير متاح)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        return False

def test_user_functions():
    """اختبار وظائف المستخدم"""
    print("\n👤 اختبار وظائف المستخدم...")
    
    try:
        from user_limit import UserManager
        
        # اختبار المستخدم
        test_user_id = 123456789
        user_manager = UserManager()
        
        # اختبار الحصول على بيانات المستخدم
        user_data = user_manager.get_user_data(test_user_id)
        print(f"✅ بيانات المستخدم: {user_data}")        # اختبار الحصول على حالة المستخدم
        import asyncio
        from user_limit import get_user_status
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            user_status, daily_count = loop.run_until_complete(get_user_status(str(test_user_id)))
            print(f"✅ حالة المستخدم: {user_status}, العدد اليومي: {daily_count}")
        except Exception as e:
            print(f"⚠️ خطأ في حالة المستخدم: {e}")
        finally:
            loop.close()
          # اختبار فحص إمكانية استخدام الخدمة المجانية  
        try:
            # محاولة استخدام دالة متزامنة
            can_use = user_manager.can_use_free_service(test_user_id)
            print(f"✅ يمكن استخدام الخدمة المجانية: {can_use}")
        except AttributeError:
            print("⚠️ دالة can_use_free_service غير موجودة - هذا طبيعي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف المستخدم: {e}")
        return False

def check_google_sheets_connection():
    """فحص الاتصال بـ Google Sheets"""
    print("\n📊 فحص الاتصال بـ Google Sheets...")
    
    try:
        import gspread
        import json
        from oauth2client.service_account import ServiceAccountCredentials
        
        # فحص وجود ملف الاعتمادات
        if not os.path.exists('json_file.json'):
            print("❌ ملف json_file.json غير موجود")
            return False
        
        print("✅ ملف الاعتمادات موجود")
        
        # محاولة الاتصال
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        with open('json_file.json', 'r') as f:
            creds = json.load(f)
        
        credentials = ServiceAccountCredentials.from_json_keyfile_dict(creds, scope)
        client = gspread.authorize(credentials)
        print("✅ الاتصال بـ Google Sheets API نجح")
        
        # محاولة فتح الملف
        try:
            spreadsheet = client.open("stock")
            print("✅ فتح ملف 'stock' نجح")
            
            # فحص worksheet promo_codes
            try:
                promo_sheet = spreadsheet.worksheet("promo_codes")
                print("✅ worksheet 'promo_codes' موجود")
                return True
            except gspread.exceptions.WorksheetNotFound:
                print("⚠️ worksheet 'promo_codes' غير موجود - سيتم إنشاؤه تلقائياً")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في فتح ملف 'stock': {e}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Google Sheets: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار حل مشكلة worksheet في نظام الأكواد")
    print("=" * 60)
    
    # اختبار الاتصال بـ Google Sheets
    sheets_ok = check_google_sheets_connection()
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار وظائف المستخدم
    user_functions_ok = test_user_functions()
    
    # اختبار وظائف الأكواد
    promo_functions_ok = test_promo_codes_functions()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print(f"📊 Google Sheets: {'✅' if sheets_ok else '❌'}")
    print(f"📦 الاستيرادات: {'✅' if imports_ok else '❌'}")
    print(f"👤 وظائف المستخدم: {'✅' if user_functions_ok else '❌'}")
    print(f"🎫 وظائف الأكواد: {'✅' if promo_functions_ok else '⚠️'}")
    
    if imports_ok and user_functions_ok:
        print("\n🎉 النظام يعمل بشكل أساسي!")
        if promo_functions_ok:
            print("🎫 نظام الأكواد يعمل بالكامل!")
        else:
            print("⚠️ نظام الأكواد غير متاح (يمكن إنشاء worksheet يدوياً)")
    else:
        print("\n❌ هناك مشاكل تحتاج حل")
    
    print("\n📚 للمساعدة راجع: PROMO_CODES_ERROR_FIX_REPORT.md")

if __name__ == "__main__":
    main()
