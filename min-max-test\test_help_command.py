#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أمر المساعدة المحدث
Test updated help command
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock

async def test_help_command():
    """اختبار أمر المساعدة"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("🧪 اختبار أمر المساعدة...")
        
        # محاكاة رسالة من المستخدم
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 123456789
        mock_message.text = "/help"
        
        # محاكاة معالجة الرسالة
        try:
            # محاكاة استدعاء reply مع رسالة المساعدة
            await mock_message.reply(COMPREHENSIVE_HELP, parse_mode="Markdown")
            print("✅ تم إرسال رسالة المساعدة بنجاح")
            
            # التحقق من محتوى الرسالة
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]  # الرسالة المرسلة
            parse_mode = call_args[1]['parse_mode']  # نمط التحليل
            
            print(f"📏 طول الرسالة المرسلة: {len(sent_message)} حرف")
            print(f"🔧 نمط التحليل: {parse_mode}")
            
            # التحقق من وجود المحتوى المطلوب
            required_content = [
                "أكواد الخصم والعروض",
                "/redeem",
                "تفعيل كود خصم أو تجربة مجانية",
                "TRIAL7FREE"
            ]
            
            missing_content = []
            for content in required_content:
                if content not in sent_message:
                    missing_content.append(content)
            
            if missing_content:
                print("⚠️ محتوى مفقود:")
                for content in missing_content:
                    print(f"   - {content}")
                return False
            else:
                print("✅ جميع المحتوى المطلوب موجود")
                return True
                
        except Exception as e:
            print(f"❌ خطأ في معالجة الرسالة: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار أمر المساعدة: {e}")
        return False

async def test_redeem_explanation():
    """اختبار شرح أمر redeem في المساعدة"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("\n🧪 اختبار شرح أمر redeem...")
        
        # البحث عن قسم أكواد الخصم
        lines = COMPREHENSIVE_HELP.split('\n')
        redeem_section_found = False
        redeem_explanation_found = False
        example_found = False
        
        for line in lines:
            if "أكواد الخصم والعروض" in line:
                redeem_section_found = True
                print("✅ تم العثور على قسم أكواد الخصم")
            
            if "/redeem" in line and "تفعيل" in line:
                redeem_explanation_found = True
                print("✅ تم العثور على شرح أمر /redeem")
            
            if "TRIAL7FREE" in line or "مثال:" in line:
                example_found = True
                print("✅ تم العثور على مثال للاستخدام")
        
        # التحقق من وجود جميع العناصر المطلوبة
        if redeem_section_found and redeem_explanation_found and example_found:
            print("✅ شرح أمر /redeem مكتمل ومفصل")
            return True
        else:
            print("❌ شرح أمر /redeem غير مكتمل")
            if not redeem_section_found:
                print("   - قسم أكواد الخصم مفقود")
            if not redeem_explanation_found:
                print("   - شرح أمر /redeem مفقود")
            if not example_found:
                print("   - مثال الاستخدام مفقود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار شرح redeem: {e}")
        return False

async def test_message_structure():
    """اختبار بنية الرسالة"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("\n🧪 اختبار بنية رسالة المساعدة...")
        
        # التحقق من الأقسام المطلوبة
        required_sections = [
            "الأوامر الأساسية",
            "أكواد الخصم والعروض",
            "تحليل الأسهم",
            "تحليلات السيولة",
            "أسهم المتاجرة",
            "أدوات فنية",
            "الصفقات والأهداف"
        ]
        
        found_sections = []
        missing_sections = []
        
        for section in required_sections:
            if section in COMPREHENSIVE_HELP:
                found_sections.append(section)
                print(f"✅ {section}")
            else:
                missing_sections.append(section)
                print(f"❌ {section}")
        
        print(f"\n📊 النتيجة: {len(found_sections)}/{len(required_sections)} قسم موجود")
        
        if missing_sections:
            print("⚠️ أقسام مفقودة:")
            for section in missing_sections:
                print(f"   - {section}")
            return False
        else:
            print("✅ جميع الأقسام المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار بنية الرسالة: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار أمر المساعدة المحدث")
    print("=" * 50)
    
    tests = [
        ("اختبار أمر المساعدة", test_help_command),
        ("اختبار شرح أمر redeem", test_redeem_explanation),
        ("اختبار بنية الرسالة", test_message_structure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed == len(results):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ أمر /help محدث ويعمل بشكل صحيح")
        print("✅ شرح أمر /redeem مضاف ومفصل")
        print("✅ رسالة المساعدة جاهزة للاستخدام")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة رسالة المساعدة")

if __name__ == "__main__":
    asyncio.run(main())
