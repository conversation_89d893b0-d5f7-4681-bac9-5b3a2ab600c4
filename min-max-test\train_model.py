import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import <PERSON>
import os
from pathlib import Path
import sys
# Add parent directory to path to import config
sys.path.append(str(Path(__file__).parent.parent))
from config import chart_data_dir  # Import path from config

def prepare_data(df, lookback=60):
    """Prepare data for LSTM model"""
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(df[['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL']])
    
    X, y = [], []
    for i in range(lookback, len(scaled_data)):
        X.append(scaled_data[i-lookback:i])
        y.append(scaled_data[i, 3])  # Predict CLOSE price
    
    return np.array(X), np.array(y), scaler

def create_model(input_shape):
    """Create LSTM model with proper gradient handling"""
    model = Sequential([
        # First LSTM layer with input shape and return sequences
        LSTM(128, 
             input_shape=input_shape,
             return_sequences=True,
             activation='tanh',
             recurrent_activation='sigmoid',
             stateful=False),
        Dropout(0.2),
        
        # Second LSTM layer
        LSTM(64,
             return_sequences=False,
             activation='tanh',
             recurrent_activation='sigmoid',
             stateful=False),
        Dropout(0.2),
        
        # Dense layers for output
        Dense(32, activation='relu'),
        Dense(16, activation='relu'),
        Dense(1, activation='linear')
    ])
    
    # Use Adam optimizer with custom learning rate
    optimizer = Adam(learning_rate=0.001)
    
    # Compile with MSE loss and additional metrics
    model.compile(
        optimizer=optimizer,
        loss='mse',
        metrics=['mae', 'mse'],
        run_eagerly=True  # This helps prevent placeholder issues
    )
    
    return model

def train_model(stock_data_dir=chart_data_dir):
    """Train model with improved error handling"""
    try:
        # Create models directory if it doesn't exist
        models_dir = Path(__file__).parent / "models"
        models_dir.mkdir(exist_ok=True)
        
        # Combine data from all stocks
        all_data = []
        for file in os.listdir(chart_data_dir):
            if file.endswith('D.TXT'):  # Only process daily data files
                try:
                    # Read file with proper format handling
                    df = pd.read_csv(
                        os.path.join(stock_data_dir, file),
                        skiprows=1,  # Skip header row
                        names=['TICKER', 'PER', 'DTYYYYMMDD', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OPENINT'],
                        dtype={
                            'TICKER': str,
                            'PER': str,
                            'DTYYYYMMDD': str,
                            'TIME': str,
                            'OPEN': float,
                            'HIGH': float,
                            'LOW': float,
                            'CLOSE': float,
                            'VOL': float,
                            'OPENINT': float
                        }
                    )
                    
                    # Convert date to datetime
                    df['DATE'] = pd.to_datetime(df['DTYYYYMMDD'], format='%Y%m%d')
                    
                    # Sort by date
                    df = df.sort_values('DATE')
                    
                    # Remove any rows with zeros or invalid values
                    df = df[
                        (df['OPEN'] > 0) & 
                        (df['HIGH'] > 0) & 
                        (df['LOW'] > 0) & 
                        (df['CLOSE'] > 0) & 
                        (df['VOL'] > 0)
                    ]
                    
                    if not df.empty:
                        all_data.append(df)
                        print(f"Processed {file} - {len(df)} rows")
                except Exception as e:
                    print(f"Error processing {file}: {e}")
                    continue
        
        if not all_data:
            raise ValueError("No valid data files found")
            
        combined_data = pd.concat(all_data, ignore_index=True)
        print(f"Total combined rows: {len(combined_data)}")
        
        # Prepare data
        X, y, scaler = prepare_data(combined_data)
        
        # Split data
        train_size = int(len(X) * 0.8)
        X_train, X_test = X[:train_size], X[train_size:]
        y_train, y_test = y[:train_size], y[train_size:]
        
        # Create and train model with early stopping
        from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
        
        model = create_model((X.shape[1], X.shape[2]))
        
        # Add callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            ),
            ModelCheckpoint(
                filepath=models_dir / 'best_model.h5',
                monitor='val_loss',
                save_best_only=True
            )
        ]
        
        # Train with validation split
        history = model.fit(
            X_train, y_train,
            validation_data=(X_test, y_test),
            epochs=50,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )
        
        # Save final model
        model.save(models_dir / 'stock_model.h5', save_format='h5')
        print(f"Model saved successfully")
        
    except Exception as e:
        print(f"Error training model: {str(e)}")

if __name__ == "__main__":
    train_model()  # Use STOCK_DATA_DIR from config by default
