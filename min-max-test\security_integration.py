"""
Integrates security middleware with the Flask application
"""
import logging
from flask import request, g
from middleware import rate_limit, sanitize_input, RateLimiter
from functools import wraps
from user_limit import check_user_limit, get_user_status
import re

logger = logging.getLogger(__name__)

# Create separate rate limiters for different user tiers
free_rate_limiter = RateLimiter(limit=30, window=60)  # 30 requests per minute
paid_rate_limiter = RateLimiter(limit=120, window=60)  # 120 requests per minute
admin_rate_limiter = RateLimiter(limit=300, window=60)  # 300 requests per minute

def tier_based_rate_limit(f):
    """Rate limiting based on user subscription tier"""
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        try:
            # Get user ID from request
            user_id = request.args.get('user_id')
            
            if not user_id:
                # Use IP for anonymous requests
                client_id = request.remote_addr
                if not free_rate_limiter.is_allowed(client_id):
                    logger.warning(f"Rate limit exceeded for anonymous client {client_id}")
                    return {"error": "Rate limit exceeded for anonymous users"}, 429
            else:
                # Check user subscription type
                subscriber_type, _ = await get_user_status(user_id=user_id)
                
                # Apply different rate limits based on subscription
                if subscriber_type == "admin":
                    limiter = admin_rate_limiter
                elif subscriber_type in ["paid", "trail"]:
                    limiter = paid_rate_limiter
                else:
                    limiter = free_rate_limiter
                    
                if not limiter.is_allowed(user_id):
                    logger.warning(f"Rate limit exceeded for user {user_id} (tier: {subscriber_type})")
                    return {"error": f"Rate limit exceeded for {subscriber_type} users"}, 429
                    
            # Store rate limit info in Flask g object for logging
            g.rate_limit_tier = subscriber_type if user_id else "anonymous"
            
            return await f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error in rate limiting: {e}")
            # In case of error, default to strictest rate limit
            client_id = request.remote_addr
            if not free_rate_limiter.is_allowed(client_id):
                return {"error": "Rate limit exceeded"}, 429
            return await f(*args, **kwargs)
    return decorated_function

def detect_suspicious_activity(f):
    """Detect and log suspicious API usage patterns"""
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        # Flag patterns that might indicate scraping or abuse
        suspicious = False
        reason = ""
        
        # Check user agent for suspicious patterns
        user_agent = request.headers.get('User-Agent', '')
        if not user_agent or re.search(r'(bot|curl|wget|python-requests|scrapy)', user_agent, re.IGNORECASE):
            suspicious = True
            reason = f"Suspicious user agent: {user_agent}"
            
        # Check for suspicious request patterns (multiple rapid requests)
        client_id = request.remote_addr
        
        # Check for unusual parameters
        for param, value in request.args.items():
            if len(value) > 500:
                suspicious = True
                reason = f"Unusually long parameter value: {param}"
            
        if suspicious:
            logger.warning(f"Suspicious activity detected from {client_id}: {reason}")
            # You could implement progressive penalties here
            
        response = await f(*args, **kwargs)
        return response
    return decorated_function

def secure_api_route(f):
    """Combined decorator for securing API routes"""
    @sanitize_input
    @tier_based_rate_limit
    @detect_suspicious_activity
    @wraps(f)
    async def secure_route(*args, **kwargs):
        return await f(*args, **kwargs)
    return secure_route

def apply_security_middleware(app):
    """Apply security middleware to the Flask application"""
    # Set security headers on all responses
    @app.after_request
    def set_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Content-Security-Policy'] = "default-src 'self'"
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        return response
        
    # Enable CORS for specific origins
    @app.after_request
    def add_cors_headers(response):
        # Replace with your allowed origins
        allowed_origins = ['http://localhost:3000', 'https://yourdomain.com'] 
        origin = request.headers.get('Origin')
        
        if origin in allowed_origins:
            response.headers['Access-Control-Allow-Origin'] = origin
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            
        return response
    
    # Log all API requests
    @app.before_request
    def log_request():
        logger.info(f"Request: {request.method} {request.path} from {request.remote_addr}")
    
    return app
