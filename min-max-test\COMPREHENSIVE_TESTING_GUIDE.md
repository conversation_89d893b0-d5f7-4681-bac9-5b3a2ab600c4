# 🧪 دليل اختبار نظام إشارات التداول الشامل
## Comprehensive Testing Guide for Trading Signals System

---

## 📋 نظرة عامة

تم إنشاء مجموعة شاملة من أدوات الاختبار والمراقبة لنظام إشارات التداول. هذا الدليل يوضح كيفية تشغيل جميع الاختبارات بطريقة منهجية ومنظمة.

---

## 🛠️ الأدوات المتاحة

### 1. **SYSTEM_TESTING_SUITE.py** - مجموعة الاختبارات الأساسية
- اختبار الاتصال بالسيرفر
- اختبار Health Endpoint
- اختبار Webhook الأساسي والمتقدم
- اختبار التحقق من البيانات
- اختبار الأداء تحت الضغط
- اختبار Dashboard و API Endpoints
- اختبار معالجة الأخطاء

### 2. **ADVANCED_SYSTEM_TESTS.py** - الاختبارات المتقدمة
- اختبار دعم الرسائل العربية
- اختبار تكامل Google Sheets
- اختبار تكامل Telegram
- اختبار منطق التحقق من البيانات المعقد
- اختبار الإشارات المتزامنة
- اختبار نمط استخدام الذاكرة

### 3. **SYSTEM_HEALTH_MONITOR.py** - مراقب الصحة المستمر
- مراقبة مستمرة لصحة النظام
- فحص دوري لجميع المكونات
- إحصائيات الأداء والتشغيل
- نظام تنبيهات ذكي
- تقارير مفصلة

---

## 🚀 خطوات التشغيل والاختبار

### الخطوة 1: تشغيل السيرفر
```bash
# تشغيل السيرفر في الخلفية
python server.py &
# أو في Windows PowerShell
Start-Process python -ArgumentList "server.py" -WindowStyle Hidden
```

### الخطوة 2: التحقق من تشغيل السيرفر
```bash
# اختبار سريع للتأكد من تشغيل السيرفر
curl http://localhost:9000/health
# أو
python -c "import requests; print(requests.get('http://localhost:9000/health').status_code)"
```

### الخطوة 3: تشغيل الاختبارات الأساسية
```bash
python SYSTEM_TESTING_SUITE.py
```

### الخطوة 4: تشغيل الاختبارات المتقدمة
```bash
python ADVANCED_SYSTEM_TESTS.py
```

### الخطوة 5: بدء المراقبة المستمرة (اختياري)
```bash
python SYSTEM_HEALTH_MONITOR.py
# للإيقاف: Ctrl+C
```

---

## 📊 فهم النتائج

### رموز الحالة
- ✅ **نجح**: الاختبار مر بنجاح
- ⚠️ **تحذير**: الاختبار مر ولكن مع ملاحظات
- ❌ **فشل**: الاختبار فشل
- 🚨 **خطأ**: حدث خطأ غير متوقع

### ملفات النتائج
- `testing_results.json`: نتائج الاختبارات الأساسية
- `testing_results.log`: سجل مفصل للاختبارات الأساسية
- `advanced_test_results.json`: نتائج الاختبارات المتقدمة
- `monitoring_report.json`: تقرير المراقبة المستمرة
- `system_monitor.log`: سجل المراقبة المستمرة

---

## 🎯 سيناريوهات الاختبار المختلفة

### السيناريو 1: اختبار سريع (5 دقائق)
```bash
# تشغيل الاختبارات الأساسية فقط
python SYSTEM_TESTING_SUITE.py
```

### السيناريو 2: اختبار شامل (15 دقيقة)
```bash
# تشغيل جميع الاختبارات
python SYSTEM_TESTING_SUITE.py
python ADVANCED_SYSTEM_TESTS.py
```

### السيناريو 3: مراقبة مستمرة (ساعات/أيام)
```bash
# بدء المراقبة المستمرة
python SYSTEM_HEALTH_MONITOR.py
```

---

## 🔍 اختبارات محددة

### اختبار webhook يدوياً
```bash
# إرسال إشارة شراء
curl -X POST http://localhost:9000/webhook?jsonRequest=true \
  -H "Content-Type: application/json" \
  -d '{
    "stock_code": "TEST001",
    "report": "buy",
    "buy_price": "10.50",
    "tp1": "11.00",
    "tp2": "11.50",
    "tp3": "12.00",
    "sl": "10.00"
  }'

# إرسال إشارة بيع
curl -X POST http://localhost:9000/webhook?jsonRequest=true \
  -H "Content-Type: application/json" \
  -d '{
    "stock_code": "TEST002",
    "report": "sell",
    "sell_price": "15.00",
    "tp1": "14.50",
    "tp2": "14.00",
    "tp3": "13.50",
    "sl": "15.50"
  }'
```

### اختبار Dashboard
```bash
# الوصول لـ Dashboard
curl http://localhost:9000/dashboard?user_id=test_user
```

### اختبار API Endpoints
```bash
# اختبار Health API
curl http://localhost:9000/api/health

# اختبار تحليل السهم
curl http://localhost:9000/api/analyze?ticker=AAPL
```

---

## ⚠️ استكشاف الأخطاء الشائعة

### السيرفر لا يستجيب
```bash
# التحقق من تشغيل السيرفر
ps aux | grep python
# أو في Windows
tasklist | findstr python

# التحقق من البورت
netstat -an | grep 9000
# أو في Windows
netstat -an | findstr 9000
```

### أخطاء في الاختبارات
1. **Connection Refused**: السيرفر غير متشغل
2. **Timeout**: السيرفر بطيء أو معلق
3. **500 Internal Server Error**: خطأ في كود السيرفر
4. **400 Bad Request**: بيانات الإشارة غير صحيحة

### حلول سريعة
```bash
# إعادة تشغيل السيرفر
pkill -f server.py  # Linux/Mac
python server.py &

# أو في Windows
taskkill /F /IM python.exe
python server.py
```

---

## 📈 تحليل الأداء

### مؤشرات الأداء المهمة
- **وقت الاستجابة**: يجب أن يكون أقل من 5 ثواني
- **معدل النجاح**: يجب أن يكون أكثر من 90%
- **معدل التشغيل**: يجب أن يكون أكثر من 95%
- **استخدام الذاكرة**: يجب أن يكون مستقراً

### تقييم حالة النظام
- **ممتاز (90%+)**: النظام يعمل بشكل مثالي
- **جيد (75-89%)**: النظام يعمل بشكل جيد مع تحسينات بسيطة
- **مقبول (50-74%)**: النظام يعمل لكن يحتاج تحسينات
- **ضعيف (<50%)**: النظام يحتاج إصلاحات فورية

---

## 🛡️ اختبارات الأمان

### اختبارات تم تضمينها
- تحقق من معالجة JSON غير الصحيح
- اختبار البيانات الكبيرة (Payload Size)
- اختبار الأحرف الخاصة
- اختبار القيم الفارغة (Null Values)

### اختبارات إضافية يدوية
```bash
# اختبار SQL Injection (إذا كان هناك قاعدة بيانات)
curl -X POST http://localhost:9000/webhook?jsonRequest=true \
  -H "Content-Type: application/json" \
  -d '{"stock_code": "TEST"; DROP TABLE signals; --"}'

# اختبار XSS (إذا كان هناك عرض HTML)
curl -X POST http://localhost:9000/webhook?jsonRequest=true \
  -H "Content-Type: application/json" \
  -d '{"stock_code": "<script>alert(1)</script>"}'
```

---

## 📊 تقارير مفصلة

### محتويات التقرير الأساسي
- ملخص النتائج
- تفاصيل كل اختبار
- أوقات الاستجابة
- الأخطاء المكتشفة
- التوصيات

### محتويات تقرير المراقبة
- إحصائيات التشغيل
- معدلات الأداء
- تاريخ الأخطاء
- التنبيهات المرسلة
- اتجاهات الأداء

---

## 🔄 جدولة الاختبارات

### اختبارات يومية
```bash
# إضافة لـ crontab (Linux/Mac)
0 9 * * * cd /path/to/project && python SYSTEM_TESTING_SUITE.py
0 21 * * * cd /path/to/project && python ADVANCED_SYSTEM_TESTS.py

# أو Task Scheduler في Windows
```

### مراقبة مستمرة
```bash
# تشغيل المراقب كخدمة
nohup python SYSTEM_HEALTH_MONITOR.py > monitor.out 2>&1 &
```

---

## 🎯 نصائح للاختبار الفعال

1. **ابدأ بالاختبارات الأساسية** قبل المتقدمة
2. **راقب السيرفر أثناء الاختبار** لمراقبة استخدام الموارد
3. **احفظ نتائج الاختبارات** لمقارنتها لاحقاً
4. **اختبر في بيئات مختلفة** (تطوير، إنتاج)
5. **راجع السجلات بانتظام** لاكتشاف الأنماط

---

## 📞 الدعم واستكشاف الأخطاء

### ملفات السجلات المهمة
- `testing_results.log`: سجل الاختبارات
- `system_monitor.log`: سجل المراقبة
- `app.log`: سجل التطبيق (إذا وجد)
- `security.log`: سجل الأمان (إذا وجد)

### معلومات مفيدة للدعم
- إصدار Python
- نظام التشغيل
- نتائج الاختبارات
- رسائل الأخطاء الكاملة
- إعدادات النظام

---

## ✅ قائمة التحقق النهائية

- [ ] تشغيل السيرفر بنجاح
- [ ] اجتياز الاختبارات الأساسية (>90%)
- [ ] اجتياز الاختبارات المتقدمة (>75%)
- [ ] مراجعة ملفات السجلات
- [ ] التحقق من الأداء والاستقرار
- [ ] توثيق أي مشاكل مكتشفة
- [ ] التأكد من عمل المراقبة المستمرة

---

## 📋 الخلاصة

هذه المجموعة الشاملة من الأدوات توفر:
- **اختبارات تلقائية** لجميع جوانب النظام
- **مراقبة مستمرة** لضمان الاستقرار
- **تقارير مفصلة** لاتخاذ القرارات
- **أدوات تشخيص** لحل المشاكل بسرعة

تأكد من تشغيل هذه الاختبارات بانتظام للحفاظ على جودة وموثوقية نظام إشارات التداول.
