"""
Utilities for handling and sanitizing Markdown text to avoid parsing errors with Telegram
"""
import re
import logging

logger = logging.getLogger(__name__)

def escape_markdown_v2(text):
    """
    Escape special characters for Telegram's MarkdownV2 format.
    These characters need to be escaped: _ * [ ] ( ) ~ ` > # + - = | { } . !
    """
    if text is None:
        return ""
        
    escape_chars = r'_*[]()~`>#+-=|{}.!'
    return re.sub(f'([{re.escape(escape_chars)}])', r'\\\1', str(text))

def sanitize_markdown(text):
    """
    Sanitize markdown text to prevent parsing errors by properly escaping characters
    and ensuring all formatting markers are properly closed.
    """
    if text is None:
        return ""
        
    text = str(text)
    
    # Count asterisks and ensure they're balanced
    if text.count('*') % 2 != 0:
        # If unbalanced, escape all asterisks
        text = text.replace('*', '\\*')
    
    # Count backticks and ensure they're balanced
    if text.count('`') % 2 != 0:
        # If unbalanced, escape all backticks
        text = text.replace('`', '\\`')
    
    # Count underscores and ensure they're balanced
    if text.count('_') % 2 != 0:
        # If unbalanced, escape all underscores
        text = text.replace('_', '\\_')
    
    # Handle other problematic characters for markdown
    text = text.replace('[', '\\[').replace(']', '\\]')
    text = text.replace('(', '\\(').replace(')', '\\)')
    
    return text

def clean_analysis_text(analysis_text):
    """
    Clean analysis text specifically for stock analysis results.
    Preserves intended formatting while fixing markdown issues.
    """
    if analysis_text is None:
        return ""
        
    try:
        # Convert to string if not already
        analysis_text = str(analysis_text)
        
        # Most aggressive approach: Remove all markdown formatting
        # This will ensure the message gets delivered, even without formatting
        stripped_text = analysis_text.replace('*', '')
        stripped_text = stripped_text.replace('_', '')
        stripped_text = stripped_text.replace('`', '')
        stripped_text = stripped_text.replace('[', '')
        stripped_text = stripped_text.replace(']', '')
        
        # Try to send the message first without markdown
        logger.info("Analysis text stripped of all formatting")
        return stripped_text
        
    except Exception as e:
        logger.error(f"Error cleaning analysis text: {e}")
        # Return plain text with all special characters removed
        return re.sub(r'[*_`\[\]()]', '', analysis_text)
