import numpy as np
import pandas as pd
import talib
from talib import abstract
import logging

logger = logging.getLogger(__name__)

class PatternRecognizer:
    def __init__(self):
        self.patterns = {
            'CDL2CROWS': '2 Crows',
            'CDL3BLACKCROWS': '3 Black Crows',
            'CDLDARKCLOUDCOVER': 'Dark Cloud Cover',
            # Add more patterns as needed
        }
    
    def identify_patterns(self, df):
        """Identify candlestick patterns in the data"""
        results = {}
        
        if not TALIB_AVAILABLE:
            logger.warning("TA-Lib not available, patterns can't be identified")
            return {"error": "Pattern recognition requires TA-Lib"}
        
        try:
            # Ensure data is properly formatted for TA-Lib
            data = {
                'open': df['OPEN'].values,
                'high': df['HIGH'].values,
                'low': df['LOW'].values,
                'close': df['CLOSE'].values,
                'volume': df['VOL'].values if 'VOL' in df.columns else None
            }
            
            for pattern_func, pattern_name in self.patterns.items():
                pattern_func = getattr(talib, pattern_func)
                result = pattern_func(data['open'], data['high'], data['low'], data['close'])
                
                # Check the last 5 candles for patterns
                last_5 = result[-5:]
                if any(last_5 != 0):
                    # Found pattern in recent candles
                    for i, v in enumerate(last_5):
                        if v != 0:
                            bullish = v > 0
                            days_ago = 5 - i - 1
                            results[pattern_name] = {
                                'type': 'bullish' if bullish else 'bearish',
                                'days_ago': days_ago,
                                'strength': abs(v)  # Some patterns return strength values
                            }
            
            return results
            
        except Exception as e:
            logger.error(f"Error recognizing patterns: {e}")
            return {"error": f"Pattern recognition failed: {str(e)}"}
