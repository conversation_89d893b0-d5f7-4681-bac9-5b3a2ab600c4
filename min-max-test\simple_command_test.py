#!/usr/bin/env python3
"""
اختبار مبسط لكشف السبب الحقيقي لعدم عمل الأوامر
"""

def check_commands_in_main():
    """فحص الأوامر في main.py"""
    print("🔍 فحص تسجيل الأوامر في main.py...")
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # فحص أوامر البرومو
    promo_commands = ['create_trial_code', 'create_discount_code', 'redeem', 'list_codes']
    
    print("\n📋 أوامر البرومو في الكود:")
    for cmd in promo_commands:
        if f"commands=['{cmd}']" in content:
            print(f"✅ /{cmd} - موجود في الكود")
        else:
            print(f"❌ /{cmd} - غير موجود في الكود")
    
    # فحص أمر الاختبار
    if "commands=['test_promo']" in content:
        print("✅ /test_promo - موجود في الكود")
    else:
        print("❌ /test_promo - غير موجود في الكود")
    
    # فحص المعالج العام
    if "@dp.message_handler()" in content:
        print("✅ المعالج العام - موجود")
    else:
        print("❌ المعالج العام - غير موجود")
    
    # فحص ترتيب تسجيل الأوامر
    lines = content.split('\n')
    
    promo_line = None
    general_handler_line = None
    test_line = None
    
    for i, line in enumerate(lines):
        if 'create_trial_code' in line and 'dp.message_handler' in line:
            promo_line = i
        if '@dp.message_handler()' in line:
            general_handler_line = i  
        if 'test_promo' in line and 'dp.message_handler' in line:
            test_line = i
    
    print(f"\n📊 ترتيب تسجيل الأوامر:")
    if test_line:
        print(f"🧪 أمر الاختبار: السطر {test_line + 1}")
    if promo_line:
        print(f"🎫 أوامر البرومو: السطر {promo_line + 1}")
    if general_handler_line:
        print(f"📝 المعالج العام: السطر {general_handler_line + 1}")
    
    # تحديد المشكلة
    print(f"\n🎯 التحليل:")
    if test_line and general_handler_line:
        if test_line < general_handler_line:
            print("✅ أمر الاختبار مُسجل قبل المعالج العام - يجب أن يعمل")
        else:
            print("❌ أمر الاختبار مُسجل بعد المعالج العام - لن يعمل!")
    
    if promo_line and general_handler_line:
        if promo_line < general_handler_line:
            print("✅ أوامر البرومو مُسجلة قبل المعالج العام - يجب أن تعمل")
        else:
            print("❌ أوامر البرومو مُسجلة بعد المعالج العام - لن تعمل!")

def check_indentation():
    """فحص المسافات بدقة"""
    print("\n🔍 فحص المسافات بالتفصيل...")
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # البحث عن الأسطر المهمة
    important_lines = []
    for i, line in enumerate(lines):
        if any(keyword in line for keyword in ['create_trial_code', 'test_promo', '@dp.message_handler()', 'تسجيل أوامر']):
            spaces = len(line) - len(line.lstrip())
            important_lines.append((i+1, spaces, line.strip()))
    
    print("📏 المسافات في الأسطر المهمة:")
    for line_num, spaces, content in important_lines:
        print(f"السطر {line_num:3d}: {spaces:2d} مسافة | {content[:50]}...")

def create_simple_test():
    """إنشاء اختبار بسيط جداً"""
    print(f"\n💡 حل مؤقت للاختبار:")
    print("1. شغّل البوت")
    print("2. اختبر /test_promo أولاً")
    print("3. إذا لم يعمل /test_promo، المشكلة في تسجيل الأوامر بشكل عام")
    print("4. إذا عمل /test_promo ولم تعمل أوامر البرومو، المشكلة في دوال البرومو")
    print("5. إذا لم يعمل أي شيء، المشكلة في المعالج العام")

if __name__ == "__main__":
    print("🚀 فحص مبسط لمشكلة الأوامر")
    print("="*50)
    
    check_commands_in_main()
    check_indentation()
    create_simple_test()
    
    print("\n" + "="*50)
    print("✅ الفحص مكتمل!")
    print("🎯 الخطوة التالية: اختبار /test_promo في البوت")
