#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح run.py لأوامر البرومو كود
Test run.py fix for promo code commands
"""

import sys
import os

def test_promo_commands_import():
    """اختبار استيراد دوال البرومو كود"""
    print("🧪 اختبار استيراد دوال البرومو كود...")
    
    try:
        from promo_commands import (
            create_trial_code_command,
            create_discount_code_command,
            redeem_promo_code,
            list_promo_codes,
            test_promo_command,
            register_promo_commands
        )
        print("✅ تم استيراد جميع دوال البرومو كود بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد دوال البرومو كود: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد دوال البرومو كود: {e}")
        return False

def test_process_data_utils():
    """اختبار تحديثات process_data_utils.py"""
    print("\n🧪 اختبار تحديثات process_data_utils.py...")
    
    try:
        from process_data_utils import register_bot_handlers, setup_bot_commands
        print("✅ تم استيراد دوال process_data_utils بنجاح")
        
        # فحص محتوى الملف للتأكد من وجود أوامر البرومو كود
        with open('process_data_utils.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'register_promo_commands' in content:
            print("✅ تم العثور على استدعاء register_promo_commands")
        else:
            print("❌ لم يتم العثور على استدعاء register_promo_commands")
            return False
            
        if 'create_trial_code' in content:
            print("✅ تم العثور على مراجع أوامر البرومو كود")
        else:
            print("❌ لم يتم العثور على مراجع أوامر البرومو كود")
            return False
            
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد process_data_utils: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في اختبار process_data_utils: {e}")
        return False

def test_mock_dispatcher():
    """اختبار تسجيل الأوامر مع dispatcher وهمي"""
    print("\n🧪 اختبار تسجيل الأوامر مع dispatcher وهمي...")
    
    try:
        # إنشاء dispatcher وهمي للاختبار
        class MockDispatcher:
            def __init__(self):
                self.registered_commands = []
                
            def register_message_handler(self, handler, commands=None, **kwargs):
                if commands:
                    for cmd in commands:
                        self.registered_commands.append(cmd)
                        print(f"   📝 تم تسجيل الأمر: /{cmd}")
                return True
        
        mock_dp = MockDispatcher()
        
        # اختبار تسجيل أوامر البرومو كود
        from promo_commands import register_promo_commands
        success = register_promo_commands(mock_dp)
        
        if success:
            print("✅ تم تسجيل أوامر البرومو كود بنجاح")
            print(f"📊 تم تسجيل {len(mock_dp.registered_commands)} أمر:")
            for cmd in mock_dp.registered_commands:
                print(f"   - /{cmd}")
            return True
        else:
            print("❌ فشل في تسجيل أوامر البرومو كود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التسجيل: {e}")
        return False

def test_run_py_compatibility():
    """اختبار توافق run.py مع التحديثات"""
    print("\n🧪 اختبار توافق run.py مع التحديثات...")
    
    try:
        # فحص محتوى run.py
        with open('run.py', 'r', encoding='utf-8') as f:
            run_content = f.read()
            
        # التحقق من وجود استيراد process_data_utils
        if 'from process_data_utils import register_bot_handlers' in run_content:
            print("✅ run.py يستورد register_bot_handlers")
        else:
            print("❌ run.py لا يستورد register_bot_handlers")
            return False
            
        # التحقق من استدعاء register_bot_handlers
        if 'register_bot_handlers(dp)' in run_content:
            print("✅ run.py يستدعي register_bot_handlers")
        else:
            print("❌ run.py لا يستدعي register_bot_handlers")
            return False
            
        print("✅ run.py متوافق مع التحديثات")
        return True
        
    except FileNotFoundError:
        print("❌ لم يتم العثور على ملف run.py")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص run.py: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إصلاح run.py لأوامر البرومو كود")
    print("=" * 60)
    
    tests = [
        ("استيراد دوال البرومو كود", test_promo_commands_import),
        ("تحديثات process_data_utils", test_process_data_utils),
        ("تسجيل الأوامر", test_mock_dispatcher),
        ("توافق run.py", test_run_py_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ run.py الآن يدعم أوامر البرومو كود")
        print("\n📋 الأوامر المتاحة عند استخدام run.py:")
        print("   - /create_trial_code")
        print("   - /create_discount_code")
        print("   - /redeem")
        print("   - /list_codes")
        print("   - /test_promo")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة التحديثات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
