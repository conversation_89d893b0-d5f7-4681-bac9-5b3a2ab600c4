#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لأوامر البوت بعد الإصلاح
"""

try:
    import main
    print("✅ تم تحميل main.py بنجاح")
    print(f"✅ PROMO_CODES_AVAILABLE: {main.PROMO_CODES_AVAILABLE}")
    
    # محاكاة رسالة أمر
    class MockMessage:
        def __init__(self, text):
            self.text = text
            self.from_user = MockUser()
        
        async def reply(self, text, **kwargs):
            print(f"📩 البوت يرد: {text}")
    
    class MockUser:
        def __init__(self):
            self.id = 123456789
    
    # اختبار الأوامر
    import asyncio
    
    async def test_commands():
        print("\n🧪 اختبار أوامر البوت...")
        
        # اختبار كود تجربة
        if hasattr(main, 'create_trial_code_command'):
            message = MockMessage("/create_trial_code 5 20 اختبار")
            try:
                await main.create_trial_code_command(message)
                print("✅ أمر create_trial_code يعمل")
            except Exception as e:
                print(f"⚠️ أمر create_trial_code: {e}")
        
        # اختبار كود خصم
        if hasattr(main, 'create_discount_code_command'):
            message = MockMessage("/create_discount_code 30 15 اختبار")
            try:
                await main.create_discount_code_command(message)
                print("✅ أمر create_discount_code يعمل")
            except Exception as e:
                print(f"⚠️ أمر create_discount_code: {e}")
        
        print("\n🎯 الاختبار مكتمل!")
    
    # تشغيل الاختبار
    asyncio.run(test_commands())
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
