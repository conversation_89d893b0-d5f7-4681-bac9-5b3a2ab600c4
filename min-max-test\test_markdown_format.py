#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تنسيق Markdown في رسائل البوت
Test Markdown formatting in bot messages
"""

def test_comprehensive_help():
    """اختبار رسالة المساعدة الشاملة"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("🧪 اختبار تنسيق رسالة المساعدة...")
        print(f"📏 طول الرسالة: {len(COMPREHENSIVE_HELP)} حرف")
        print(f"📄 عدد الأسطر: {len(COMPREHENSIVE_HELP.split('\n'))}")
        
        # فحص الأحرف الخاصة
        import re
        asterisks = len(re.findall(r'\*', COMPREHENSIVE_HELP))
        underscores = len(re.findall(r'_', COMPREHENSIVE_HELP))
        backticks = len(re.findall(r'`', COMPREHENSIVE_HELP))
        
        print(f"⭐ عدد النجمات (*): {asterisks}")
        print(f"📝 عدد الشرطات السفلية (_): {underscores}")
        print(f"💻 عدد العلامات المائلة (`): {backticks}")
        
        # التحقق من تطابق الأقواس
        issues = []
        if asterisks % 2 != 0:
            issues.append("عدد النجمات غير متطابق")
        if underscores % 2 != 0:
            issues.append("عدد الشرطات السفلية غير متطابق")
        if backticks % 2 != 0:
            issues.append("عدد العلامات المائلة غير متطابق")
        
        if issues:
            print("⚠️ مشاكل في التنسيق:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ تنسيق Markdown صحيح")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار رسالة المساعدة: {e}")
        return False

def test_message_sending():
    """اختبار إرسال رسالة المساعدة"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("\n🧪 اختبار محاكاة إرسال الرسالة...")
        
        # محاكاة معالجة Telegram للرسالة
        # التحقق من الأحرف الخاصة التي قد تسبب مشاكل
        problematic_chars = []
        
        # البحث عن أنماط قد تسبب مشاكل
        import re
        
        # البحث عن نجمات غير متطابقة
        asterisk_positions = [m.start() for m in re.finditer(r'\*', COMPREHENSIVE_HELP)]
        if len(asterisk_positions) % 2 != 0:
            problematic_chars.append(f"نجمة غير متطابقة في الموضع {asterisk_positions[-1]}")
        
        # البحث عن شرطات سفلية غير متطابقة
        underscore_positions = [m.start() for m in re.finditer(r'_', COMPREHENSIVE_HELP)]
        if len(underscore_positions) % 2 != 0:
            problematic_chars.append(f"شرطة سفلية غير متطابقة في الموضع {underscore_positions[-1]}")
        
        # البحث عن علامات مائلة غير متطابقة
        backtick_positions = [m.start() for m in re.finditer(r'`', COMPREHENSIVE_HELP)]
        if len(backtick_positions) % 2 != 0:
            problematic_chars.append(f"علامة مائلة غير متطابقة في الموضع {backtick_positions[-1]}")
        
        if problematic_chars:
            print("⚠️ مشاكل محتملة:")
            for char in problematic_chars:
                print(f"   - {char}")
            
            # عرض المنطقة المشكوك فيها
            if asterisk_positions and len(asterisk_positions) % 2 != 0:
                pos = asterisk_positions[-1]
                start = max(0, pos - 50)
                end = min(len(COMPREHENSIVE_HELP), pos + 50)
                print(f"\n📍 المنطقة المشكوك فيها حول الموضع {pos}:")
                print(f"'{COMPREHENSIVE_HELP[start:end]}'")
            
            return False
        else:
            print("✅ لا توجد مشاكل في التنسيق")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإرسال: {e}")
        return False

def find_byte_offset_issue():
    """البحث عن مشكلة byte offset 1341"""
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        print("\n🔍 البحث عن مشكلة byte offset 1341...")
        
        # تحويل النص إلى bytes
        text_bytes = COMPREHENSIVE_HELP.encode('utf-8')
        print(f"📏 طول النص بالبايت: {len(text_bytes)}")
        
        if len(text_bytes) > 1341:
            # فحص المنطقة حول byte offset 1341
            start_pos = max(0, 1341 - 50)
            end_pos = min(len(text_bytes), 1341 + 50)
            
            problem_area = text_bytes[start_pos:end_pos]
            try:
                problem_text = problem_area.decode('utf-8')
                print(f"📍 النص حول الموضع 1341:")
                print(f"'{problem_text}'")
                
                # البحث عن أحرف Markdown في هذه المنطقة
                markdown_chars = ['*', '_', '`', '[', ']', '(', ')']
                for i, char in enumerate(problem_text):
                    if char in markdown_chars:
                        actual_pos = start_pos + i
                        print(f"🔍 وجد حرف '{char}' في الموضع {actual_pos}")
                        
            except UnicodeDecodeError:
                print("❌ مشكلة في تشفير النص")
                
        else:
            print("📏 النص أقصر من الموضع المشكوك فيه")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث عن المشكلة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار تنسيق Markdown في رسائل البوت")
    print("=" * 50)
    
    tests = [
        ("اختبار رسالة المساعدة", test_comprehensive_help),
        ("اختبار محاكاة الإرسال", test_message_sending),
        ("البحث عن مشكلة byte offset", find_byte_offset_issue),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed == len(results):
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ رسالة المساعدة جاهزة للاستخدام")
    else:
        print("⚠️ توجد مشاكل تحتاج إصلاح")

if __name__ == "__main__":
    main()
