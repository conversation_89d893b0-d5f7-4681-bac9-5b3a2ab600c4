#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دالة get_sheet_records_safe
"""

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_function_existence():
    """اختبار وجود الدالة الجديدة"""
    try:
        # قراءة محتوى process_data.py
        with open('process_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص وجود الدالة الجديدة
        if 'def get_sheet_records_safe(' in content:
            print("✅ دالة get_sheet_records_safe موجودة")
        else:
            print("❌ دالة get_sheet_records_safe غير موجودة")
            return False
        
        # فحص استخدام الدالة في الدوال المختلفة
        safe_usages = [
            'get_sheet_records_safe(sheet3)',
        ]
        
        unsafe_usages = [
            'sheet3.get_all_records()',
        ]
        
        print("\n🔍 فحص استخدام الدالة الآمنة:")
        safe_count = 0
        for usage in safe_usages:
            count = content.count(usage)
            safe_count += count
            print(f"✅ {usage}: {count} مرة")
        
        print("\n🔍 فحص الاستخدامات غير الآمنة:")
        unsafe_count = 0
        for usage in unsafe_usages:
            count = content.count(usage)
            unsafe_count += count
            if count > 0:
                print(f"⚠️ {usage}: {count} مرة (يجب إصلاحها)")
            else:
                print(f"✅ {usage}: {count} مرة")
        
        # فحص معالجة الأخطاء
        error_handling_patterns = [
            'except Exception as e:',
            'logging.error(',
            '.get(',  # استخدام .get() بدلاً من الوصول المباشر
        ]
        
        print("\n🔍 فحص معالجة الأخطاء:")
        for pattern in error_handling_patterns:
            count = content.count(pattern)
            print(f"✅ {pattern}: {count} مرة")
        
        # النتيجة النهائية
        if safe_count >= 3 and unsafe_count == 0:
            print(f"\n🎉 الإصلاح تم بنجاح!")
            print(f"- الاستخدامات الآمنة: {safe_count}")
            print(f"- الاستخدامات غير الآمنة: {unsafe_count}")
            return True
        else:
            print(f"\n⚠️ الإصلاح يحتاج مراجعة:")
            print(f"- الاستخدامات الآمنة: {safe_count}")
            print(f"- الاستخدامات غير الآمنة: {unsafe_count}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        return False

def test_function_structure():
    """فحص بنية الدالة"""
    try:
        with open('process_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استخراج الدالة
        start_idx = content.find('def get_sheet_records_safe(')
        if start_idx == -1:
            print("❌ لم يتم العثور على الدالة")
            return False
        
        # فحص المكونات المهمة
        function_checks = [
            'worksheet.get_all_records()',
            'duplicate',
            'all_values = worksheet.get_all_values()',
            'unique_headers',
            'logging.warning',
            'logging.error',        ]
        
        print("\n🔍 فحص مكونات الدالة:")
        all_good = True
        
        # استخراج محتوى الدالة كاملة
        end_idx = content.find('\ndef ', start_idx + 1)
        if end_idx == -1:
            end_idx = len(content)
        
        function_content = content[start_idx:end_idx]
        
        for check in function_checks:
            if check in function_content:
                print(f"✅ {check}")
            else:
                print(f"❌ {check}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الدالة: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء فحص إصلاح Google Sheets...")
    
    existence_ok = test_function_existence()
    structure_ok = test_function_structure()
    
    print(f"\n📊 النتائج:")
    print(f"وجود الدالة والاستخدام: {'✅ سليم' if existence_ok else '❌ يحتاج إصلاح'}")
    print(f"بنية الدالة: {'✅ سليم' if structure_ok else '❌ يحتاج إصلاح'}")
    
    if existence_ok and structure_ok:
        print("\n🎉 إصلاح Google Sheets تم بنجاح!")
        print("\n📋 الدوال المحمية الآن:")
        print("- process_today_deals")
        print("- process_t1_achieved") 
        print("- process_t2_achieved")
        print("- process_t3_achieved")
        print("\n💡 لن تعود هذه الدوال تسبب crash عند وجود عناوين مكررة في الشيت")
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إصلاح")
