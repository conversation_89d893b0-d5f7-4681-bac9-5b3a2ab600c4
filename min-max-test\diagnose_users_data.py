#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص بيانات المستخدمين وإضافة مستخدمين تجريبيين
Diagnose user data and add test users
"""

import logging
import datetime
from typing import List, Dict

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def diagnose_sheets_data():
    """تشخيص بيانات Google Sheets"""
    try:
        from auth import open_google_sheet
        
        print("🔍 تشخيص بيانات Google Sheets...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        print(f"✅ تم الاتصال بـ Google Sheets بنجاح")
        
        # فحص sheet2 (بيانات المستخدمين)
        print("\n📊 فحص sheet2 (بيانات المستخدمين):")
        
        try:
            all_records = sheet2.get_all_records()
            print(f"📋 إجمالي السجلات: {len(all_records)}")
            
            if all_records:
                # عرض أول 3 سجلات كعينة
                print("\n📝 عينة من البيانات (أول 3 سجلات):")
                for i, record in enumerate(all_records[:3], 1):
                    print(f"   {i}. {record}")
                
                # تحليل أنواع الاشتراكات
                subscription_types = {}
                user_ids = []
                
                for record in all_records:
                    user_id = record.get('user_id', '')
                    subscription_type = record.get('subscription_type', 'free')
                    
                    if user_id:
                        user_ids.append(user_id)
                    
                    if subscription_type in subscription_types:
                        subscription_types[subscription_type] += 1
                    else:
                        subscription_types[subscription_type] = 1
                
                print(f"\n📊 إحصائيات أنواع الاشتراكات:")
                for sub_type, count in subscription_types.items():
                    print(f"   - {sub_type}: {count}")
                
                print(f"\n👥 إجمالي المستخدمين مع معرف: {len(user_ids)}")
                
                # البحث عن المستخدمين المجانيين
                free_users = []
                for record in all_records:
                    user_id = record.get('user_id', '')
                    subscription_type = record.get('subscription_type', 'free')
                    
                    if user_id and subscription_type == 'free':
                        free_users.append(record)
                
                print(f"🆓 المستخدمين المجانيين: {len(free_users)}")
                
                if free_users:
                    print("📝 عينة من المستخدمين المجانيين:")
                    for i, user in enumerate(free_users[:3], 1):
                        print(f"   {i}. معرف: {user.get('user_id', 'غير محدد')}, الاسم: {user.get('first_name', 'غير محدد')}")
                
                return True, len(all_records), len(free_users)
                
            else:
                print("⚠️ لا توجد سجلات في sheet2")
                return True, 0, 0
                
        except Exception as e:
            print(f"❌ خطأ في قراءة sheet2: {e}")
            return False, 0, 0
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Google Sheets: {e}")
        return False, 0, 0

def add_test_users():
    """إضافة مستخدمين تجريبيين"""
    try:
        from auth import open_google_sheet
        
        print("\n🧪 إضافة مستخدمين تجريبيين...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        # بيانات المستخدمين التجريبيين
        test_users = [
            {
                'user_id': '111111111',
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'subscription_type': 'free',
                'count': 3,
                'end_date': '',
                'created_date': datetime.date.today().strftime('%Y-%m-%d')
            },
            {
                'user_id': '222222222',
                'first_name': 'فاطمة',
                'last_name': 'علي',
                'subscription_type': 'free',
                'count': 1,
                'end_date': '',
                'created_date': datetime.date.today().strftime('%Y-%m-%d')
            },
            {
                'user_id': '333333333',
                'first_name': 'محمد',
                'last_name': 'حسن',
                'subscription_type': 'free',
                'count': 5,
                'end_date': '',
                'created_date': datetime.date.today().strftime('%Y-%m-%d')
            },
            {
                'user_id': '444444444',
                'first_name': 'مريم',
                'last_name': 'أحمد',
                'subscription_type': 'trail',
                'count': 2,
                'end_date': (datetime.date.today() - datetime.timedelta(days=5)).strftime('%Y-%m-%d'),  # منتهي
                'created_date': (datetime.date.today() - datetime.timedelta(days=10)).strftime('%Y-%m-%d')
            },
            {
                'user_id': '555555555',
                'first_name': 'خالد',
                'last_name': 'سالم',
                'subscription_type': 'free',
                'count': 0,
                'end_date': '',
                'created_date': datetime.date.today().strftime('%Y-%m-%d')
            }
        ]
        
        # التحقق من الأعمدة الموجودة
        try:
            headers = sheet2.row_values(1)
            print(f"📋 الأعمدة الموجودة: {headers}")
        except:
            headers = []
        
        # إضافة الأعمدة المطلوبة إذا لم تكن موجودة
        required_headers = ['user_id', 'first_name', 'last_name', 'subscription_type', 'count', 'end_date', 'created_date']
        
        if not headers:
            print("📝 إضافة رؤوس الأعمدة...")
            sheet2.insert_row(required_headers, 1)
            headers = required_headers
        
        # التحقق من وجود المستخدمين التجريبيين
        existing_records = sheet2.get_all_records()
        existing_user_ids = [str(record.get('user_id', '')) for record in existing_records]
        
        added_count = 0
        for user in test_users:
            if str(user['user_id']) not in existing_user_ids:
                # إضافة المستخدم
                row_data = []
                for header in headers:
                    row_data.append(user.get(header, ''))
                
                sheet2.append_row(row_data)
                added_count += 1
                print(f"✅ تم إضافة المستخدم: {user['first_name']} {user['last_name']} ({user['user_id']})")
            else:
                print(f"⚠️ المستخدم موجود بالفعل: {user['user_id']}")
        
        print(f"\n📊 تم إضافة {added_count} مستخدم جديد")
        return True, added_count
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدمين التجريبيين: {e}")
        return False, 0

def test_free_users_detection():
    """اختبار اكتشاف المستخدمين المجانيين"""
    try:
        print("\n🧪 اختبار اكتشاف المستخدمين المجانيين...")
        
        from free_users_manager import FreeUsersManager
        
        # جلب جميع المستخدمين المجانيين
        all_free = FreeUsersManager.get_all_free_users()
        print(f"🆓 إجمالي المستخدمين المجانيين: {len(all_free)}")
        
        if all_free:
            print("📝 عينة من المستخدمين المجانيين:")
            for i, user in enumerate(all_free[:3], 1):
                print(f"   {i}. معرف: {user['user_id']}, النوع: {user['subscription_type']}, الاستخدامات: {user['count']}")
        
        # جلب المستخدمين النشطين
        active_free = FreeUsersManager.get_active_free_users()
        print(f"🟢 المستخدمين النشطين: {len(active_free)}")
        
        if active_free:
            print("📝 عينة من المستخدمين النشطين:")
            for i, user in enumerate(active_free[:3], 1):
                print(f"   {i}. معرف: {user['user_id']}, الاستخدامات: {user['count']}")
        
        # إحصائيات مفصلة
        stats = FreeUsersManager.get_free_users_statistics()
        if stats:
            print(f"\n📊 إحصائيات مفصلة:")
            print(f"   - إجمالي المجانيين: {stats['total_free_users']}")
            print(f"   - النشطين: {stats['active_free_users']}")
            print(f"   - لم يستخدموا البوت: {stats['never_used']}")
            print(f"   - استخدام خفيف: {stats['light_users']}")
            print(f"   - استخدام منتظم: {stats['regular_users']}")
            print(f"   - استخدام كثيف: {stats['heavy_users']}")
            print(f"   - منتهي الصلاحية: {stats['expired_users']}")
        
        return len(all_free), len(active_free)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اكتشاف المستخدمين: {e}")
        return 0, 0

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص بيانات المستخدمين وحل مشكلة 'لا توجد مستخدمين'")
    print("=" * 70)
    
    # 1. تشخيص البيانات الحالية
    success, total_records, free_users_count = diagnose_sheets_data()
    
    if not success:
        print("❌ فشل في الاتصال بـ Google Sheets")
        return
    
    # 2. إضافة مستخدمين تجريبيين إذا لم يوجد مستخدمين مجانيين
    if free_users_count == 0:
        print("\n⚠️ لا توجد مستخدمين مجانيين - سيتم إضافة مستخدمين تجريبيين")
        add_success, added_count = add_test_users()
        
        if add_success:
            print(f"✅ تم إضافة {added_count} مستخدم تجريبي")
        else:
            print("❌ فشل في إضافة المستخدمين التجريبيين")
            return
    else:
        print(f"✅ يوجد {free_users_count} مستخدم مجاني بالفعل")
    
    # 3. اختبار النظام بعد التحديث
    print("\n🧪 اختبار النظام بعد التحديث...")
    free_count, active_count = test_free_users_detection()
    
    # 4. النتيجة النهائية
    print("\n" + "=" * 70)
    print("📊 النتيجة النهائية:")
    
    if free_count > 0:
        print(f"✅ تم العثور على {free_count} مستخدم مجاني")
        print(f"✅ منهم {active_count} مستخدم نشط")
        print("🎉 النظام جاهز لإرسال أكواد البرومو!")
        
        print("\n🚀 يمكنك الآن استخدام:")
        print("   - /list_free_users - لعرض الإحصائيات")
        print("   - /send_promo_active - لإرسال أكواد للنشطين")
        print("   - /send_promo_all - لإرسال أكواد للجميع")
    else:
        print("❌ لا يزال لا توجد مستخدمين مجانيين")
        print("💡 تحقق من:")
        print("   - اتصال Google Sheets")
        print("   - صحة بيانات المستخدمين")
        print("   - أعمدة الجدول المطلوبة")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
