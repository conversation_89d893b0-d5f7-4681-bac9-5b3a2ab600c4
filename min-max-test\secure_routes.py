"""
Example of applying security middleware to API routes
"""
import logging
from flask import Flask, request, jsonify
from security_integration import secure_api_route, apply_security_middleware

logger = logging.getLogger(__name__)

def setup_secure_routes(app):
    """Apply security and set up secure routes on the Flask application"""
    # Apply security middleware
    app = apply_security_middleware(app)
    
    # Example secure API endpoint
    @app.route('/api/analyze', methods=['GET'])
    @secure_api_route
    async def analyze_stock():
        try:
            # Get stock code from request
            stock_code = request.args.get('ticker')
            
            if not stock_code:
                return jsonify({"error": "Missing required parameter: ticker"}), 400
            
            # Call your analysis function here
            # analysis_result = your_analysis_function(stock_code)
            analysis_result = {"message": f"Analysis for {stock_code} would appear here"}
            
            return jsonify(analysis_result)
        except Exception as e:
            logger.error(f"Error analyzing stock: {e}")
            return jsonify({"error": "Analysis failed"}), 500
    
    # Example of a tiered access endpoint
    @app.route('/api/premium-data', methods=['GET'])
    @secure_api_route
    async def get_premium_data():
        try:
            # Get user ID from request
            user_id = request.args.get('user_id')
            
            # This would use the user_id to check subscription status
            # subscriber_type = check_subscription(user_id)
            subscriber_type = "free"  # Replace with actual check
            
            if subscriber_type not in ["paid", "trail", "admin"]:
                return jsonify({
                    "error": "This endpoint requires a premium subscription",
                    "upgrade_link": "/subscribe"
                }), 403
            
            # Return premium data for paid users
            return jsonify({"premium_data": "Your premium data would appear here"})
        except Exception as e:
            logger.error(f"Error retrieving premium data: {e}")
            return jsonify({"error": "Failed to retrieve premium data"}), 500
    
    return app
