"""
Implementation script to integrate security features into the existing project.
Run this script once to set up the security infrastructure.
"""
import os
import sys
import logging
import shutil
import importlib.util
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_file_exists(filepath):
    """Check if a file exists"""
    return Path(filepath).exists()

def backup_file(filepath):
    """Create a backup of a file if it exists"""
    if check_file_exists(filepath):
        backup_path = f"{filepath}.bak"
        logger.info(f"Creating backup of {filepath} to {backup_path}")
        shutil.copy2(filepath, backup_path)
        return True
    return False

def verify_imports():
    """Verify that all required packages are installed"""
    required_packages = [
        'flask', 'redis', 'sqlalchemy', 'functools', 're', 
        'hashlib', 'time', 'datetime', 'logging'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Please install missing packages with:")
        logger.info(f"pip install {' '.join(missing_packages)}")
        return False
        
    return True

def update_run_py():
    """Update run.py to use the secure app initialization"""
    run_py_path = "run.py"
    
    # Backup the file first
    backup_file(run_py_path)
    
    if not check_file_exists(run_py_path):
        logger.error(f"File {run_py_path} not found")
        return False
        
    # Read the current content
    with open(run_py_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if we need to update imports
    import_updates = []
    
    if "from app_init import create_app" in content:
        import_updates.append(
            ("from app_init import create_app", 
             "from improved_app_init import create_secure_app")
        )
    
    if "app = create_app()" in content:
        import_updates.append(
            ("app = create_app()", 
             "app = create_secure_app()")
        )
    
    # Apply the updates
    for old, new in import_updates:
        content = content.replace(old, new)
    
    # Add security logging
    if "import logging" in content and "log_security_events" not in content:
        security_logging = """
# Set up security event logging
def log_security_events():
    security_logger = logging.getLogger('security')
    if not security_logger.handlers:
        security_file_handler = logging.FileHandler('security.log')
        security_file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        security_logger.addHandler(security_file_handler)
        security_logger.setLevel(logging.INFO)
    return security_logger

security_logger = log_security_events()
"""
        # Find a good position to insert this code (after imports)
        import_end = content.find('\n\n', content.find('import '))
        if import_end != -1:
            content = content[:import_end+2] + security_logging + content[import_end+2:]
    
    # Write the updated file
    with open(run_py_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"Updated {run_py_path} to use secure app initialization")
    return True

def create_directories():
    """Create any required directories"""
    dirs = ["logs"]
    
    for dir_path in dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            logger.info(f"Created directory {dir_path}")

def implement_security():
    """Main function to implement security features"""
    logger.info("Starting security implementation...")
    
    # Step 1: Verify required packages are installed
    if not verify_imports():
        logger.error("Required packages missing. Please install them first.")
        return False
    
    # Step 2: Create necessary directories
    create_directories()
    
    # Step 3: Update run.py to use secure app
    if not update_run_py():
        logger.error("Failed to update run.py")
        return False
    
    # Step 4: Set up security environment variables
    try:
        # Generate a secure key if not already set
        if 'SECRET_KEY' not in os.environ:
            import secrets
            secret_key = secrets.token_hex(24)
            with open('.env', 'a') as f:
                f.write(f"\nSECRET_KEY={secret_key}\n")
            logger.info("Created new SECRET_KEY in .env file")
    except Exception as e:
        logger.error(f"Failed to set up environment variables: {e}")
    
    logger.info("Security implementation completed successfully!")
    logger.info("Please restart your application for changes to take effect.")
    return True

if __name__ == '__main__':
    if implement_security():
        print("✅ Security implementation successful!")
    else:
        print("❌ Security implementation failed. Check the logs for details.")
        sys.exit(1)
