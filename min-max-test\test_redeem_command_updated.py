#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أمر /redeem المحدث
Test updated /redeem command
"""

import asyncio
import datetime
from unittest.mock import AsyncMock, MagicMock

async def test_redeem_command_functionality():
    """اختبار وظائف أمر /redeem المحدث"""
    try:
        from promo_codes import PromoCodeManager
        from user_subscription_manager import UserSubscriptionManager
        
        print("🧪 اختبار أمر /redeem المحدث...")
        
        # 1. إنشاء كود تجربة مجانية للاختبار
        print("\n🎫 إنشاء كود تجربة مجانية للاختبار:")
        
        test_code = PromoCodeManager.create_trial_code(
            days=7,
            expiry_days=30,
            note="اختبار أمر redeem المحدث"
        )
        
        if test_code:
            print(f"✅ تم إنشاء كود الاختبار: {test_code}")
        else:
            print("❌ فشل في إنشاء كود الاختبار")
            return False
        
        # 2. اختبار مستخدم حقيقي من البيانات
        test_user_id = "1078882205"  # Michael من البيانات الحقيقية
        
        print(f"\n👤 اختبار مع المستخدم: {test_user_id}")
        
        # فحص حالة المستخدم قبل التفعيل
        user_info_before = UserSubscriptionManager.get_user_subscription_info(test_user_id)
        if user_info_before:
            print(f"📊 حالة المستخدم قبل التفعيل:")
            print(f"   - نوع الاشتراك: {user_info_before['subscription_type']}")
            print(f"   - تاريخ الانتهاء: {user_info_before['end_date']}")
            print(f"   - نشط: {user_info_before['is_active']}")
        else:
            print("❌ لم يتم العثور على المستخدم")
            return False
        
        # 3. محاكاة تفعيل الكود
        print(f"\n🔄 محاكاة تفعيل الكود {test_code}...")
        
        # التحقق من صحة الكود
        is_valid, error_msg, code_data = PromoCodeManager.validate_promo_code(test_code, test_user_id)
        
        if is_valid:
            print(f"✅ الكود صحيح:")
            print(f"   - النوع: {code_data['type']}")
            print(f"   - القيمة: {code_data['value']} أيام")
            print(f"   - ينتهي في: {code_data['expiry_date']}")
            
            # تفعيل التجربة المجانية
            success = UserSubscriptionManager.activate_trial_subscription(test_user_id, code_data['value'])
            
            if success:
                print("✅ تم تفعيل التجربة المجانية بنجاح")
                
                # تحديث حالة الكود
                PromoCodeManager.use_promo_code(code_data, test_user_id)
                print("✅ تم تحديث حالة الكود")
                
                # فحص حالة المستخدم بعد التفعيل
                user_info_after = UserSubscriptionManager.get_user_subscription_info(test_user_id)
                if user_info_after:
                    print(f"\n📊 حالة المستخدم بعد التفعيل:")
                    print(f"   - نوع الاشتراك: {user_info_after['subscription_type']}")
                    print(f"   - تاريخ الانتهاء: {user_info_after['end_date']}")
                    print(f"   - نشط: {user_info_after['is_active']}")
                    print(f"   - أيام متبقية: {user_info_after['days_remaining']}")
                    
                    # التحقق من التحديث
                    if (user_info_after['subscription_type'] == 'trail' and 
                        user_info_after['is_active'] and 
                        user_info_after['days_remaining'] > 0):
                        print("🎉 تم تحديث بيانات المستخدم بنجاح!")
                        return True
                    else:
                        print("❌ لم يتم تحديث بيانات المستخدم بشكل صحيح")
                        return False
                else:
                    print("❌ فشل في قراءة بيانات المستخدم بعد التحديث")
                    return False
            else:
                print("❌ فشل في تفعيل التجربة المجانية")
                return False
        else:
            print(f"❌ الكود غير صحيح: {error_msg}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أمر redeem: {e}")
        return False

async def test_redeem_command_interface():
    """اختبار واجهة أمر /redeem"""
    try:
        print("\n🤖 اختبار واجهة أمر /redeem:")
        
        # استيراد دالة الأمر
        from promo_codes import redeem_promo_code
        
        # إنشاء كود اختبار
        from promo_codes import PromoCodeManager
        test_code = PromoCodeManager.create_trial_code(
            days=7,
            expiry_days=30,
            note="اختبار واجهة redeem"
        )
        
        if not test_code:
            print("❌ فشل في إنشاء كود الاختبار")
            return False
        
        # محاكاة رسالة من المستخدم
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 1078882205  # مستخدم حقيقي
        mock_message.from_user.first_name = "Michael"
        mock_message.from_user.last_name = ""
        mock_message.text = f"/redeem {test_code}"
        
        # تشغيل الأمر
        await redeem_promo_code(mock_message)
        
        # التحقق من استدعاء reply
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            print("✅ تم إرسال رسالة الرد")
            
            # التحقق من محتوى الرسالة
            required_content = [
                "تم تفعيل التجربة المجانية بنجاح",
                "تحليلات غير محدودة",
                "أيام",
                "ينتهي في"
            ]
            
            missing_content = []
            for content in required_content:
                if content not in sent_message:
                    missing_content.append(content)
            
            if missing_content:
                print(f"⚠️ محتوى مفقود في الرسالة: {missing_content}")
                print(f"📝 الرسالة المرسلة: {sent_message[:200]}...")
            else:
                print("✅ جميع المحتوى المطلوب موجود في الرسالة")
            
            return len(missing_content) == 0
        else:
            print("❌ لم يتم استدعاء reply")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة الأمر: {e}")
        return False

async def test_error_handling():
    """اختبار معالجة الأخطاء"""
    try:
        print("\n🛡️ اختبار معالجة الأخطاء:")
        
        from promo_codes import redeem_promo_code
        
        # 1. اختبار كود غير موجود
        print("   1. اختبار كود غير موجود:")
        
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 1078882205
        mock_message.from_user.first_name = "Michael"
        mock_message.from_user.last_name = ""
        mock_message.text = "/redeem INVALIDCODE123"
        
        await redeem_promo_code(mock_message)
        
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            if "خطأ في الكود" in sent_message or "غير صحيح" in sent_message:
                print("   ✅ تم التعامل مع الكود غير الصحيح")
            else:
                print(f"   ❌ رسالة خطأ غير متوقعة: {sent_message[:100]}...")
        
        # 2. اختبار تنسيق خاطئ
        print("   2. اختبار تنسيق خاطئ:")
        
        mock_message.reply.reset_mock()
        mock_message.text = "/redeem"  # بدون كود
        
        await redeem_promo_code(mock_message)
        
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            if "الاستخدام الصحيح" in sent_message:
                print("   ✅ تم التعامل مع التنسيق الخاطئ")
            else:
                print(f"   ❌ رسالة خطأ غير متوقعة: {sent_message[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الأخطاء: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لأمر /redeem المحدث")
    print("=" * 60)
    
    tests = [
        ("اختبار وظائف أمر redeem", test_redeem_command_functionality),
        ("اختبار واجهة الأمر", test_redeem_command_interface),
        ("اختبار معالجة الأخطاء", test_error_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed == len(results):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ أمر /redeem المحدث يعمل بكفاءة")
        print("✅ يحدث كود البرومو في sheet promo_codes")
        print("✅ يحدث حالة المستخدم في sheet users")
        print("✅ يحدث تاريخ الانتهاء بشكل صحيح")
        
        print("\n🚀 الميزات المحققة:")
        print("   - تحديث نوع الاشتراك من free إلى trail")
        print("   - تحديث تاريخ انتهاء الاشتراك")
        print("   - تحديث حالة كود البرومو")
        print("   - رسائل تأكيد مفصلة وجميلة")
        print("   - معالجة أخطاء شاملة")
        
        print("\n💡 للاستخدام:")
        print("   /redeem TRIAL7FREE")
        print("   /redeem YOUR_PROMO_CODE")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")

if __name__ == "__main__":
    asyncio.run(main())
