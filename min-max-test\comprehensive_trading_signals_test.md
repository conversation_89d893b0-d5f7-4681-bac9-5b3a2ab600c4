# اختبار شامل لمنطق إشارات التداول

## 🎯 **مقدمة الاختبار**

هذا الاختبار يغطي جميع السيناريوهات الممكنة لنظام إشارات التداول المحدث، بما في ذلك:
- جميع أنواع الإشارات (Buy, T1Done, T2Done, T3Done, TSL, Sell)
- نظام تعزيز المراكز
- تحليل المخاطر الفني
- حساب الربح الفعلي مع البدائل المختلفة

## 🧪 **Test Cases - حالات الاختبار**

### **TC001: إشارة شراء جديدة - سهم غير موجود**

#### **المدخلات:**
```json
{
  "signal_type": "buy",
  "stock_code": "NEWSTOCK",
  "buy_price": 100,
  "tp1": 110,
  "tp2": 120,
  "tp3": 130,
  "sl": 90
}
```

#### **التوقعات:**
1. ✅ البحث في Sheet4 → غير موجود
2. ✅ البحث في Sheet1 → غير موجود
3. ✅ إضافة السهم إلى Sheet4 بدون إرسال توصيات
4. ✅ إرسال تنبيه للإدارة: "سهم جديد غير مدرج"

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
رسالة الإدارة: تم استلام رمز سهم جديد 'NEWSTOCK'
إرسال توصيات: لا
تحديث الشيت: نعم
```

---

### **TC002: إشارة شراء - سهم موجود مع صفقة مفتوحة (تعزيز مراكز)**

#### **المدخلات:**
```json
{
  "signal_type": "buy",
  "stock_code": "COMI",
  "buy_price": 95,  // أقل من السعر الموجود (100)
  "tp1": 105,
  "tp2": 114,
  "tp3": 123,
  "sl": 85
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- السعر: 100 جنيه
- الحالة: buy
- الأهداف: 110, 120, 130
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ السعر الجديد أقل (95 < 100)
3. ✅ تحديث السعر والأهداف
4. ✅ إرسال رسالة تعزيز للأعضاء والإدارة
5. ✅ حفظ السعر الأصلي في العمود 17

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
نوع العملية: تعزيز مراكز
تحسن السعر: 5%
رسالة المستخدمين: "فرصة تعزيز مراكز ممتازة"
تحديث البيانات: نعم
```

---

### **TC003: إشارة شراء مع مؤشرات مخاطر عالية**

#### **المدخلات:**
```json
{
  "signal_type": "buy",
  "stock_code": "RISKY",
  "buy_price": 50,
  "tp1": 55,
  "tp2": 60,
  "tp3": 65,
  "sl": 45
}
```

#### **المؤشرات الفنية المحاكاة:**
```python
rsi = 75  # تشبع شرائي
macd_diff = -0.8  # إشارة سلبية قوية
volume_ratio = 0.4  # حجم تداول منخفض
adx = 15  # ضعف في الاتجاه
```

#### **التوقعات:**
1. ✅ تحليل المؤشرات الفنية
2. ✅ تحديد مستوى المخاطرة: عالي
3. ✅ إضافة تحذيرات للرسالة
4. ✅ تعديل توصيات حجم المركز

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
مستوى المخاطرة: عالي 🟠
عوامل المخاطرة: 
  - تشبع شرائي (RSI: 75)
  - إشارة MACD سلبية قوية
  - حجم تداول منخفض (40%)
التوصية: تقليل المركز إلى 50%
```

---

### **TC004: تحقق الهدف الأول**

#### **المدخلات:**
```json
{
  "signal_type": "t1done",
  "stock_code": "COMI",
  "tp1": 110,
  "sl": 105  // رفع وقف الخسارة
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- سعر الشراء: 100 جنيه
- تاريخ الشراء: 2025-06-01
- الحالة: buy
```

#### **التوقعات:**
1. ✅ تحديث الحالة إلى "t1done"
2. ✅ رفع وقف الخسارة إلى 105
3. ✅ تسجيل تاريخ تحقق الهدف
4. ✅ حساب نسبة الربح: 10%
5. ✅ حساب المدة المستغرقة: 21 يوم

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الربح المحقق: 10%
المدة: 21 يوم
رسالة المستخدمين: "تحقق الهدف الأول"
وقف الخسارة الجديد: 105 جنيه
```

---

### **TC005: إشارة بيع مع أهداف محققة - بيانات تاريخية متوفرة**

#### **المدخلات:**
```json
{
  "signal_type": "sell",
  "stock_code": "ETEL",
  "sell_price": 118
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- سعر الشراء: 100 جنيه
- تاريخ الشراء: 2025-06-01
- الهدف الأول: محقق (110 - 2025-06-10)
- الهدف الثاني: محقق (120 - 2025-06-15)
- الحالة: t2done
```

#### **البيانات التاريخية المحاكاة:**
```python
historical_data = {
  "period": "2025-06-15 to 2025-06-22",
  "highest_price": 125,  // أعلى من سعر البيع
  "date_achieved": "2025-06-18",
  "data_source": "historical"
}
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ وجود أهداف محققة (t1, t2)
3. ✅ استخراج تاريخ آخر هدف: 2025-06-15
4. ✅ البحث في البيانات التاريخية
5. ✅ اختيار أعلى سعر: 125 (أعلى من 118)
6. ✅ حساب الربح الفعلي: 25%

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الربح الفعلي: 25%
أفضل سعر خروج: 125 جنيه
طريقة الحساب: "البيانات التاريخية"
موثوقية البيانات: عالية
رسالة المستخدمين: "تحقق ربح فعلي ممتاز"
```

---

### **TC006: إشارة بيع مع أهداف محققة - بيانات تاريخية غير متوفرة**

#### **المدخلات:**
```json
{
  "signal_type": "sell",
  "stock_code": "SWDY", 
  "sell_price": 88
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- سعر الشراء: 80 جنيه
- الهدف الأول: محقق (88 - 2025-06-10)
- آخر هدف محقق: 88 جنيه
- الحالة: t1done
```

#### **البيانات التاريخية:**
```python
historical_data = None  // غير متوفرة
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ وجود هدف محقق (t1)
3. ✅ فشل في الحصول على البيانات التاريخية
4. ✅ استخدام سعر آخر هدف (88)
5. ✅ اختيار أفضل سعر: 88 (مساوي لسعر البيع)
6. ✅ حساب الربح الفعلي: 10%

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الربح الفعلي: 10%
أفضل سعر خروج: 88 جنيه
طريقة الحساب: "سعر آخر هدف محقق"
موثوقية البيانات: متوسطة
ملاحظة: "تم استخدام سعر آخر هدف بسبب عدم توفر البيانات التاريخية"
```

---

### **TC007: وقف خسارة مع أهداف محققة**

#### **المدخلات:**
```json
{
  "signal_type": "tsl",
  "stock_code": "EGAL",
  "sl": 105
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- سعر الشراء: 100 جنيه
- الهدف الأول: محقق (110 - 2025-06-05)
- الهدف الثاني: محقق (120 - 2025-06-12)
- الهدف الثالث: محقق (130 - 2025-06-18)
- الحالة: t3done
```

#### **البيانات التاريخية المحاكاة:**
```python
historical_data = {
  "highest_price": 135,  // أعلى من سعر الوقف
  "last_target_date": "2025-06-18",
  "current_date": "2025-06-22"
}
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ وجود أهداف محققة (t1, t2, t3)
3. ✅ استخراج آخر هدف: 130 (2025-06-18)
4. ✅ البحث في البيانات التاريخية
5. ✅ أعلى سعر محقق: 135
6. ✅ حساب الربح الفعلي: 35%

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الربح الفعلي: 35%
أفضل سعر محقق: 135 جنيه
طريقة الحساب: "البيانات التاريخية"
نوع الرسالة: "حماية الأرباح"
تفاصيل: "تم تحقيق أقصى ربح ممكن قبل التراجع"
```

---

### **TC008: إشارة بيع بدون أهداف محققة**

#### **المدخلات:**
```json
{
  "signal_type": "sell",
  "stock_code": "LOSSTOCK",
  "sell_price": 85
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- سعر الشراء: 100 جنيه
- لا توجد أهداف محققة
- الحالة: buy
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ لا توجد أهداف محققة
3. ✅ حساب الخسارة: -15%
4. ✅ إرسال تحذير بيع

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
النتيجة: خسارة 15%
نوع الرسالة: "تحذير بيع"
التوصية: "اتخاذ تدابير حماية وتقليل الخسائر"
```

---

### **TC009: إشارة على سهم بدون صفقة مفتوحة**

#### **المدخلات:**
```json
{
  "signal_type": "tsl",
  "stock_code": "NOPOSITION",
  "sl": 90
}
```

#### **البيانات الموجودة:**
```
لا توجد صفقة مفتوحة للسهم
```

#### **التوقعات:**
1. ✅ عدم وجود صفقة مفتوحة
2. ✅ تجاهل الإشارة
3. ✅ إرسال تنبيه للإدارة

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الإجراء: تجاهل الإشارة
رسالة الإدارة: "إشارة وقف خسارة على سهم بدون صفقة مفتوحة"
إرسال توصيات: لا
```

---

### **TC010: إشارة شراء مكررة بسعر أعلى**

#### **المدخلات:**
```json
{
  "signal_type": "buy",
  "stock_code": "COMI",
  "buy_price": 105,  // أعلى من السعر الموجود (100)
  "tp1": 115,
  "tp2": 125,
  "tp3": 135,
  "sl": 95
}
```

#### **البيانات الموجودة:**
```
الصفقة الحالية:
- السعر: 100 جنيه
- الحالة: buy
```

#### **التوقعات:**
1. ✅ وجود صفقة مفتوحة
2. ✅ السعر الجديد أعلى (105 > 100)
3. ✅ تجاهل الإشارة
4. ✅ إرسال تنبيه للإدارة

#### **النتيجة المتوقعة:**
```
حالة النجاح: ✅
الإجراء: تجاهل الإشارة المكررة
رسالة الإدارة: "إشارة شراء مكررة بسعر أعلى"
السبب: "السعر الجديد لا يحسن المركز الحالي"
```

---

## 🔧 **Test Execution Framework - إطار تنفيذ الاختبارات**

### **TestRunner Class:**

```python
class TradingSignalTestRunner:
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        # إنشاء sheet تجريبي
        # تحميل بيانات اختبار
        # إعداد mock functions
        pass
    
    def run_test_case(self, test_case):
        """تنفيذ حالة اختبار واحدة"""
        try:
            # إعداد البيانات
            self.setup_test_data(test_case)
            
            # تنفيذ الاختبار
            result = self.execute_signal(test_case['input'])
            
            # التحقق من النتائج
            success = self.verify_expectations(result, test_case['expected'])
            
            # تسجيل النتائج
            self.record_test_result(test_case['id'], success, result)
            
            return success
            
        except Exception as e:
            self.record_test_failure(test_case['id'], str(e))
            return False
    
    def run_all_tests(self):
        """تنفيذ جميع الاختبارات"""
        test_cases = [
            self.create_tc001(),
            self.create_tc002(),
            self.create_tc003(),
            # ... جميع حالات الاختبار
        ]
        
        for test_case in test_cases:
            success = self.run_test_case(test_case)
            if success:
                self.passed_tests += 1
            else:
                self.failed_tests += 1
        
        return self.generate_test_report()
    
    def verify_expectations(self, actual_result, expected_result):
        """التحقق من توافق النتائج مع التوقعات"""
        checks = [
            self.check_signal_processing(actual_result, expected_result),
            self.check_database_updates(actual_result, expected_result),
            self.check_message_content(actual_result, expected_result),
            self.check_calculations(actual_result, expected_result)
        ]
        
        return all(checks)
    
    def generate_test_report(self):
        """إنتاج تقرير شامل للاختبارات"""
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests) * 100
        
        report = f"""
        📊 تقرير اختبار نظام إشارات التداول
        =====================================
        
        📈 إجمالي الاختبارات: {total_tests}
        ✅ الاختبارات الناجحة: {self.passed_tests}
        ❌ الاختبارات الفاشلة: {self.failed_tests}
        📊 معدل النجاح: {success_rate:.1f}%
        
        📋 تفاصيل النتائج:
        {self.format_detailed_results()}
        """
        
        return report
```

### **Mock Data Generator:**

```python
class MockDataGenerator:
    """مولد بيانات اختبار وهمية"""
    
    def create_mock_sheet_data(self):
        """إنشاء بيانات شيت وهمية"""
        return {
            'Sheet1': [  # قائمة الأسهم المدرجة
                ['COMI', 'البنك التجاري الدولي'],
                ['ETEL', 'المصرية للاتصالات'],
                ['SWDY', 'السويدي اليكتريك'],
                ['EGAL', 'مصر للألومنيوم']
            ],
            'Sheet4': [  # الصفقات النشطة
                ['COMI', '', 'buy', 100, '2025-06-01', '', '', 110, 120, 130, 90],
                ['ETEL', '', 't2done', 100, '2025-06-01', '', '', 110, 120, 130, 105, '2025-06-10', '2025-06-15']
            ]
        }
    
    def create_mock_historical_data(self, stock_code, start_date, end_date):
        """إنشاء بيانات تاريخية وهمية"""
        mock_data = {
            'COMI': {'highest_price': 125, 'date': '2025-06-18'},
            'ETEL': {'highest_price': 135, 'date': '2025-06-20'},
            'SWDY': None,  # لا توجد بيانات
            'DEFAULT': {'highest_price': 110, 'date': end_date}
        }
        
        return mock_data.get(stock_code, mock_data['DEFAULT'])
    
    def create_mock_technical_indicators(self, stock_code):
        """إنشاء مؤشرات فنية وهمية"""
        risk_profiles = {
            'RISKY': {
                'rsi': 75,
                'macd_diff': -0.8,
                'volume_ratio': 0.4,
                'adx': 15,
                'risk_level': 'عالي'
            },
            'SAFE': {
                'rsi': 45,
                'macd_diff': 0.3,
                'volume_ratio': 1.2,
                'adx': 35,
                'risk_level': 'منخفض'
            },
            'DEFAULT': {
                'rsi': 55,
                'macd_diff': 0.1,
                'volume_ratio': 0.8,
                'adx': 25,
                'risk_level': 'متوسط'
            }
        }
        
        return risk_profiles.get(stock_code, risk_profiles['DEFAULT'])
```

## 📊 **Expected Test Results - النتائج المتوقعة**

### **ملخص النتائج المتوقعة:**

| Test Case | الوصف | النتيجة المتوقعة | مستوى الأولوية |
|-----------|--------|------------------|------------------|
| TC001 | سهم جديد غير مدرج | ✅ إضافة بدون إرسال | عالي |
| TC002 | تعزيز مراكز | ✅ تحديث وإرسال | عالي |
| TC003 | مخاطر عالية | ✅ تحذير مخصص | متوسط |
| TC004 | تحقق هدف أول | ✅ تحديث ورفع وقف | عالي |
| TC005 | بيع مع بيانات تاريخية | ✅ ربح 25% | عالي |
| TC006 | بيع بدون بيانات تاريخية | ✅ ربح 10% | عالي |
| TC007 | وقف خسارة مع أهداف | ✅ حماية أرباح | عالي |
| TC008 | بيع بدون أهداف | ✅ تحذير خسارة | متوسط |
| TC009 | إشارة بدون صفقة | ✅ تجاهل وتنبيه | متوسط |
| TC010 | إشارة مكررة أعلى | ✅ تجاهل | منخفض |

### **معايير النجاح:**
- ✅ **95%+ من الاختبارات تنجح**
- ✅ **جميع اختبارات الأولوية العالية تنجح**
- ✅ **لا توجد أخطاء في الحسابات المالية**
- ✅ **جميع رسائل التنبيه تُرسل بشكل صحيح**

### **نقاط الفحص الحرجة:**
1. **دقة الحسابات المالية** - يجب أن تكون دقيقة 100%
2. **استخدام البدائل** - عند فشل البيانات التاريخية
3. **منع الإشارات المكررة** - إلا في حالة التعزيز
4. **تنبيهات الإدارة** - في جميع الحالات الاستثنائية
5. **تحديث البيانات** - في الأعمدة الصحيحة

هذا الاختبار الشامل يضمن أن جميع جوانب النظام تعمل بشكل صحيح ومتسق مع المتطلبات الجديدة.
