import logging
import os
import sys
from threading import Thread
import traceback
import socket
import asyncio


# Set up security event logging
def log_security_events():
    security_logger = logging.getLogger('security')
    if not security_logger.handlers:
        security_file_handler = logging.FileHandler('security.log')
        security_file_handler.setFormatter(
            logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        security_logger.addHandler(security_file_handler)
        security_logger.setLevel(logging.INFO)
    return security_logger

security_logger = log_security_events()
# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Check if a port is in use
def is_port_in_use(port):
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

# Find an available port starting from the given port
def find_available_port(start_port):
    port = start_port
    while is_port_in_use(port):
        logger.warning(f"Port {port} is already in use, trying port {port+1}")
        port += 1
    return port

# Try importing all required components, providing useful error messages if they fail
try:
    from improved_app_init import create_secure_app
    from server import (
        handle_alert,
        start_server,
        dashboard,
        main2
    )
    # Import bot and dispatcher specifically
    from auth import bot, dp
    # Import required aiogram components
    from aiogram import executor
    
    # Import our custom command registration function
    try:
        from process_data_utils import register_bot_handlers, setup_bot_commands
        CUSTOM_HANDLERS_AVAILABLE = True
        logger.info("Custom handler registration is available")
    except ImportError:
        CUSTOM_HANDLERS_AVAILABLE = False
        logger.warning("Custom handler registration is not available, will use fallback")

except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    print(f"Error: Could not import required modules: {e}", file=sys.stderr)
    print("Make sure all required files exist and are in the correct locations.", file=sys.stderr)
    sys.exit(1)

# Setup a fallback handler registration function
def fallback_register_handlers():
    """Fallback function to register critical command handlers"""
    try:
        # Import handlers for problematic commands
        from update_google_sheet import update_sheet
        from reset_counter import reset_counters_command
        from comp import comp_start_handler
        
        # Register commands that weren't working
        dp.register_message_handler(update_sheet, commands=['comp'])
        dp.register_message_handler(reset_counters_command, commands=['reset_counters'])
        
        # Register a custom handler for /upgrade with handling for extra spaces
        import re
        
        @dp.message_handler(lambda message: message.text and message.text.strip().startswith('/upgrade'))
        async def robust_upgrade_handler(message):
            try:
                # Extract user_id and days
                parts = re.split(r'\s+', message.text.strip())[1:]
                if len(parts) >= 2:
                    user_id = parts[0]
                    days = parts[1]
                    
                    # Import the upgrade function
                    try:
                        from upgrade import upgrade_user
                        await upgrade_user(message, user_id, days)
                    except ImportError:
                        from update_google_sheet import upgrade_handler
                        # Reformat the message for the handler
                        message.text = f"/upgrade {user_id} {days}"
                        await upgrade_handler(message)
                else:
                    await message.reply("❌ Invalid format. Use: /upgrade [user_id] [days]")
            except Exception as e:
                logger.error(f"Error in robust_upgrade_handler: {e}")
                await message.reply("❌ Error processing upgrade command")
                
        logger.info("Registered fallback handlers for critical commands")
        return True
    except Exception as e:
        logger.error(f"Error registering fallback handlers: {e}")
        return False

def setup_routes(app):
    """Set up routes for the Flask application"""
    try:
        # Dashboard route
        app.route('/dashboard')(dashboard)
        
        # API routes
        app.route('/webhook', methods=["POST"])(handle_alert)
        
        # Root route
        app.route('/')(main2)
        
        return app
    except Exception as e:
        logger.error(f"Error setting up routes: {e}", exc_info=True)
        sys.exit(1)

def start_flask_app(app, port):
    """Start the Flask application on the specified port"""
    try:
        logger.info(f"Starting Flask app on port {port}")
        app.run(host='0.0.0.0', port=port, debug=False)
    except Exception as e:
        logger.error(f"Error starting Flask app: {e}", exc_info=True)
        sys.exit(1)

def start_api_server(port):
    """Start the API server on the specified port"""
    try:
        logger.info(f"Starting API server on port {port}")
        start_server(host='0.0.0.0', port=port)
    except Exception as e:
        logger.error(f"Error starting API server: {e}", exc_info=True)

# Update the telegram bot starting function to use process_data_utils
def start_telegram_bot():
    """Start the Telegram bot"""
    try:
        logger.info("Starting Telegram bot")
        
        # Import necessary modules
        import asyncio
        
        # Create a new event loop for this thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # Register handlers using our custom function if available
        if CUSTOM_HANDLERS_AVAILABLE:
            logger.info("Using custom handler registration")
            register_bot_handlers(dp)
        else:
            logger.info("Using fallback handler registration")
            fallback_register_handlers()
        
        # Set up bot commands in menu
        if CUSTOM_HANDLERS_AVAILABLE:
            # Use our custom setup function
            loop.run_until_complete(setup_bot_commands(bot))
        else:
            # Fallback to a simpler command setup
            from aiogram.types import BotCommand
            loop.run_until_complete(bot.set_my_commands([
                BotCommand("start", "Start the bot"),
                BotCommand("help", "Get help"),
                BotCommand("menu", "Main menu"),
                BotCommand("comp", "Compare stocks"),
                BotCommand("stock", "Stock information")
            ]))
        
        # Start the bot using aiogram's executor with our event loop
        executor.start_polling(dp, loop=loop, skip_updates=True)
    except Exception as e:
        logger.error(f"Error starting Telegram bot: {e}", exc_info=True)

def main():
    """Main function to start the application"""
    try:
        logger.info("Starting Stock Analyzer Bot application")
        
        # Create the Flask app
        app = create_secure_app()
        
        # Set up routes
        app = setup_routes(app)
        
        # Check which components to start (from environment variables or arguments)
        start_web = os.environ.get("START_WEB", "true").lower() == "true"
        start_api = os.environ.get("START_API", "false").lower() == "true"
        start_bot = os.environ.get("START_BOT", "true").lower() == "true"
        
        # Get port configurations
        web_port = int(os.environ.get("WEB_PORT", 9000))
        api_port = int(os.environ.get("API_PORT", 8000))
        
        # Make sure we have available ports
        if start_web:
            web_port = find_available_port(web_port)
            os.environ["WEB_PORT"] = str(web_port)
            logger.info(f"Web server will use port {web_port}")
            
        if start_api:
            # Make sure API port doesn't conflict with web port
            if start_web and api_port == web_port:
                api_port = find_available_port(web_port + 1)
            else:
                api_port = find_available_port(api_port)
            os.environ["API_PORT"] = str(api_port)
            logger.info(f"API server will use port {api_port}")
        
        # Start Telegram bot in a separate thread if requested
        if start_bot:
            bot_thread = Thread(target=start_telegram_bot)
            bot_thread.daemon = True
            bot_thread.start()
            logger.info("Telegram bot thread started")
        
        # Start components based on configuration
        if start_web and start_api:
            # Start API in a separate thread
            api_thread = Thread(target=start_api_server, args=(api_port,))
            api_thread.daemon = True
            api_thread.start()
            
            # Start web app (this will block)
            start_flask_app(app, web_port)
        elif start_web:
            # Only start web app
            start_flask_app(app, web_port)
        elif start_api:
            # Only start API server
            start_api_server(api_port)
        elif start_bot and not (start_web or start_api):
            # If only the bot is running, we need to keep the main thread alive
            logger.info("Running bot only mode, press Ctrl+C to exit")
            try:
                # Keep the main thread alive with a simple loop
                while True:
                    time.sleep(60)
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt, shutting down")
        else:
            logger.warning("No components configured to start. Set START_WEB=true or START_API=true")
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    # Import time for sleep functions
    import time
    main()
