import re
import logging
from functools import wraps
from flask import request, abort, jsonify
import ipaddress

logger = logging.getLogger(__name__)

# Configure logging
logging.basicConfig(level=logging.INFO)

# List of common malicious patterns
SUSPICIOUS_PATTERNS = [
    r'(\\x[0-9a-f]{2})+',  # Hex-encoded content
    r'SELECT\s+.*FROM',     # SQL injection attempts
    r'DROP\s+TABLE',        # SQL injection attempts
    r'UNION\s+SELECT',      # SQL injection attempts
    r'<script>',            # XSS attempts
    r'eval\(',              # JavaScript injection
    r'document\.cookie',    # <PERSON>ie stealing
]

# Compile the patterns for efficiency
COMPILED_PATTERNS = [re.compile(pattern, re.IGNORECASE) for pattern in SUSPICIOUS_PATTERNS]

def is_suspicious_request(request):
    """Check if the request contains suspicious patterns"""
    # Check URL for suspicious patterns
    for pattern in COMPILED_PATTERNS:
        if pattern.search(request.url):
            logger.warning(f"Suspicious URL detected: {request.url}")
            return True
            
    # Check headers for suspicious content
    for header, value in request.headers:
        for pattern in COMPILED_PATTERNS:
            if pattern.search(value):
                logger.warning(f"Suspicious header detected: {header}: {value}")
                return True
                
    # Check form data for suspicious content
    for key, value in request.form.items():
        for pattern in COMPILED_PATTERNS:
            if pattern.search(value):
                logger.warning(f"Suspicious form data detected: {key}: {value}")
                return True
    
    return False

def security_middleware(app):
    """Add security middleware to Flask app"""
    @app.before_request
    def check_security():
        """Check requests for security issues before processing"""
        # Check if the request contains suspicious patterns
        if is_suspicious_request(request):
            logger.warning(f"Blocked suspicious request from {request.remote_addr}")
            abort(403)  # Forbidden
            
        # Check if request contains binary data in headers
        for header, value in request.headers:
            try:
                # Try to decode header value as ASCII
                value.encode('ascii').decode('ascii')
            except (UnicodeDecodeError, UnicodeEncodeError):
                # Binary data detected in header
                logger.warning(f"Binary data detected in header {header} from {request.remote_addr}")
                abort(400)  # Bad request
        
        # Log requests with unusual HTTP methods
        if request.method not in ['GET', 'POST', 'PUT', 'DELETE', 'HEAD', 'OPTIONS']:
            logger.warning(f"Unusual HTTP method {request.method} from {request.remote_addr}")
        
    return app
