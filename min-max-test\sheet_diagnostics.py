import logging
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from config import sheet_name
import json

def diagnose_sheet_structure():
    """Examine the Google Sheet structure to diagnose column order issues."""
    logging.basicConfig(level=logging.INFO)
    
    try:
        # Connect to Google Sheets
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        with open('json_file.json', 'r') as f:
            creds = json.load(f)
        credentials = ServiceAccountCredentials.from_json_keyfile_dict(creds, scope)
        client = gspread.authorize(credentials)
        
        # Open the users sheet
        users_sheet = client.open(sheet_name).worksheet("users")
        
        # Get header row
        header_row = users_sheet.row_values(1)
        logging.info(f"Header row: {header_row}")
        
        # Get sample data
        sample_rows = users_sheet.get_all_values()[:5]  # Get first 5 rows
        logging.info("Sample data rows:")
        for idx, row in enumerate(sample_rows):
            logging.info(f"Row {idx+1}: {row}")
        
        # Suggest proper column mappings
        logging.info("\nSuggested column mappings:")
        for idx, header in enumerate(header_row):
            logging.info(f"Column {idx}: '{header}'")
            
        logging.info("\nTo fix the subscription info display, update the column indices in get_subscription_date function.")
        
    except Exception as e:
        logging.error(f"Error diagnosing sheet structure: {e}")

if __name__ == "__main__":
    diagnose_sheet_structure()
