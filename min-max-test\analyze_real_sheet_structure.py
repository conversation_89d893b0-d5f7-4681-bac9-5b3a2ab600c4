#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحليل البنية الحقيقية لجدول المستخدمين
Analyze real user sheet structure
"""

import logging
import datetime
from typing import List, Dict

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_real_sheet_structure():
    """تحليل البنية الحقيقية للجدول"""
    try:
        from auth import open_google_sheet
        
        print("🔍 تحليل البنية الحقيقية لجدول المستخدمين...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        # جلب الرؤوس
        headers = sheet2.row_values(1)
        print(f"📋 الرؤوس الحالية: {headers}")
        
        # جلب أول 5 سجلات للتحليل
        all_records = sheet2.get_all_records()
        print(f"📊 إجمالي السجلات: {len(all_records)}")
        
        if all_records:
            print("\n📝 تحليل أول 5 سجلات:")
            for i, record in enumerate(all_records[:5], 1):
                print(f"\n   السجل {i}:")
                for key, value in record.items():
                    if value:  # عرض القيم غير الفارغة فقط
                        print(f"      '{key}': '{value}'")
            
            # تحليل البنية المتوقعة بناءً على المثال المعطى
            print("\n🔍 تحليل البنية المتوقعة:")
            print("   المثال المعطى: 7252774174	0	free	2025-06-21	Ahmed	Abdelrahman")
            print("   البنية المتوقعة:")
            print("      العمود 1: user_id (7252774174)")
            print("      العمود 2: count (0)")
            print("      العمود 3: subscription_type (free)")
            print("      العمود 4: end_date (2025-06-21)")
            print("      العمود 5: first_name (Ahmed)")
            print("      العمود 6: last_name (Abdelrahman)")
            
            # محاولة تطبيق هذا التحليل على البيانات الفعلية
            print("\n🧪 تطبيق التحليل على البيانات الفعلية:")
            
            free_users_count = 0
            active_users_count = 0
            sample_users = []
            
            for record in all_records[:10]:  # تحليل أول 10 سجلات
                values = list(record.values())
                
                # محاولة استخراج البيانات حسب الموضع
                if len(values) >= 6:
                    try:
                        user_id = str(values[0]) if values[0] else ""
                        count = int(values[1]) if str(values[1]).isdigit() else 0
                        subscription_type = str(values[2]) if values[2] else "free"
                        end_date = str(values[3]) if values[3] else ""
                        first_name = str(values[4]) if values[4] else ""
                        last_name = str(values[5]) if values[5] else ""
                        
                        # التحقق من صحة معرف المستخدم
                        if user_id and len(user_id) >= 8 and user_id.isdigit():
                            if subscription_type.lower() == 'free':
                                free_users_count += 1
                                if count > 0:
                                    active_users_count += 1
                                
                                sample_users.append({
                                    'user_id': user_id,
                                    'count': count,
                                    'subscription_type': subscription_type,
                                    'end_date': end_date,
                                    'first_name': first_name,
                                    'last_name': last_name
                                })
                    except Exception as e:
                        logger.warning(f"خطأ في تحليل السجل: {e}")
                        continue
            
            print(f"📊 نتائج التحليل:")
            print(f"   - مستخدمين مجانيين: {free_users_count}")
            print(f"   - مستخدمين نشطين: {active_users_count}")
            
            if sample_users:
                print(f"\n📝 عينة من المستخدمين المجانيين:")
                for i, user in enumerate(sample_users[:3], 1):
                    print(f"   {i}. {user['first_name']} {user['last_name']} ({user['user_id']}) - استخدامات: {user['count']}")
            
            return {
                'headers': headers,
                'total_records': len(all_records),
                'free_users_count': free_users_count,
                'active_users_count': active_users_count,
                'sample_users': sample_users,
                'structure_analysis': {
                    'user_id_column': headers[0] if len(headers) > 0 else None,
                    'count_column': headers[1] if len(headers) > 1 else None,
                    'subscription_type_column': headers[2] if len(headers) > 2 else None,
                    'end_date_column': headers[3] if len(headers) > 3 else None,
                    'first_name_column': headers[4] if len(headers) > 4 else None,
                    'last_name_column': headers[5] if len(headers) > 5 else None,
                }
            }
        
        return None
        
    except Exception as e:
        print(f"❌ خطأ في تحليل البنية: {e}")
        return None

def create_updated_free_users_manager(analysis):
    """إنشاء نسخة محدثة من FreeUsersManager بناءً على التحليل"""
    try:
        print("\n🔧 إنشاء نسخة محدثة من FreeUsersManager...")
        
        # تحديد أسماء الأعمدة الفعلية
        headers = analysis['headers']
        
        updated_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشتركين المجانيين - محدث للبنية الحقيقية
Free Users Management System - Updated for Real Structure
"""

import logging
import datetime
from typing import List, Dict, Optional
from auth import open_google_sheet, bot
from user_limit import UserManager
from promo_codes import PromoCodeManager

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

class FreeUsersManager:
    """مدير المشتركين المجانيين - محدث للبنية الحقيقية"""
    
    @staticmethod
    def get_all_free_users() -> List[Dict]:
        """الحصول على جميع المشتركين المجانيين"""
        try:
            all_records = sheet2.get_all_records()
            free_users = []
            
            for record in all_records:
                try:
                    # استخراج البيانات حسب الموضع (بناءً على البنية الحقيقية)
                    values = list(record.values())
                    
                    if len(values) < 6:
                        continue
                    
                    # تحديد البيانات حسب الموضع
                    user_id = str(values[0]) if values[0] else ""
                    count = int(values[1]) if str(values[1]).isdigit() else 0
                    subscription_type = str(values[2]).lower() if values[2] else "free"
                    end_date_str = str(values[3]) if values[3] else ""
                    first_name = str(values[4]) if values[4] else ""
                    last_name = str(values[5]) if values[5] else ""
                    
                    # التحقق من صحة معرف المستخدم
                    if not user_id or len(user_id) < 8 or not user_id.isdigit():
                        continue
                    
                    # التحقق من انتهاء الاشتراك
                    is_expired = False
                    if subscription_type in ['trail', 'paid'] and end_date_str:
                        try:
                            end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                            is_expired = datetime.date.today() > end_date
                        except ValueError:
                            is_expired = True
                    
                    # إضافة المستخدمين المجانيين أو المنتهي اشتراكهم
                    if subscription_type == 'free' or is_expired:
                        free_users.append({{
                            'user_id': user_id,
                            'subscription_type': subscription_type,
                            'end_date': end_date_str,
                            'first_name': first_name,
                            'last_name': last_name,
                            'count': count,
                            'is_expired': is_expired
                        }})
                
                except Exception as e:
                    logger.warning(f"خطأ في معالجة سجل: {{e}}")
                    continue
            
            logger.info(f"تم العثور على {{len(free_users)}} مستخدم مجاني")
            return free_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين المجانيين: {{e}}")
            return []
    
    @staticmethod
    def get_active_free_users() -> List[Dict]:
        """الحصول على المشتركين المجانيين النشطين"""
        try:
            all_free_users = FreeUsersManager.get_all_free_users()
            active_users = []
            
            for user in all_free_users:
                # المستخدمين النشطين هم من لديهم استخدامات
                if user['count'] > 0:
                    active_users.append(user)
            
            logger.info(f"تم العثور على {{len(active_users)}} مستخدم مجاني نشط")
            return active_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين النشطين: {{e}}")
            return []
    
    @staticmethod
    def create_bulk_trial_codes(count: int, days: int = 7, note: str = "كود تجربة مجانية للمشتركين") -> List[str]:
        """إنشاء مجموعة من أكواد التجربة المجانية"""
        try:
            codes = []
            for i in range(count):
                code = PromoCodeManager.create_trial_code(
                    days=days,
                    expiry_days=30,
                    note=f"{{note}} - الدفعة {{i+1}}"
                )
                if code:
                    codes.append(code)
                    logger.info(f"تم إنشاء الكود {{i+1}}/{{count}}: {{code}}")
            
            logger.info(f"تم إنشاء {{len(codes)}} كود تجربة مجانية")
            return codes
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الأكواد المجمعة: {{e}}")
            return []
    
    @staticmethod
    async def send_promo_code_to_user(user_id: str, promo_code: str, user_name: str = "") -> bool:
        """إرسال كود البرومو لمستخدم واحد"""
        try:
            message = f"""
🎉 **مفاجأة خاصة لك!**

مرحباً {{user_name}}! 👋

🎁 **كود تجربة مجانية لمدة 7 أيام:**
`{{promo_code}}`

✨ **كيفية التفعيل:**
1️⃣ انسخ الكود أعلاه
2️⃣ اكتب الأمر: `/redeem {{promo_code}}`
3️⃣ استمتع بـ 7 أيام تجربة مجانية كاملة!

🚀 **مزايا التجربة المجانية:**
• تحليلات غير محدودة للأسهم
• مؤشرات فنية متقدمة
• تنبيهات فورية
• دعم فني مميز

⏰ **الكود صالح لمدة 30 يوماً**
💡 **لا تفوت هذه الفرصة!**
"""
            
            await bot.send_message(chat_id=user_id, text=message, parse_mode="Markdown")
            logger.info(f"تم إرسال كود البرومو {{promo_code}} للمستخدم {{user_id}}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الكود للمستخدم {{user_id}}: {{e}}")
            return False
    
    @staticmethod
    async def send_bulk_promo_codes(target_users: str = "active") -> Dict:
        """إرسال أكواد البرومو بشكل مجمع"""
        try:
            # تحديد المستخدمين المستهدفين
            if target_users == "active":
                users = FreeUsersManager.get_active_free_users()
                target_desc = "المستخدمين النشطين"
            elif target_users == "all":
                users = FreeUsersManager.get_all_free_users()
                target_desc = "جميع المستخدمين المجانيين"
            else:
                return {{"success": False, "error": "نوع المستخدمين غير صحيح"}}
            
            if not users:
                return {{"success": False, "error": "لا توجد مستخدمين للإرسال إليهم"}}
            
            # إنشاء أكواد البرومو
            codes = FreeUsersManager.create_bulk_trial_codes(
                count=len(users),
                days=7,
                note=f"كود تجربة مجانية - {{target_desc}}"
            )
            
            if len(codes) != len(users):
                return {{"success": False, "error": "فشل في إنشاء العدد المطلوب من الأكواد"}}
            
            # إرسال الأكواد
            sent_count = 0
            failed_count = 0
            results = []
            
            for i, user in enumerate(users):
                user_id = user['user_id']
                user_name = f"{{user.get('first_name', '')}} {{user.get('last_name', '')}}".strip()
                if not user_name:
                    user_name = f"المستخدم {{user_id}}"
                
                success = await FreeUsersManager.send_promo_code_to_user(
                    user_id=user_id,
                    promo_code=codes[i],
                    user_name=user_name
                )
                
                if success:
                    sent_count += 1
                    results.append({{"user_id": user_id, "code": codes[i], "status": "sent"}})
                else:
                    failed_count += 1
                    results.append({{"user_id": user_id, "code": codes[i], "status": "failed"}})
                
                # تأخير بسيط لتجنب حدود التليجرام
                import asyncio
                await asyncio.sleep(0.5)
            
            return {{
                "success": True,
                "total_users": len(users),
                "sent_count": sent_count,
                "failed_count": failed_count,
                "target_type": target_desc,
                "results": results
            }}
            
        except Exception as e:
            logger.error(f"خطأ في الإرسال المجمع: {{e}}")
            return {{"success": False, "error": str(e)}}
    
    @staticmethod
    def get_free_users_statistics() -> Dict:
        """إحصائيات المشتركين المجانيين"""
        try:
            all_free = FreeUsersManager.get_all_free_users()
            active_free = FreeUsersManager.get_active_free_users()
            
            # تصنيف المستخدمين
            never_used = [u for u in all_free if u['count'] == 0]
            light_users = [u for u in all_free if 1 <= u['count'] <= 2]
            regular_users = [u for u in all_free if 3 <= u['count'] <= 5]
            heavy_users = [u for u in all_free if u['count'] > 5]
            expired_users = [u for u in all_free if u['is_expired']]
            
            return {{
                "total_free_users": len(all_free),
                "active_free_users": len(active_free),
                "never_used": len(never_used),
                "light_users": len(light_users),
                "regular_users": len(regular_users),
                "heavy_users": len(heavy_users),
                "expired_users": len(expired_users),
                "activity_rate": round((len(active_free) / len(all_free) * 100), 2) if all_free else 0
            }}
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {{e}}")
            return {{}}

# باقي الدوال تبقى كما هي...
'''
        
        # حفظ النسخة المحدثة
        with open('free_users_manager_real_structure.py', 'w', encoding='utf-8') as f:
            f.write(updated_code)
        
        print("✅ تم إنشاء free_users_manager_real_structure.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة المحدثة: {e}")
        return False

def test_real_structure():
    """اختبار النظام مع البنية الحقيقية"""
    try:
        print("\n🧪 اختبار النظام مع البنية الحقيقية...")
        
        # استيراد النسخة المحدثة
        import importlib.util
        spec = importlib.util.spec_from_file_location("free_users_manager_real", "free_users_manager_real_structure.py")
        free_users_real = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(free_users_real)
        
        # اختبار جلب المستخدمين
        all_free = free_users_real.FreeUsersManager.get_all_free_users()
        active_free = free_users_real.FreeUsersManager.get_active_free_users()
        
        print(f"✅ إجمالي المستخدمين المجانيين: {len(all_free)}")
        print(f"✅ المستخدمين النشطين: {len(active_free)}")
        
        if all_free:
            print("📝 عينة من المستخدمين:")
            for i, user in enumerate(all_free[:3], 1):
                print(f"   {i}. {user['first_name']} {user['last_name']} ({user['user_id']}) - استخدامات: {user['count']}")
        
        # اختبار الإحصائيات
        stats = free_users_real.FreeUsersManager.get_free_users_statistics()
        if stats:
            print(f"\n📊 إحصائيات:")
            print(f"   - إجمالي المجانيين: {stats['total_free_users']}")
            print(f"   - النشطين: {stats['active_free_users']}")
            print(f"   - معدل النشاط: {stats['activity_rate']}%")
        
        return len(all_free), len(active_free)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return 0, 0

def main():
    """الدالة الرئيسية"""
    print("🔍 تحليل البنية الحقيقية لجدول المستخدمين")
    print("=" * 60)
    
    # 1. تحليل البنية الحقيقية
    analysis = analyze_real_sheet_structure()
    
    if not analysis:
        print("❌ فشل في تحليل البنية")
        return
    
    print(f"\n📊 نتائج التحليل:")
    print(f"   - إجمالي السجلات: {analysis['total_records']}")
    print(f"   - مستخدمين مجانيين: {analysis['free_users_count']}")
    print(f"   - مستخدمين نشطين: {analysis['active_users_count']}")
    
    # 2. إنشاء نسخة محدثة من FreeUsersManager
    if analysis['free_users_count'] > 0:
        success = create_updated_free_users_manager(analysis)
        
        if success:
            print("\n✅ تم إنشاء النسخة المحدثة من FreeUsersManager")
            
            # 3. اختبار النظام المحدث
            free_count, active_count = test_real_structure()
            
            if free_count > 0:
                print(f"\n🎉 النظام يعمل مع البنية الحقيقية!")
                print(f"✅ تم العثور على {free_count} مستخدم مجاني")
                print(f"✅ منهم {active_count} مستخدم نشط")
                
                print("\n🚀 للاستخدام:")
                print("   1. استبدل free_users_manager.py بالنسخة الجديدة")
                print("   2. اختبر الأوامر: /list_free_users, /send_promo_active")
            else:
                print("❌ لا يزال لا توجد مستخدمين مجانيين")
        else:
            print("❌ فشل في إنشاء النسخة المحدثة")
    else:
        print("⚠️ لا توجد مستخدمين مجانيين في البيانات الحالية")

if __name__ == "__main__":
    main()
