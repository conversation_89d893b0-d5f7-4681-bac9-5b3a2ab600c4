"""
Utility functions to handle model compatibility issues between different TensorFlow versions.
"""

import logging
import tensorflow as tf
from tensorflow.keras.models import load_model
import tempfile
import json
import h5py
import os

logger = logging.getLogger(__name__)

def load_model_with_compatibility(model_path):
    """
    Load a Keras model with compatibility fixes for different TensorFlow versions.
    
    Args:
        model_path (str): Path to the model file
        
    Returns:
        The loaded model or None if loading fails
    """
    try:
        # First try normal loading
        model = load_model(model_path)
        logger.info("Model loaded successfully")
        return model
    except ValueError as e:
        logger.warning(f"Standard model loading failed: {e}")
        
        # Check if it's the time_major issue
        if 'time_major' in str(e):
            return _fix_time_major_issue(model_path)
        else:
            logger.error(f"Unknown model loading error: {e}")
            return None

def _fix_time_major_issue(model_path):
    """
    Fix the 'time_major' parameter issue in LSTM layers.
    
    This function:
    1. Opens the H5 file
    2. Modifies the model_config to remove 'time_major' attributes
    3. Saves to a temporary file
    4. Loads the model from the temporary file
    
    Args:
        model_path (str): Path to the original model file
        
    Returns:
        The loaded model or None if fixing fails
    """
    try:
        # Open the H5 file
        with h5py.File(model_path, 'r') as h5file:
            # Check if it has the model_config attribute
            if 'model_config' in h5file.attrs:
                # Get the model config
                model_config = json.loads(h5file.attrs['model_config'])
                
                # Function to recursively remove 'time_major' from the config
                def remove_time_major(config):
                    if isinstance(config, dict):
                        # Remove time_major if present
                        if 'time_major' in config:
                            del config['time_major']
                        
                        # Process all dictionary values
                        for key, value in list(config.items()):
                            config[key] = remove_time_major(value)
                    
                    elif isinstance(config, list):
                        # Process all list items
                        for i in range(len(config)):
                            config[i] = remove_time_major(config[i])
                    
                    return config
                
                # Remove time_major from the config
                modified_config = remove_time_major(model_config)
                
                # Create a temporary file to save the modified model
                temp_dir = tempfile.gettempdir()
                temp_model_path = os.path.join(temp_dir, 'temp_model.h5')
                
                # Copy the original H5 file to the temporary file
                with h5py.File(temp_model_path, 'w') as temp_h5:
                    # Copy all groups and datasets
                    for key in h5file.keys():
                        h5file.copy(key, temp_h5)
                    
                    # Copy all attributes except model_config
                    for attr_key in h5file.attrs.keys():
                        if attr_key != 'model_config':
                            temp_h5.attrs[attr_key] = h5file.attrs[attr_key]
                    
                    # Set the modified model_config
                    temp_h5.attrs['model_config'] = json.dumps(modified_config)
                
                # Try to load the modified model
                try:
                    # Define a custom object to handle any remaining issues
                    class CustomLSTM(tf.keras.layers.LSTM):
                        def __init__(self, *args, **kwargs):
                            # Remove problematic parameters
                            kwargs.pop('time_major', None)
                            super(CustomLSTM, self).__init__(*args, **kwargs)
                    
                    custom_objects = {'LSTM': CustomLSTM}
                    model = load_model(temp_model_path, custom_objects=custom_objects)
                    logger.info("Model loaded successfully after fixing time_major issue")
                    
                    # Clean up the temporary file
                    try:
                        os.remove(temp_model_path)
                    except:
                        pass
                        
                    return model
                except Exception as e:
                    logger.error(f"Failed to load modified model: {e}")
                    return None
            else:
                logger.error("H5 file does not contain model_config attribute")
                return None
    except Exception as e:
        logger.error(f"Error while fixing model: {e}")
        return None
