#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مجموعة اختبارات شاملة لنظام إشارات التداول
System Testing Suite for Trading Signals System
"""

import requests
import json
import time
import asyncio
import logging
from datetime import datetime
import sys
import traceback

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('testing_results.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TradingSystemTester:
    def __init__(self, base_url="http://localhost:9000"):
        self.base_url = base_url
        self.test_results = []
        self.start_time = datetime.now()
        
    def log_test_result(self, test_name, status, details="", response_time=None):
        """تسجيل نتيجة الاختبار"""
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        logger.info(f"{status_emoji} {test_name}: {status} - {details}")
        
    def test_server_connectivity(self):
        """اختبار الاتصال بالسيرفر"""
        test_name = "Server Connectivity"
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/", timeout=10)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            if response.status_code == 200:
                self.log_test_result(test_name, "PASS", f"Status: {response.status_code}", response_time)
                return True
            else:
                self.log_test_result(test_name, "FAIL", f"Status: {response.status_code}", response_time)
                return False
                
        except requests.exceptions.ConnectionError:
            self.log_test_result(test_name, "FAIL", "Connection refused - Server not running")
            return False
        except requests.exceptions.Timeout:
            self.log_test_result(test_name, "FAIL", "Request timeout")
            return False
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_health_endpoint(self):
        """اختبار endpoint الصحة"""
        test_name = "Health Endpoint"
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=5)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'status' in data:
                        self.log_test_result(test_name, "PASS", f"Health status: {data.get('status')}", response_time)
                        return True
                    else:
                        self.log_test_result(test_name, "WARN", "No status field in response", response_time)
                        return False
                except json.JSONDecodeError:
                    self.log_test_result(test_name, "FAIL", "Invalid JSON response", response_time)
                    return False
            else:
                self.log_test_result(test_name, "FAIL", f"Status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_webhook_endpoint_basic(self):
        """اختبار webhook الأساسي"""
        test_name = "Webhook Basic"
        try:
            # إرسال طلب فارغ للتحقق من معالجة الأخطاء
            start_time = time.time()
            response = requests.post(f"{self.base_url}/webhook", timeout=10)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            # نتوقع خطأ 400 لأن البيانات فارغة
            if response.status_code == 400:
                self.log_test_result(test_name, "PASS", "Correctly handles empty data", response_time)
                return True
            else:
                self.log_test_result(test_name, "WARN", f"Unexpected status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_webhook_invalid_json(self):
        """اختبار webhook مع JSON غير صحيح"""
        test_name = "Webhook Invalid JSON"
        try:
            headers = {'Content-Type': 'application/json'}
            invalid_json = "{'invalid': json}"
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=invalid_json,
                headers=headers,
                timeout=10
            )
            response_time = round((time.time() - start_time) * 1000, 2)
            
            # نتوقع خطأ 400 للـ JSON غير الصحيح
            if response.status_code == 400:
                self.log_test_result(test_name, "PASS", "Correctly handles invalid JSON", response_time)
                return True
            else:
                self.log_test_result(test_name, "WARN", f"Status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_webhook_valid_buy_signal(self):
        """اختبار إشارة شراء صحيحة"""
        test_name = "Valid Buy Signal"
        try:
            headers = {'Content-Type': 'application/json'}
            buy_signal = {
                "stock_code": "TEST",
                "report": "buy",
                "buy_price": "10.50",
                "tp1": "11.00",
                "tp2": "11.50",
                "tp3": "12.00",
                "sl": "10.00"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(buy_signal),
                headers=headers,
                timeout=15
            )
            response_time = round((time.time() - start_time) * 1000, 2)
            
            if response.status_code == 200:
                self.log_test_result(test_name, "PASS", "Buy signal processed successfully", response_time)
                return True
            else:
                try:
                    error_data = response.json()
                    self.log_test_result(test_name, "FAIL", f"Status: {response.status_code}, Error: {error_data}", response_time)
                except:
                    self.log_test_result(test_name, "FAIL", f"Status: {response.status_code}, Response: {response.text}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_webhook_invalid_prices(self):
        """اختبار إشارة بأسعار غير صحيحة"""
        test_name = "Invalid Prices Signal"
        try:
            headers = {'Content-Type': 'application/json'}
            invalid_signal = {
                "stock_code": "TEST",
                "report": "buy",
                "buy_price": "10.50",
                "tp1": "9.00",  # أقل من سعر الشراء
                "tp2": "11.50",
                "tp3": "12.00",
                "sl": "11.00"   # أعلى من سعر الشراء
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(invalid_signal),
                headers=headers,
                timeout=10
            )
            response_time = round((time.time() - start_time) * 1000, 2)
            
            # نتوقع رفض الإشارة
            if response.status_code == 400:
                self.log_test_result(test_name, "PASS", "Correctly rejects invalid prices", response_time)
                return True
            else:
                self.log_test_result(test_name, "WARN", f"Should reject invalid prices, Status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_webhook_missing_fields(self):
        """اختبار إشارة بحقول مفقودة"""
        test_name = "Missing Fields Signal"
        try:
            headers = {'Content-Type': 'application/json'}
            incomplete_signal = {
                "stock_code": "TEST",
                # report مفقود
                "buy_price": "10.50"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/webhook?jsonRequest=true",
                data=json.dumps(incomplete_signal),
                headers=headers,
                timeout=10
            )
            response_time = round((time.time() - start_time) * 1000, 2)
            
            # نتوقع رفض الإشارة
            if response.status_code == 400:
                self.log_test_result(test_name, "PASS", "Correctly rejects incomplete data", response_time)
                return True
            else:
                self.log_test_result(test_name, "WARN", f"Should reject incomplete data, Status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_performance_under_load(self):
        """اختبار الأداء تحت الضغط"""
        test_name = "Performance Under Load"
        try:
            headers = {'Content-Type': 'application/json'}
            test_signal = {
                "stock_code": "PERF",
                "report": "buy",
                "buy_price": "10.00",
                "tp1": "10.50",
                "tp2": "11.00",
                "tp3": "11.50",
                "sl": "9.50"
            }
            
            num_requests = 5  # عدد محدود للاختبار
            response_times = []
            successful_requests = 0
            
            for i in range(num_requests):
                try:
                    start_time = time.time()
                    response = requests.post(
                        f"{self.base_url}/webhook?jsonRequest=true",
                        data=json.dumps(test_signal),
                        headers=headers,
                        timeout=5
                    )
                    response_time = (time.time() - start_time) * 1000
                    response_times.append(response_time)
                    
                    if response.status_code in [200, 400]:  # 400 مقبول للإشارات المكررة
                        successful_requests += 1
                        
                    time.sleep(0.1)  # توقف قصير بين الطلبات
                    
                except Exception as e:
                    logger.warning(f"Request {i+1} failed: {e}")
            
            if response_times:
                avg_time = round(sum(response_times) / len(response_times), 2)
                max_time = round(max(response_times), 2)
                success_rate = (successful_requests / num_requests) * 100
                
                if avg_time < 5000 and success_rate > 80:  # أقل من 5 ثواني ونجاح أكثر من 80%
                    self.log_test_result(test_name, "PASS", f"Avg: {avg_time}ms, Max: {max_time}ms, Success: {success_rate}%")
                    return True
                else:
                    self.log_test_result(test_name, "WARN", f"Avg: {avg_time}ms, Max: {max_time}ms, Success: {success_rate}%")
                    return False
            else:
                self.log_test_result(test_name, "FAIL", "No successful requests")
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_dashboard_endpoint(self):
        """اختبار صفحة لوحة التحكم"""
        test_name = "Dashboard Endpoint"
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/dashboard?user_id=test", timeout=10)
            response_time = round((time.time() - start_time) * 1000, 2)
            
            if response.status_code == 200:
                if "html" in response.headers.get('content-type', '').lower():
                    self.log_test_result(test_name, "PASS", "Dashboard loads successfully", response_time)
                    return True
                else:
                    self.log_test_result(test_name, "WARN", "Dashboard returns non-HTML content", response_time)
                    return False
            else:
                self.log_test_result(test_name, "FAIL", f"Status: {response.status_code}", response_time)
                return False
                
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"Error: {str(e)}")
            return False
    
    def test_api_endpoints(self):
        """اختبار API endpoints المختلفة"""
        test_name = "API Endpoints"
        endpoints_to_test = [
            "/api/health",
            "/api/analyze?ticker=TEST",
            "/api/analyze/TEST"
        ]
        
        successful_endpoints = 0
        total_endpoints = len(endpoints_to_test)
        
        for endpoint in endpoints_to_test:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                response_time = round((time.time() - start_time) * 1000, 2)
                
                if response.status_code in [200, 404]:  # 404 مقبول لبعض endpoints
                    successful_endpoints += 1
                    logger.info(f"  ✓ {endpoint}: {response.status_code} ({response_time}ms)")
                else:
                    logger.warning(f"  ✗ {endpoint}: {response.status_code} ({response_time}ms)")
                    
            except Exception as e:
                logger.warning(f"  ✗ {endpoint}: Error - {str(e)}")
        
        success_rate = (successful_endpoints / total_endpoints) * 100
        if success_rate >= 70:
            self.log_test_result(test_name, "PASS", f"{successful_endpoints}/{total_endpoints} endpoints working ({success_rate:.1f}%)")
            return True
        else:
            self.log_test_result(test_name, "FAIL", f"{successful_endpoints}/{total_endpoints} endpoints working ({success_rate:.1f}%)")
            return False
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        test_name = "Error Handling"
        
        # اختبارات مختلفة للأخطاء
        error_tests = [
            ("Large payload", "x" * 10000),
            ("Special characters", "{'test': '测试'}"),
            ("Empty JSON", "{}"),
            ("Null values", '{"stock_code": null, "report": "buy"}')
        ]
        
        passed_tests = 0
        total_tests = len(error_tests)
        
        for test_desc, payload in error_tests:
            try:
                headers = {'Content-Type': 'application/json'}
                response = requests.post(
                    f"{self.base_url}/webhook?jsonRequest=true",
                    data=payload,
                    headers=headers,
                    timeout=5
                )
                
                # أي استجابة بدون crash تعتبر نجاح
                if response.status_code in [200, 400, 500]:
                    passed_tests += 1
                    logger.info(f"  ✓ {test_desc}: {response.status_code}")
                else:
                    logger.warning(f"  ✗ {test_desc}: {response.status_code}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"  ✗ {test_desc}: Timeout")
            except Exception as e:
                logger.warning(f"  ✗ {test_desc}: {str(e)}")
        
        success_rate = (passed_tests / total_tests) * 100
        if success_rate >= 75:
            self.log_test_result(test_name, "PASS", f"{passed_tests}/{total_tests} error tests handled ({success_rate:.1f}%)")
            return True
        else:
            self.log_test_result(test_name, "FAIL", f"{passed_tests}/{total_tests} error tests handled ({success_rate:.1f}%)")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء اختبارات نظام إشارات التداول")
        logger.info(f"📡 اختبار الخادم على: {self.base_url}")
        logger.info("=" * 60)
        
        # قائمة الاختبارات
        tests = [
            self.test_server_connectivity,
            self.test_health_endpoint,
            self.test_webhook_endpoint_basic,
            self.test_webhook_invalid_json,
            self.test_webhook_valid_buy_signal,
            self.test_webhook_invalid_prices,
            self.test_webhook_missing_fields,
            self.test_dashboard_endpoint,
            self.test_api_endpoints,
            self.test_error_handling,
            self.test_performance_under_load
        ]
        
        passed_tests = 0
        failed_tests = 0
        warned_tests = 0
        
        for test_func in tests:
            try:
                result = test_func()
                if result is True:
                    passed_tests += 1
                elif result is False:
                    failed_tests += 1
                else:
                    warned_tests += 1
            except Exception as e:
                logger.error(f"Test {test_func.__name__} crashed: {e}")
                failed_tests += 1
            
            time.sleep(0.5)  # توقف قصير بين الاختبارات
        
        # تلخيص النتائج
        total_tests = len(tests)
        self.generate_summary_report(total_tests, passed_tests, failed_tests, warned_tests)
    
    def generate_summary_report(self, total, passed, failed, warned):
        """إنشاء تقرير ملخص"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        logger.info("=" * 60)
        logger.info("📊 ملخص نتائج الاختبارات")
        logger.info("=" * 60)
        logger.info(f"⏱️  المدة الإجمالية: {duration:.2f} ثانية")
        logger.info(f"🔢 إجمالي الاختبارات: {total}")
        logger.info(f"✅ نجح: {passed}")
        logger.info(f"⚠️  تحذير: {warned}")
        logger.info(f"❌ فشل: {failed}")
        
        success_rate = (passed / total) * 100
        logger.info(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        # تقييم حالة النظام
        if success_rate >= 90:
            status = "ممتاز ✨"
            recommendation = "النظام يعمل بشكل ممتاز"
        elif success_rate >= 75:
            status = "جيد ✅"
            recommendation = "النظام يعمل بشكل جيد مع بعض التحسينات المطلوبة"
        elif success_rate >= 50:
            status = "مقبول ⚠️"
            recommendation = "النظام يعمل ولكن يحتاج تحسينات كبيرة"
        else:
            status = "ضعيف ❌"
            recommendation = "النظام يحتاج إصلاحات فورية"
        
        logger.info(f"🎯 حالة النظام: {status}")
        logger.info(f"💡 التوصية: {recommendation}")
        
        # حفظ التقرير المفصل
        self.save_detailed_report()
        
        logger.info("=" * 60)
        logger.info("📄 تم حفظ التقرير المفصل في: testing_results.json")
        logger.info("📋 تم حفظ السجل في: testing_results.log")
    
    def save_detailed_report(self):
        """حفظ التقرير المفصل"""
        report = {
            "test_session": {
                "start_time": self.start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": (datetime.now() - self.start_time).total_seconds(),
                "base_url": self.base_url
            },
            "summary": {
                "total_tests": len(self.test_results),
                "passed": len([r for r in self.test_results if r["status"] == "PASS"]),
                "failed": len([r for r in self.test_results if r["status"] == "FAIL"]),
                "warned": len([r for r in self.test_results if r["status"] == "WARN"])
            },
            "detailed_results": self.test_results
        }
        
        with open('testing_results.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

def main():
    """الدالة الرئيسية"""
    print("🔬 مجموعة اختبارات نظام إشارات التداول")
    print("=" * 50)
    
    # يمكن تغيير الـ URL حسب إعدادات السيرفر
    tester = TradingSystemTester("http://localhost:9000")
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        logger.info("\n🛑 تم إيقاف الاختبارات بواسطة المستخدم")
    except Exception as e:
        logger.error(f"🚨 خطأ في تشغيل الاختبارات: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
