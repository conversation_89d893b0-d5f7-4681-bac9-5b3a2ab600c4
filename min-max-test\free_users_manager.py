#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشتركين المجانيين وإرسال أكواد البرومو
Free Users Management and Promo Code Distribution System
"""

import logging
import datetime
from typing import List, Dict, Optional
from auth import open_google_sheet, bot
from user_limit import UserManager
from promo_codes import PromoCodeManager

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

# محاولة استخدام جدول المستخدمين المحدث
try:
    users_sheet = sheet2.spreadsheet.worksheet("users_data")
    logger.info("استخدام جدول users_data المحدث")
except:
    users_sheet = sheet2
    logger.info("استخدام الجدول الافتراضي sheet2")

class FreeUsersManager:
    """مدير المشتركين المجانيين"""
    
    @staticmethod
    def get_all_free_users() -> List[Dict]:
        """الحصول على جميع المشتركين المجانيين"""
        try:
            all_records = users_sheet.get_all_records()
            free_users = []

            for record in all_records:
                user_id = str(record.get('user_id', ''))
                subscription_type = record.get('subscription_type', 'free')
                end_date_str = record.get('end_date', '')

                # التحقق من صحة معرف المستخدم
                if not user_id or len(user_id) < 8:
                    continue
                
                # التحقق من انتهاء الاشتراك
                is_expired = False
                if subscription_type in ['trail', 'paid'] and end_date_str:
                    try:
                        end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                        is_expired = datetime.date.today() > end_date
                    except ValueError:
                        is_expired = True
                
                # إضافة المستخدمين المجانيين أو المنتهي اشتراكهم
                if subscription_type == 'free' or is_expired:
                    free_users.append({
                        'user_id': user_id,
                        'subscription_type': subscription_type,
                        'end_date': end_date_str,
                        'first_name': record.get('first_name', ''),
                        'last_name': record.get('last_name', ''),
                        'count': int(record.get('count', 0)),
                        'is_expired': is_expired
                    })
            
            logger.info(f"تم العثور على {len(free_users)} مستخدم مجاني")
            return free_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين المجانيين: {e}")
            return []
    
    @staticmethod
    def get_active_free_users() -> List[Dict]:
        """الحصول على المشتركين المجانيين النشطين (استخدموا البوت مؤخراً)"""
        try:
            all_free_users = FreeUsersManager.get_all_free_users()
            active_users = []
            
            # تحديد المستخدمين النشطين (استخدموا البوت في آخر 7 أيام)
            cutoff_date = datetime.date.today() - datetime.timedelta(days=7)
            
            for user in all_free_users:
                # إذا كان المستخدم لديه استخدام أو تاريخ حديث
                if user['count'] > 0:
                    try:
                        # التحقق من تاريخ آخر نشاط
                        if user['end_date']:
                            last_activity = datetime.datetime.strptime(user['end_date'], "%Y-%m-%d").date()
                            if last_activity >= cutoff_date:
                                active_users.append(user)
                        else:
                            # إذا لم يكن هناك تاريخ، نعتبره نشط
                            active_users.append(user)
                    except ValueError:
                        # في حالة خطأ في التاريخ، نعتبره نشط
                        active_users.append(user)
            
            logger.info(f"تم العثور على {len(active_users)} مستخدم مجاني نشط")
            return active_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين النشطين: {e}")
            return []
    
    @staticmethod
    def create_bulk_trial_codes(count: int, days: int = 7, note: str = "كود تجربة مجانية للمشتركين") -> List[str]:
        """إنشاء مجموعة من أكواد التجربة المجانية"""
        try:
            codes = []
            for i in range(count):
                code = PromoCodeManager.create_trial_code(
                    days=days,
                    expiry_days=30,  # صالح لمدة 30 يوم
                    note=f"{note} - الدفعة {i+1}"
                )
                if code:
                    codes.append(code)
                    logger.info(f"تم إنشاء الكود {i+1}/{count}: {code}")
            
            logger.info(f"تم إنشاء {len(codes)} كود تجربة مجانية")
            return codes
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الأكواد المجمعة: {e}")
            return []
    
    @staticmethod
    async def send_promo_code_to_user(user_id: str, promo_code: str, user_name: str = "") -> bool:
        """إرسال كود البرومو لمستخدم واحد"""
        try:
            message = f"""
🎉 **مفاجأة خاصة لك!**

مرحباً {user_name}! 👋

🎁 **كود تجربة مجانية لمدة 7 أيام:**
`{promo_code}`

✨ **كيفية التفعيل:**
1️⃣ انسخ الكود أعلاه
2️⃣ اكتب الأمر: `/redeem {promo_code}`
3️⃣ استمتع بـ 7 أيام تجربة مجانية كاملة!

🚀 **مزايا التجربة المجانية:**
• تحليلات غير محدودة للأسهم
• مؤشرات فنية متقدمة
• تنبيهات فورية
• دعم فني مميز

⏰ **الكود صالح لمدة 30 يوماً**
💡 **لا تفوت هذه الفرصة!**

---

🎉 **Special Surprise for You!**

Hello {user_name}! 👋

🎁 **7-Day Free Trial Code:**
`{promo_code}`

✨ **How to Activate:**
1️⃣ Copy the code above
2️⃣ Type: `/redeem {promo_code}`
3️⃣ Enjoy 7 days of full free trial!

🚀 **Trial Benefits:**
• Unlimited stock analysis
• Advanced technical indicators
• Instant alerts
• Premium support

⏰ **Code valid for 30 days**
💡 **Don't miss this opportunity!**
"""
            
            await bot.send_message(chat_id=user_id, text=message, parse_mode="Markdown")
            logger.info(f"تم إرسال كود البرومو {promo_code} للمستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الكود للمستخدم {user_id}: {e}")
            return False
    
    @staticmethod
    async def send_bulk_promo_codes(target_users: str = "active") -> Dict:
        """إرسال أكواد البرومو بشكل مجمع"""
        try:
            # تحديد المستخدمين المستهدفين
            if target_users == "active":
                users = FreeUsersManager.get_active_free_users()
                target_desc = "المستخدمين النشطين"
            elif target_users == "all":
                users = FreeUsersManager.get_all_free_users()
                target_desc = "جميع المستخدمين المجانيين"
            else:
                return {"success": False, "error": "نوع المستخدمين غير صحيح"}
            
            if not users:
                return {"success": False, "error": "لا توجد مستخدمين للإرسال إليهم"}
            
            # إنشاء أكواد البرومو
            codes = FreeUsersManager.create_bulk_trial_codes(
                count=len(users),
                days=7,
                note=f"كود تجربة مجانية - {target_desc}"
            )
            
            if len(codes) != len(users):
                return {"success": False, "error": "فشل في إنشاء العدد المطلوب من الأكواد"}
            
            # إرسال الأكواد
            sent_count = 0
            failed_count = 0
            results = []
            
            for i, user in enumerate(users):
                user_id = user['user_id']
                user_name = f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
                if not user_name:
                    user_name = f"المستخدم {user_id}"
                
                success = await FreeUsersManager.send_promo_code_to_user(
                    user_id=user_id,
                    promo_code=codes[i],
                    user_name=user_name
                )
                
                if success:
                    sent_count += 1
                    results.append({"user_id": user_id, "code": codes[i], "status": "sent"})
                else:
                    failed_count += 1
                    results.append({"user_id": user_id, "code": codes[i], "status": "failed"})
                
                # تأخير بسيط لتجنب حدود التليجرام
                import asyncio
                await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "total_users": len(users),
                "sent_count": sent_count,
                "failed_count": failed_count,
                "target_type": target_desc,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"خطأ في الإرسال المجمع: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def get_free_users_statistics() -> Dict:
        """إحصائيات المشتركين المجانيين"""
        try:
            all_free = FreeUsersManager.get_all_free_users()
            active_free = FreeUsersManager.get_active_free_users()
            
            # تصنيف المستخدمين
            never_used = [u for u in all_free if u['count'] == 0]
            light_users = [u for u in all_free if 1 <= u['count'] <= 2]
            regular_users = [u for u in all_free if 3 <= u['count'] <= 5]
            heavy_users = [u for u in all_free if u['count'] > 5]
            expired_users = [u for u in all_free if u['is_expired']]
            
            return {
                "total_free_users": len(all_free),
                "active_free_users": len(active_free),
                "never_used": len(never_used),
                "light_users": len(light_users),
                "regular_users": len(regular_users),
                "heavy_users": len(heavy_users),
                "expired_users": len(expired_users),
                "activity_rate": round((len(active_free) / len(all_free) * 100), 2) if all_free else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}

# دوال أوامر البوت للإدارة
async def list_free_users_command(message):
    """عرض قائمة المشتركين المجانيين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass
    
    try:
        stats = FreeUsersManager.get_free_users_statistics()
        
        if not stats:
            await message.reply("❌ خطأ في جلب الإحصائيات")
            return
        
        stats_message = f"""
📊 **إحصائيات المشتركين المجانيين**

👥 **إجمالي المستخدمين المجانيين:** {stats['total_free_users']}
🟢 **المستخدمين النشطين:** {stats['active_free_users']}
📈 **معدل النشاط:** {stats['activity_rate']}%

📋 **تصنيف حسب الاستخدام:**
• لم يستخدموا البوت: {stats['never_used']}
• استخدام خفيف (1-2): {stats['light_users']}
• استخدام منتظم (3-5): {stats['regular_users']}
• استخدام كثيف (+5): {stats['heavy_users']}
• اشتراكات منتهية: {stats['expired_users']}

🎯 **الأوامر المتاحة:**
• `/send_promo_active` - إرسال أكواد للنشطين
• `/send_promo_all` - إرسال أكواد للجميع
"""
        
        await message.reply(stats_message, parse_mode="Markdown")
        
    except Exception as e:
        logger.error(f"خطأ في أمر list_free_users: {e}")
        await message.reply("❌ حدث خطأ في جلب البيانات")

async def send_promo_to_active_command(message):
    """إرسال أكواد برومو للمستخدمين النشطين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass
    
    try:
        await message.reply("🔄 جاري إرسال أكواد البرومو للمستخدمين النشطين...")
        
        result = await FreeUsersManager.send_bulk_promo_codes("active")
        
        if result["success"]:
            success_message = f"""
✅ **تم إرسال أكواد البرومو بنجاح!**

📊 **النتائج:**
• المستهدفين: {result['target_type']}
• إجمالي المستخدمين: {result['total_users']}
• تم الإرسال بنجاح: {result['sent_count']}
• فشل الإرسال: {result['failed_count']}

🎉 معدل النجاح: {round(result['sent_count']/result['total_users']*100, 1)}%
"""
            await message.reply(success_message, parse_mode="Markdown")
        else:
            await message.reply(f"❌ فشل في الإرسال: {result['error']}")
            
    except Exception as e:
        logger.error(f"خطأ في أمر send_promo_active: {e}")
        await message.reply("❌ حدث خطأ في الإرسال")

async def send_promo_to_all_command(message):
    """إرسال أكواد برومو لجميع المستخدمين المجانيين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass
    
    try:
        await message.reply("🔄 جاري إرسال أكواد البرومو لجميع المستخدمين المجانيين...")
        
        result = await FreeUsersManager.send_bulk_promo_codes("all")
        
        if result["success"]:
            success_message = f"""
✅ **تم إرسال أكواد البرومو بنجاح!**

📊 **النتائج:**
• المستهدفين: {result['target_type']}
• إجمالي المستخدمين: {result['total_users']}
• تم الإرسال بنجاح: {result['sent_count']}
• فشل الإرسال: {result['failed_count']}

🎉 معدل النجاح: {round(result['sent_count']/result['total_users']*100, 1)}%
"""
            await message.reply(success_message, parse_mode="Markdown")
        else:
            await message.reply(f"❌ فشل في الإرسال: {result['error']}")
            
    except Exception as e:
        logger.error(f"خطأ في أمر send_promo_all: {e}")
        await message.reply("❌ حدث خطأ في الإرسال")
