#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشتركين المجانيين - محدث للبنية الحقيقية
Free Users Management System - Updated for Real Structure
"""

import logging
import datetime
from typing import List, Dict, Optional
from auth import open_google_sheet, bot
from user_limit import UserManager
from promo_codes import PromoCodeManager

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

class FreeUsersManager:
    """مدير المشتركين المجانيين - محدث للبنية الحقيقية"""
    
    @staticmethod
    def get_all_free_users() -> List[Dict]:
        """الحصول على جميع المشتركين المجانيين"""
        try:
            all_records = sheet2.get_all_records()
            free_users = []
            
            for record in all_records:
                try:
                    # استخراج البيانات حسب الموضع (بناءً على البنية الحقيقية)
                    values = list(record.values())
                    
                    if len(values) < 6:
                        continue
                    
                    # تحديد البيانات حسب الموضع
                    user_id = str(values[0]) if values[0] else ""
                    count = int(values[1]) if str(values[1]).isdigit() else 0
                    subscription_type = str(values[2]).lower() if values[2] else "free"
                    end_date_str = str(values[3]) if values[3] else ""
                    first_name = str(values[4]) if values[4] else ""
                    last_name = str(values[5]) if values[5] else ""
                    
                    # التحقق من صحة معرف المستخدم
                    if not user_id or len(user_id) < 8 or not user_id.isdigit():
                        continue
                    
                    # التحقق من انتهاء الاشتراك
                    is_expired = False
                    if subscription_type in ['trail', 'paid'] and end_date_str:
                        try:
                            end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                            is_expired = datetime.date.today() > end_date
                        except ValueError:
                            is_expired = True
                    
                    # إضافة المستخدمين المجانيين أو المنتهي اشتراكهم
                    if subscription_type == 'free' or is_expired:
                        free_users.append({
                            'user_id': user_id,
                            'subscription_type': subscription_type,
                            'end_date': end_date_str,
                            'first_name': first_name,
                            'last_name': last_name,
                            'count': count,
                            'is_expired': is_expired
                        })
                
                except Exception as e:
                    logger.warning(f"خطأ في معالجة سجل: {e}")
                    continue
            
            logger.info(f"تم العثور على {len(free_users)} مستخدم مجاني")
            return free_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين المجانيين: {e}")
            return []
    
    @staticmethod
    def get_active_free_users() -> List[Dict]:
        """الحصول على المشتركين المجانيين النشطين"""
        try:
            all_free_users = FreeUsersManager.get_all_free_users()
            active_users = []
            
            for user in all_free_users:
                # المستخدمين النشطين هم من لديهم استخدامات
                if user['count'] > 0:
                    active_users.append(user)
            
            logger.info(f"تم العثور على {len(active_users)} مستخدم مجاني نشط")
            return active_users
            
        except Exception as e:
            logger.error(f"خطأ في جلب المستخدمين النشطين: {e}")
            return []
    
    @staticmethod
    def create_bulk_trial_codes(count: int, days: int = 7, note: str = "كود تجربة مجانية للمشتركين") -> List[str]:
        """إنشاء مجموعة من أكواد التجربة المجانية مع تحكم في معدل الطلبات"""
        try:
            import time
            codes = []
            failed_count = 0
            max_retries = 3

            # تحديد التأخير بناءً على حدود Google Sheets API
            # الحد الأقصى: 60 طلب كتابة في الدقيقة = طلب كل ثانية
            base_delay = 1.2  # ثانية واحدة + هامش أمان

            logger.info(f"بدء إنشاء {count} كود مع تأخير {base_delay} ثانية بين كل كود")

            for i in range(count):
                code = None

                # تأخير لتجنب تجاوز حدود API (إلا للكود الأول)
                if i > 0:
                    time.sleep(base_delay)

                # محاولة إنشاء الكود مع إعادة المحاولة
                for retry in range(max_retries):
                    try:
                        code = PromoCodeManager.create_trial_code(
                            days=days,
                            expiry_days=30,
                            note=f"{note} - الدفعة {i+1}"
                        )
                        if code:
                            break
                        else:
                            logger.warning(f"فشل في إنشاء الكود {i+1}/{count} - المحاولة {retry+1}")
                    except Exception as e:
                        error_msg = str(e)
                        if "RATE_LIMIT_EXCEEDED" in error_msg or "Quota exceeded" in error_msg:
                            # إذا تجاوزنا الحد، انتظر أكثر
                            wait_time = base_delay * (retry + 2)
                            logger.warning(f"تجاوز حد API - انتظار {wait_time} ثانية قبل المحاولة {retry+1}")
                            time.sleep(wait_time)
                        else:
                            logger.warning(f"خطأ في إنشاء الكود {i+1}/{count} - المحاولة {retry+1}: {e}")
                            if retry < max_retries - 1:
                                time.sleep(0.5)  # تأخير قصير للأخطاء الأخرى

                if code:
                    codes.append(code)
                    logger.info(f"تم إنشاء الكود {i+1}/{count}: {code}")
                else:
                    failed_count += 1
                    logger.error(f"فشل نهائياً في إنشاء الكود {i+1}/{count}")

                # عرض التقدم كل 5 أكواد
                if (i + 1) % 5 == 0:
                    progress = ((i + 1) / count * 100)
                    logger.info(f"التقدم: {i+1}/{count} ({progress:.1f}%)")

            success_rate = (len(codes) / count * 100) if count > 0 else 0
            logger.info(f"تم إنشاء {len(codes)}/{count} كود تجربة مجانية (معدل النجاح: {success_rate:.1f}%)")

            if failed_count > 0:
                logger.warning(f"فشل في إنشاء {failed_count} كود من أصل {count}")

            return codes

        except Exception as e:
            logger.error(f"خطأ في إنشاء الأكواد المجمعة: {e}")
            return []
    
    @staticmethod
    async def send_promo_code_to_user(user_id: str, promo_code: str, user_name: str = "") -> bool:
        """إرسال كود البرومو لمستخدم واحد"""
        try:
            message = f"""
🎉 **مفاجأة خاصة لك!**

مرحباً {user_name}! 👋

🎁 **كود تجربة مجانية لمدة 7 أيام:**
`{promo_code}`

✨ **كيفية التفعيل:**
1️⃣ انسخ الكود أعلاه
2️⃣ اكتب الأمر: `/redeem {promo_code}`
3️⃣ استمتع بـ 7 أيام تجربة مجانية كاملة!

🚀 **مزايا التجربة المجانية:**
• تحليلات غير محدودة للأسهم
• مؤشرات فنية متقدمة
• تنبيهات فورية
• دعم فني مميز

⏰ **الكود صالح لمدة 30 يوماً**
💡 **لا تفوت هذه الفرصة!**
"""
            
            await bot.send_message(chat_id=user_id, text=message, parse_mode="Markdown")
            logger.info(f"تم إرسال كود البرومو {promo_code} للمستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الكود للمستخدم {user_id}: {e}")
            return False
    
    @staticmethod
    async def send_bulk_promo_codes(target_users: str = "active", batch_mode: bool = False) -> Dict:
        """إرسال أكواد البرومو بشكل مجمع مع تحكم في معدل الطلبات"""
        try:
            # تحديد المستخدمين المستهدفين
            if target_users == "active":
                users = FreeUsersManager.get_active_free_users()
                target_desc = "المستخدمين النشطين"
            elif target_users == "all":
                users = FreeUsersManager.get_all_free_users()
                target_desc = "جميع المستخدمين المجانيين"
            else:
                return {"success": False, "error": "نوع المستخدمين غير صحيح"}

            if not users:
                return {"success": False, "error": "لا توجد مستخدمين للإرسال إليهم"}

            # تحديد الحد الأقصى للدفعة الواحدة لتجنب تجاوز حدود API
            max_batch_size = 50  # حد أقصى 50 كود في الدفعة الواحدة

            if len(users) > max_batch_size and not batch_mode:
                logger.warning(f"عدد المستخدمين ({len(users)}) يتجاوز الحد الأقصى للدفعة ({max_batch_size})")
                logger.info(f"سيتم الإرسال لأول {max_batch_size} مستخدم فقط")
                logger.info(f"استخدم /send_promo_all_batches للإرسال للجميع على دفعات")
                users = users[:max_batch_size]
            elif len(users) > max_batch_size and batch_mode:
                logger.info(f"وضع الدفعات مفعل - سيتم الإرسال لـ {len(users)} مستخدم على دفعات")

            logger.info(f"بدء إنشاء {len(users)} كود لـ {target_desc}")

            # إنشاء أكواد البرومو
            codes = FreeUsersManager.create_bulk_trial_codes(
                count=len(users),
                days=7,
                note=f"كود تجربة مجانية - {target_desc}"
            )

            if not codes:
                return {"success": False, "error": "فشل في إنشاء أي أكواد برومو"}

            if len(codes) < len(users):
                logger.warning(f"تم إنشاء {len(codes)} كود فقط من أصل {len(users)} مطلوب")
                # تقليل عدد المستخدمين ليتطابق مع عدد الأكواد المتاحة
                users = users[:len(codes)]
                logger.info(f"سيتم الإرسال لـ {len(users)} مستخدم فقط")
            
            # إرسال الأكواد
            sent_count = 0
            failed_count = 0
            results = []
            
            for i, user in enumerate(users):
                user_id = user['user_id']
                user_name = f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
                if not user_name:
                    user_name = f"المستخدم {user_id}"
                
                success = await FreeUsersManager.send_promo_code_to_user(
                    user_id=user_id,
                    promo_code=codes[i],
                    user_name=user_name
                )
                
                if success:
                    sent_count += 1
                    results.append({"user_id": user_id, "code": codes[i], "status": "sent"})
                else:
                    failed_count += 1
                    results.append({"user_id": user_id, "code": codes[i], "status": "failed"})
                
                # تأخير بسيط لتجنب حدود التليجرام
                import asyncio
                await asyncio.sleep(0.5)
            
            return {
                "success": True,
                "total_users": len(users),
                "sent_count": sent_count,
                "failed_count": failed_count,
                "target_type": target_desc,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"خطأ في الإرسال المجمع: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    async def send_bulk_promo_codes_in_batches(target_users: str = "active") -> Dict:
        """إرسال أكواد البرومو للجميع على دفعات متعددة"""
        try:
            import asyncio

            # تحديد المستخدمين المستهدفين
            if target_users == "active":
                all_users = FreeUsersManager.get_active_free_users()
                target_desc = "المستخدمين النشطين"
            elif target_users == "all":
                all_users = FreeUsersManager.get_all_free_users()
                target_desc = "جميع المستخدمين المجانيين"
            else:
                return {"success": False, "error": "نوع المستخدمين غير صحيح"}

            if not all_users:
                return {"success": False, "error": "لا توجد مستخدمين للإرسال إليهم"}

            batch_size = 50
            total_users = len(all_users)
            total_batches = (total_users + batch_size - 1) // batch_size  # تقريب لأعلى

            logger.info(f"بدء الإرسال بالدفعات: {total_users} مستخدم على {total_batches} دفعة")

            # متغيرات التتبع الإجمالية
            total_sent = 0
            total_failed = 0
            all_results = []

            # معالجة كل دفعة
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, total_users)
                batch_users = all_users[start_idx:end_idx]

                logger.info(f"معالجة الدفعة {batch_num + 1}/{total_batches}: {len(batch_users)} مستخدم")

                # إرسال الدفعة الحالية
                batch_result = await FreeUsersManager.send_bulk_promo_codes(
                    target_users=target_users,
                    batch_mode=True
                )

                if batch_result["success"]:
                    total_sent += batch_result["sent_count"]
                    total_failed += batch_result["failed_count"]
                    all_results.extend(batch_result.get("results", []))

                    logger.info(f"الدفعة {batch_num + 1} مكتملة: {batch_result['sent_count']} نجح، {batch_result['failed_count']} فشل")
                else:
                    logger.error(f"فشل في الدفعة {batch_num + 1}: {batch_result['error']}")
                    total_failed += len(batch_users)

                # تأخير بين الدفعات لتجنب تجاوز الحدود
                if batch_num < total_batches - 1:  # ليس الدفعة الأخيرة
                    wait_time = 30  # 30 ثانية بين الدفعات
                    logger.info(f"انتظار {wait_time} ثانية قبل الدفعة التالية...")
                    await asyncio.sleep(wait_time)

            # النتيجة الإجمالية
            success_rate = (total_sent / total_users * 100) if total_users > 0 else 0

            return {
                "success": True,
                "total_users": total_users,
                "sent_count": total_sent,
                "failed_count": total_failed,
                "target_type": target_desc,
                "batches_processed": total_batches,
                "success_rate": round(success_rate, 1),
                "results": all_results
            }

        except Exception as e:
            logger.error(f"خطأ في الإرسال بالدفعات: {e}")
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def get_free_users_statistics() -> Dict:
        """إحصائيات المشتركين المجانيين"""
        try:
            all_free = FreeUsersManager.get_all_free_users()
            active_free = FreeUsersManager.get_active_free_users()
            
            # تصنيف المستخدمين
            never_used = [u for u in all_free if u['count'] == 0]
            light_users = [u for u in all_free if 1 <= u['count'] <= 2]
            regular_users = [u for u in all_free if 3 <= u['count'] <= 5]
            heavy_users = [u for u in all_free if u['count'] > 5]
            expired_users = [u for u in all_free if u['is_expired']]
            
            return {
                "total_free_users": len(all_free),
                "active_free_users": len(active_free),
                "never_used": len(never_used),
                "light_users": len(light_users),
                "regular_users": len(regular_users),
                "heavy_users": len(heavy_users),
                "expired_users": len(expired_users),
                "activity_rate": round((len(active_free) / len(all_free) * 100), 2) if all_free else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب الإحصائيات: {e}")
            return {}

# دوال أوامر البوت للإدارة
async def list_free_users_command(message):
    """عرض قائمة المشتركين المجانيين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass

    try:
        stats = FreeUsersManager.get_free_users_statistics()

        if not stats:
            await message.reply("❌ خطأ في جلب الإحصائيات")
            return

        stats_message = f"""
📊 **إحصائيات المشتركين المجانيين**

👥 **إجمالي المستخدمين المجانيين:** {stats['total_free_users']:,}
🟢 **المستخدمين النشطين:** {stats['active_free_users']:,}
📈 **معدل النشاط:** {stats['activity_rate']}%

📋 **تصنيف حسب الاستخدام:**
• لم يستخدموا البوت: {stats['never_used']:,}
• استخدام خفيف (1-2): {stats['light_users']:,}
• استخدام منتظم (3-5): {stats['regular_users']:,}
• استخدام كثيف (+5): {stats['heavy_users']:,}
• اشتراكات منتهية: {stats['expired_users']:,}

🎯 **الأوامر المتاحة:**
• `/send_promo_active` - إرسال أكواد للنشطين ({stats['active_free_users']:,} مستخدم)
• `/send_promo_all` - إرسال أكواد للجميع ({stats['total_free_users']:,} مستخدم)
"""

        await message.reply(stats_message, parse_mode="Markdown")

    except Exception as e:
        logger.error(f"خطأ في أمر list_free_users: {e}")
        await message.reply("❌ حدث خطأ في جلب البيانات")

async def send_promo_to_active_command(message):
    """إرسال أكواد برومو للمستخدمين النشطين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass

    try:
        # التحقق من عدد المستخدمين النشطين
        active_users = FreeUsersManager.get_active_free_users()

        if not active_users:
            await message.reply("❌ لا توجد مستخدمين نشطين للإرسال إليهم")
            return

        await message.reply(f"🔄 جاري إرسال أكواد البرومو لـ {len(active_users):,} مستخدم نشط...")

        result = await FreeUsersManager.send_bulk_promo_codes("active")

        if result["success"]:
            success_message = f"""
✅ **تم إرسال أكواد البرومو بنجاح!**

📊 **النتائج:**
• المستهدفين: {result['target_type']}
• إجمالي المستخدمين: {result['total_users']:,}
• تم الإرسال بنجاح: {result['sent_count']:,}
• فشل الإرسال: {result['failed_count']:,}

🎉 معدل النجاح: {round(result['sent_count']/result['total_users']*100, 1)}%

⏰ تم إنشاء {result['total_users']} كود تجربة مجانية لمدة 7 أيام
💡 الأكواد صالحة لمدة 30 يوماً
"""
            await message.reply(success_message, parse_mode="Markdown")
        else:
            await message.reply(f"❌ فشل في الإرسال: {result['error']}")

    except Exception as e:
        logger.error(f"خطأ في أمر send_promo_active: {e}")
        await message.reply("❌ حدث خطأ في الإرسال")

async def send_promo_to_all_command(message):
    """إرسال أكواد برومو لجميع المستخدمين المجانيين - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass

    try:
        # التحقق من عدد المستخدمين المجانيين
        all_users = FreeUsersManager.get_all_free_users()

        if not all_users:
            await message.reply("❌ لا توجد مستخدمين مجانيين للإرسال إليهم")
            return

        # تحذير للأعداد الكبيرة
        if len(all_users) > 100:
            await message.reply(f"⚠️ سيتم إرسال أكواد لـ {len(all_users):,} مستخدم. هذا قد يستغرق وقتاً طويلاً...")

        await message.reply(f"🔄 جاري إرسال أكواد البرومو لـ {len(all_users):,} مستخدم مجاني...")

        result = await FreeUsersManager.send_bulk_promo_codes("all")

        if result["success"]:
            success_message = f"""
✅ **تم إرسال أكواد البرومو بنجاح!**

📊 **النتائج:**
• المستهدفين: {result['target_type']}
• إجمالي المستخدمين: {result['total_users']:,}
• تم الإرسال بنجاح: {result['sent_count']:,}
• فشل الإرسال: {result['failed_count']:,}

🎉 معدل النجاح: {round(result['sent_count']/result['total_users']*100, 1)}%

⏰ تم إنشاء {result['total_users']} كود تجربة مجانية لمدة 7 أيام
💡 الأكواد صالحة لمدة 30 يوماً

🚀 **توقع زيادة كبيرة في معدلات التحويل!**
"""
            await message.reply(success_message, parse_mode="Markdown")
        else:
            await message.reply(f"❌ فشل في الإرسال: {result['error']}")

    except Exception as e:
        logger.error(f"خطأ في أمر send_promo_all: {e}")
        await message.reply("❌ حدث خطأ في الإرسال")

async def send_promo_to_all_batches_command(message):
    """إرسال أكواد برومو لجميع المستخدمين المجانيين بالدفعات - للإدارة فقط"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            await message.reply("❌ هذا الأمر متاح للإدارة فقط")
            return
    except ImportError:
        pass

    try:
        # التحقق من عدد المستخدمين المجانيين
        all_users = FreeUsersManager.get_all_free_users()

        if not all_users:
            await message.reply("❌ لا توجد مستخدمين مجانيين للإرسال إليهم")
            return

        total_batches = (len(all_users) + 49) // 50  # تقريب لأعلى
        estimated_time = total_batches * 1.5  # تقدير الوقت بالدقائق

        # رسالة تأكيد
        confirmation_message = f"""
⚠️ **تأكيد الإرسال بالدفعات**

📊 **التفاصيل:**
• إجمالي المستخدمين: {len(all_users):,}
• عدد الدفعات: {total_batches}
• الوقت المتوقع: ~{estimated_time:.0f} دقيقة

🔄 **العملية:**
• 50 مستخدم في كل دفعة
• 30 ثانية انتظار بين الدفعات
• إرسال آمن ومحمي من تجاوز الحدود

⏰ **هذا سيستغرق وقتاً طويلاً!**

💡 **للمتابعة، أرسل:** `نعم أريد الإرسال للجميع`
❌ **للإلغاء، تجاهل هذه الرسالة**
"""

        await message.reply(confirmation_message, parse_mode="Markdown")

    except Exception as e:
        logger.error(f"خطأ في أمر send_promo_all_batches: {e}")
        await message.reply("❌ حدث خطأ في الإعداد")

async def confirm_send_all_batches_command(message):
    """تأكيد الإرسال بالدفعات"""
    try:
        from config import ADMIN_IDS
        if message.from_user.id not in ADMIN_IDS:
            return
    except ImportError:
        pass

    if message.text and "نعم أريد الإرسال للجميع" in message.text:
        try:
            all_users = FreeUsersManager.get_all_free_users()

            await message.reply(f"🚀 **بدء الإرسال بالدفعات لـ {len(all_users):,} مستخدم...**\n\n⏳ هذا سيستغرق وقتاً طويلاً، يرجى الانتظار...")

            result = await FreeUsersManager.send_bulk_promo_codes_in_batches("all")

            if result["success"]:
                success_message = f"""
🎉 **تم إكمال الإرسال بالدفعات بنجاح!**

📊 **النتائج الإجمالية:**
• المستهدفين: {result['target_type']}
• إجمالي المستخدمين: {result['total_users']:,}
• تم الإرسال بنجاح: {result['sent_count']:,}
• فشل الإرسال: {result['failed_count']:,}
• عدد الدفعات المعالجة: {result['batches_processed']}

🎯 **معدل النجاح الإجمالي: {result['success_rate']}%**

⏰ تم إنشاء {result['sent_count']:,} كود تجربة مجانية لمدة 7 أيام
💡 الأكواد صالحة لمدة 30 يوماً

🚀 **توقع زيادة كبيرة في معدلات التحويل!**
📈 **تم الوصول لجميع المستخدمين المجانيين!**
"""
                await message.reply(success_message, parse_mode="Markdown")
            else:
                await message.reply(f"❌ فشل في الإرسال بالدفعات: {result['error']}")

        except Exception as e:
            logger.error(f"خطأ في تأكيد الإرسال بالدفعات: {e}")
            await message.reply("❌ حدث خطأ أثناء الإرسال")
