#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول مستخدمين بالبنية الصحيحة
Create users sheet with correct structure
"""

import datetime

def create_users_sheet():
    """إنشاء جدول مستخدمين جديد"""
    try:
        from auth import open_google_sheet
        
        print("🔧 إنشاء جدول مستخدمين جديد...")
        
        # الاتصال بـ Google Sheets
        sheet_name = "stock"
        sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
        
        # محاولة إنشاء ورقة جديدة
        try:
            spreadsheet = sheet2.spreadsheet
            new_sheet = spreadsheet.add_worksheet(title="users_data", rows="1000", cols="10")
            print("✅ تم إنشاء ورقة جديدة: users_data")
        except Exception as e:
            # إذا كانت الورقة موجودة، استخدمها
            try:
                new_sheet = spreadsheet.worksheet("users_data")
                print("✅ تم العثور على ورقة users_data موجودة")
                new_sheet.clear()  # مسح المحتوى الحالي
            except:
                print(f"❌ فشل في إنشاء أو العثور على ورقة users_data: {e}")
                return False
        
        # إضافة الرؤوس الصحيحة
        headers = [
            'user_id', 'first_name', 'last_name', 'subscription_type', 
            'count', 'end_date', 'created_date', 'last_activity'
        ]
        
        new_sheet.insert_row(headers, 1)
        print(f"✅ تم إضافة الرؤوس: {headers}")
        
        # إضافة مستخدمين تجريبيين
        test_users = [
            [
                '111111111',  # user_id
                'أحمد',       # first_name
                'محمد',       # last_name
                'free',       # subscription_type
                3,            # count
                '',           # end_date
                datetime.date.today().strftime('%Y-%m-%d'),  # created_date
                datetime.date.today().strftime('%Y-%m-%d')   # last_activity
            ],
            [
                '222222222',
                'فاطمة',
                'علي',
                'free',
                1,
                '',
                datetime.date.today().strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ],
            [
                '333333333',
                'محمد',
                'حسن',
                'free',
                5,
                '',
                datetime.date.today().strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ],
            [
                '444444444',
                'مريم',
                'أحمد',
                'trail',
                2,
                (datetime.date.today() - datetime.timedelta(days=5)).strftime('%Y-%m-%d'),  # منتهي
                (datetime.date.today() - datetime.timedelta(days=10)).strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ],
            [
                '555555555',
                'خالد',
                'سالم',
                'free',
                0,
                '',
                datetime.date.today().strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ],
            [
                '666666666',
                'نور',
                'حسام',
                'free',
                7,
                '',
                datetime.date.today().strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ],
            [
                '777777777',
                'سارة',
                'عبدالله',
                'free',
                2,
                '',
                datetime.date.today().strftime('%Y-%m-%d'),
                datetime.date.today().strftime('%Y-%m-%d')
            ]
        ]
        
        # إضافة المستخدمين التجريبيين
        for user in test_users:
            new_sheet.append_row(user)
        
        print(f"✅ تم إضافة {len(test_users)} مستخدم تجريبي")
        
        # اختبار قراءة البيانات
        print("\n🧪 اختبار قراءة البيانات...")
        test_records = new_sheet.get_all_records()
        
        free_count = 0
        active_count = 0
        
        for record in test_records:
            subscription_type = record.get('subscription_type', 'free')
            user_id = str(record.get('user_id', ''))
            count = int(record.get('count', 0))
            
            if user_id and len(user_id) >= 8:
                if subscription_type == 'free':
                    free_count += 1
                    if count > 0:
                        active_count += 1
        
        print(f"📊 النتائج:")
        print(f"   - إجمالي المستخدمين المجانيين: {free_count}")
        print(f"   - المستخدمين النشطين: {active_count}")
        
        if free_count > 0:
            print("🎉 تم إنشاء جدول المستخدمين بنجاح!")
            return True
        else:
            print("❌ لم يتم العثور على مستخدمين مجانيين")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول المستخدمين: {e}")
        return False

def test_updated_system():
    """اختبار النظام المحدث"""
    try:
        print("\n🧪 اختبار النظام المحدث...")
        
        from free_users_manager import FreeUsersManager
        
        # اختبار جلب المستخدمين المجانيين
        all_free = FreeUsersManager.get_all_free_users()
        print(f"🆓 إجمالي المستخدمين المجانيين: {len(all_free)}")
        
        if all_free:
            print("📝 عينة من المستخدمين المجانيين:")
            for i, user in enumerate(all_free[:3], 1):
                print(f"   {i}. {user['first_name']} {user['last_name']} ({user['user_id']}) - استخدامات: {user['count']}")
        
        # اختبار جلب المستخدمين النشطين
        active_free = FreeUsersManager.get_active_free_users()
        print(f"🟢 المستخدمين النشطين: {len(active_free)}")
        
        # اختبار الإحصائيات
        stats = FreeUsersManager.get_free_users_statistics()
        if stats:
            print(f"\n📊 إحصائيات مفصلة:")
            print(f"   - إجمالي المجانيين: {stats['total_free_users']}")
            print(f"   - النشطين: {stats['active_free_users']}")
            print(f"   - معدل النشاط: {stats['activity_rate']}%")
            print(f"   - لم يستخدموا البوت: {stats['never_used']}")
            print(f"   - استخدام خفيف: {stats['light_users']}")
            print(f"   - استخدام منتظم: {stats['regular_users']}")
            print(f"   - استخدام كثيف: {stats['heavy_users']}")
        
        return len(all_free), len(active_free)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return 0, 0

def main():
    """الدالة الرئيسية"""
    print("🔧 إنشاء جدول مستخدمين بالبنية الصحيحة")
    print("=" * 60)
    
    # 1. إنشاء جدول المستخدمين
    success = create_users_sheet()
    
    if not success:
        print("❌ فشل في إنشاء جدول المستخدمين")
        return
    
    # 2. اختبار النظام المحدث
    free_count, active_count = test_updated_system()
    
    # 3. النتيجة النهائية
    print("\n" + "=" * 60)
    print("📊 النتيجة النهائية:")
    
    if free_count > 0:
        print(f"✅ تم العثور على {free_count} مستخدم مجاني")
        print(f"✅ منهم {active_count} مستخدم نشط")
        print("🎉 النظام جاهز لإرسال أكواد البرومو!")
        
        print("\n🚀 يمكنك الآن استخدام:")
        print("   - /list_free_users - لعرض الإحصائيات")
        print("   - /send_promo_active - لإرسال أكواد للنشطين")
        print("   - /send_promo_all - لإرسال أكواد للجميع")
        
        print("\n💡 ملاحظة:")
        print("   - تم إنشاء جدول 'users_data' جديد")
        print("   - تم تحديث free_users_manager.py لاستخدام الجدول الجديد")
        print("   - الأوامر ستعمل الآن بدون مشاكل")
    else:
        print("❌ لا يزال لا توجد مستخدمين مجانيين")
        print("💡 تحقق من:")
        print("   - اتصال Google Sheets")
        print("   - صحة بيانات المستخدمين")
        print("   - أذونات الوصول للجدول")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
