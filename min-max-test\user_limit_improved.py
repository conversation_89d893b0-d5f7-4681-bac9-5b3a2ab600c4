import datetime
import os
import logging
from typing import <PERSON><PERSON>, Optional
from aiogram.types import Message
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from auth import open_google_sheet, dp, bot

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

# Constants
DAILY_FREE_LIMIT = 3
REFERRAL_BONUS_DAYS = 3
TRIAL_PERIOD_DAYS = 2

class UserManager:
    @staticmethod
    def get_user_data(user_id: str) -> Optional[dict]:
        """Get user data from Google Sheets with error handling"""
        try:
            user_data = sheet2.find(str(user_id))
            if not user_data:
                return None
            
            row = user_data.row
            col = user_data.col
            row_values = sheet2.row_values(row)
            
            # Expected format: [user_id, count, subscription_type, end_date, first_name, last_name]
            return {
                'row': row,
                'col': col,
                'user_id': row_values[col - 1],
                'count': int(row_values[col]),
                'subscription_type': row_values[col + 1],
                'end_date': row_values[col + 2],
                'first_name': row_values[col + 3] if len(row_values) > col + 3 else '',
                'last_name': row_values[col + 4] if len(row_values) > col + 4 else ''
            }
        except Exception as e:
            logger.error(f"Error getting user data for {user_id}: {e}")
            return None
    
    @staticmethod
    def update_user_data(row: int, col: int, count: int = None, subscription_type: str = None, end_date: str = None):
        """Update user data in Google Sheets with error handling"""
        try:
            if count is not None:
                sheet2.update_cell(row, col + 1, count)
            if subscription_type is not None:
                sheet2.update_cell(row, col + 2, subscription_type)
            if end_date is not None:
                sheet2.update_cell(row, col + 3, end_date)
        except Exception as e:
            logger.error(f"Error updating user data: {e}")
    
    @staticmethod
    def add_new_user(user_id: str, user_fname: str = '', user_lname: str = '') -> str:
        """Add new user to Google Sheets"""
        try:
            end_date = (datetime.date.today() + datetime.timedelta(days=TRIAL_PERIOD_DAYS)).strftime("%Y-%m-%d")
            user_info = [user_id, 1, "trial", end_date, user_fname or '', user_lname or '']
            sheet2.append_row(user_info)
            return end_date
        except Exception as e:
            logger.error(f"Error adding new user {user_id}: {e}")
            return None
    
    @staticmethod
    def is_new_day(last_update_date: str) -> bool:
        """Check if it's a new day since last update"""
        try:
            last_date = datetime.datetime.strptime(last_update_date, "%Y-%m-%d").date()
            return datetime.date.today() > last_date
        except:
            return True

@dp.message_handler(commands=['myrefer'])
async def display_refer_link(message: Message):
    """Display referral link to user"""
    user_id = message.from_user.id
    refer_link = f'https://t.me/egx_stock_analyzer_bot?start={user_id}'

    # Bilingual message
    en_message = (
        "🔗 **Your Personal Referral Link**\n\n"
        "Invite your friends to join the automated analyst bot for the Egyptian stock market "
        f"and get **{REFERRAL_BONUS_DAYS} days** of paid membership for every new member who uses your link!\n\n"
        "You can share this link on social media, WhatsApp, Telegram, or any platform you prefer.\n\n"
        "رابط الإحالة الشخصي الخاص بك 🔗\n\n"
        f"قم بدعوة أصدقائك للانضمام إلى بوت المحلل الآلي لأسهم البورصة المصرية واحصل على **{REFERRAL_BONUS_DAYS} أيام** "
        "عضوية مدفوعة عن كل عضو جديد يستخدم الرابط الخاص بك!\n\n"
        "يمكنك مشاركة هذا الرابط على مواقع التواصل الاجتماعي والواتساب والتليجرام.\n\n"
        f"🔗 **{refer_link}**"
    )
    
    try:
        await bot.send_message(chat_id=user_id, text=en_message, parse_mode='Markdown')
    except Exception as e:
        logger.error(f"Error sending referral link to {user_id}: {e}")

async def handle_referral(referrer_id: str, new_user_id: str) -> bool:
    """Handle referral logic when new user joins"""
    try:
        if not referrer_id or str(referrer_id) == str(new_user_id):
            return False
        
        referrer_data = UserManager.get_user_data(referrer_id)
        if not referrer_data:
            return False
        
        # Calculate new end date for referrer
        if referrer_data['subscription_type'] == 'free':
            # Convert free user to trial
            new_end_date = (datetime.date.today() + datetime.timedelta(days=REFERRAL_BONUS_DAYS)).strftime("%Y-%m-%d")
            UserManager.update_user_data(
                referrer_data['row'], 
                referrer_data['col'], 
                subscription_type='trial',
                end_date=new_end_date
            )
        else:
            # Extend existing subscription
            current_end_date = datetime.datetime.strptime(referrer_data['end_date'], "%Y-%m-%d").date()
            # If subscription is expired, start from today
            if current_end_date < datetime.date.today():
                current_end_date = datetime.date.today()
            
            new_end_date = (current_end_date + datetime.timedelta(days=REFERRAL_BONUS_DAYS)).strftime("%Y-%m-%d")
            UserManager.update_user_data(
                referrer_data['row'], 
                referrer_data['col'], 
                end_date=new_end_date
            )
        
        # Notify referrer
        await bot.send_message(
            chat_id=referrer_id, 
            text=f'🎉 تهانينا! لقد قمت بإحالة مستخدم جديد!\n'
                 f'تم تمديد اشتراكك إلى {new_end_date}\n\n'
                 f'🎉 Congratulations! You referred a new user!\n'
                 f'Your subscription has been extended to {new_end_date}'
        )
        return True
        
    except Exception as e:
        logger.error(f"Error handling referral from {referrer_id} to {new_user_id}: {e}")
        return False

@dp.message_handler(commands=['start'])
async def check_user_limit(message: Message) -> Tuple[str, int]:
    """Check user subscription status and limits"""
    user_id = message.from_user.id
    user_fname = message.from_user.first_name or ''
    user_lname = message.from_user.last_name or ''
    referrer_id = message.get_args()

    try:
        # Handle referral logic for new users
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data and referrer_id:
            await handle_referral(referrer_id, str(user_id))

        # Check user subscription status
        if not user_data:
            # New user - add to sheet
            end_date = UserManager.add_new_user(str(user_id), user_fname, user_lname)
            if end_date:
                welcome_msg = (
                    f"🎉 أهلاً بك في خدمتنا!\n"
                    f"لديك حساب تجريبي بجميع المميزات المدفوعة\n"
                    f"ستنتهي فترتك التجريبية في {end_date}\n\n"
                    f"🎉 Welcome to our service!\n"
                    f"You have a trial account with full paid features\n"
                    f"Your trial period will end on {end_date}"
                )
                await bot.send_message(chat_id=user_id, text=welcome_msg)
                return "trial", 1
            else:
                await bot.send_message(chat_id=user_id, text="حدث خطأ في النظام، يرجى المحاولة مرة أخرى\nSystem error, please try again")
                return "free", 0
        
        # Existing user - check status
        subscription_type = user_data['subscription_type']
        count = user_data['count']
        
        try:
            end_date = datetime.datetime.strptime(user_data['end_date'], "%Y-%m-%d").date()
        except:
            end_date = datetime.date.today()
        
        # Check if subscription expired
        today = datetime.date.today()
        if subscription_type in ["trial", "paid"] and today > end_date:
            # Subscription expired, convert to free
            UserManager.update_user_data(
                user_data['row'], 
                user_data['col'], 
                count=0,
                subscription_type="free",
                end_date=today.strftime("%Y-%m-%d")
            )
            
            expiry_msg = (
                f"انتهت فترة اشتراكك في {end_date}\n"
                f"يرجى تجديد الاشتراك للاستمرار في الخدمة\n\n"
                f"Your subscription ended on {end_date}\n"
                f"Please renew your subscription to continue using the service"
            )
            await bot.send_message(chat_id=user_id, text=expiry_msg)
            return "free", 0
        
        # Reset daily count if it's a new day
        if subscription_type == "free" and UserManager.is_new_day(user_data['end_date']):
            UserManager.update_user_data(
                user_data['row'], 
                user_data['col'], 
                count=1,
                end_date=today.strftime("%Y-%m-%d")
            )
            remaining = DAILY_FREE_LIMIT - 1
            await bot.send_message(
                chat_id=user_id, 
                text=f"يوم جديد! لديك {remaining} تقارير متبقية اليوم\nNew day! You have {remaining} reports remaining today"
            )
            return "free", 1
        
        # Check free user daily limit
        if subscription_type == "free" and count >= DAILY_FREE_LIMIT:
            limit_msg = (
                "عفواً، لقد نفدت التقارير المجانية اليومية\n"
                "للحصول على تقارير إضافية قم بالاشتراك أو انتظر حتى الغد\n\n"
                "Sorry, you have exceeded the daily free reports limit\n"
                "Subscribe for unlimited access or wait until tomorrow"
            )
            await bot.send_message(chat_id=user_id, text=limit_msg)
            return "free", count
        
        # Update usage count
        new_count = count + 1
        UserManager.update_user_data(user_data['row'], user_data['col'], count=new_count)
        
        # Send remaining quota message for free users
        if subscription_type == "free":
            remaining = DAILY_FREE_LIMIT - new_count
            if remaining > 0:
                await bot.send_message(
                    chat_id=user_id, 
                    text=f"لديك {remaining} تقارير متبقية اليوم\nYou have {remaining} reports remaining today"
                )
        
        return subscription_type, new_count
        
    except Exception as e:
        logger.error(f"Error in check_user_limit for user {user_id}: {e}")
        await bot.send_message(chat_id=user_id, text="حدث خطأ في النظام، يرجى المحاولة مرة أخرى\nSystem error, please try again")
        return "free", 0

# Helper function to check user status without updating count
async def get_user_status(user_id: str) -> Tuple[str, int]:
    """Get user status without updating usage count"""
    try:
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data:
            return "free", 0
        
        subscription_type = user_data['subscription_type']
        count = user_data['count']
        
        try:
            end_date = datetime.datetime.strptime(user_data['end_date'], "%Y-%m-%d").date()
        except:
            end_date = datetime.date.today()
        
        # Check if subscription expired
        if subscription_type in ["trial", "paid"] and datetime.date.today() > end_date:
            return "free", count
        
        return subscription_type, count
        
    except Exception as e:
        logger.error(f"Error getting user status for {user_id}: {e}")
        return "free", 0
