#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الدفعات الجديد
Test new batch system
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock

async def test_batch_system():
    """اختبار نظام الدفعات الجديد"""
    try:
        print("🧪 اختبار نظام الدفعات الجديد...")
        
        from free_users_manager import FreeUsersManager
        
        # جلب جميع المستخدمين المجانيين
        all_users = FreeUsersManager.get_all_free_users()
        print(f"👥 إجمالي المستخدمين المجانيين: {len(all_users):,}")
        
        if len(all_users) == 0:
            print("⚠️ لا توجد مستخدمين للاختبار")
            return False
        
        # حساب عدد الدفعات المتوقع
        batch_size = 50
        expected_batches = (len(all_users) + batch_size - 1) // batch_size
        print(f"📊 عدد الدفعات المتوقع: {expected_batches}")
        print(f"⏰ الوقت المتوقع: ~{expected_batches * 1.5:.0f} دقيقة")
        
        # محاكاة النظام
        original_send = FreeUsersManager.send_promo_code_to_user
        original_create = FreeUsersManager.create_bulk_trial_codes
        
        async def mock_send(user_id, promo_code, user_name=""):
            return True  # محاكاة نجاح
        
        def mock_create(count, days=7, note=""):
            # محاكاة إنشاء أكواد
            return [f"TRIAL{i:06d}" for i in range(count)]
        
        FreeUsersManager.send_promo_code_to_user = mock_send
        FreeUsersManager.create_bulk_trial_codes = mock_create
        
        try:
            print(f"\n🚀 بدء اختبار الإرسال بالدفعات...")
            
            # محاكاة الإرسال بالدفعات (مع عدد محدود للاختبار)
            test_users = all_users[:150]  # اختبار مع 150 مستخدم (3 دفعات)
            
            # تعديل مؤقت للاختبار
            original_get_all = FreeUsersManager.get_all_free_users
            FreeUsersManager.get_all_free_users = lambda: test_users
            
            result = await FreeUsersManager.send_bulk_promo_codes_in_batches("all")
            
            # إعادة الدالة الأصلية
            FreeUsersManager.get_all_free_users = original_get_all
            
            if result["success"]:
                print(f"\n✅ نجح الإرسال بالدفعات:")
                print(f"   📊 المستهدفين: {result['target_type']}")
                print(f"   👥 إجمالي المستخدمين: {result['total_users']}")
                print(f"   ✅ تم الإرسال بنجاح: {result['sent_count']}")
                print(f"   ❌ فشل الإرسال: {result['failed_count']}")
                print(f"   📦 عدد الدفعات المعالجة: {result['batches_processed']}")
                print(f"   📈 معدل النجاح: {result['success_rate']}%")
                
                # التحقق من النتائج
                expected_batches_test = (len(test_users) + 49) // 50
                if result['batches_processed'] == expected_batches_test:
                    print("✅ عدد الدفعات صحيح")
                    return True
                else:
                    print(f"❌ عدد الدفعات غير صحيح: متوقع {expected_batches_test}, فعلي {result['batches_processed']}")
                    return False
            else:
                print(f"❌ فشل في الإرسال بالدفعات: {result['error']}")
                return False
                
        finally:
            FreeUsersManager.send_promo_code_to_user = original_send
            FreeUsersManager.create_bulk_trial_codes = original_create
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الدفعات: {e}")
        return False

async def test_batch_command():
    """اختبار أمر الإرسال بالدفعات"""
    try:
        print("\n🤖 اختبار أمر الإرسال بالدفعات...")
        
        from free_users_manager import send_promo_to_all_batches_command, confirm_send_all_batches_command
        
        # محاكاة رسالة من المدير
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 868182073  # معرف المدير
        
        # اختبار أمر البدء
        await send_promo_to_all_batches_command(mock_message)
        
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            
            if "تأكيد الإرسال بالدفعات" in sent_message:
                print("✅ رسالة التأكيد صحيحة")
                
                # اختبار رسالة التأكيد
                mock_confirm = MagicMock()
                mock_confirm.reply = AsyncMock()
                mock_confirm.from_user.id = 868182073
                mock_confirm.text = "نعم أريد الإرسال للجميع"
                
                # محاكاة النظام
                original_batch_send = FreeUsersManager.send_bulk_promo_codes_in_batches
                
                async def mock_batch_send(target_users):
                    return {
                        "success": True,
                        "total_users": 1218,
                        "sent_count": 1150,
                        "failed_count": 68,
                        "target_type": "جميع المستخدمين المجانيين",
                        "batches_processed": 25,
                        "success_rate": 94.4
                    }
                
                FreeUsersManager.send_bulk_promo_codes_in_batches = mock_batch_send
                
                try:
                    await confirm_send_all_batches_command(mock_confirm)
                    
                    if mock_confirm.reply.called:
                        confirm_call = mock_confirm.reply.call_args
                        confirm_message = confirm_call[0][0]
                        
                        if "تم إكمال الإرسال بالدفعات بنجاح" in confirm_message:
                            print("✅ رسالة إكمال الإرسال صحيحة")
                            return True
                        else:
                            print(f"❌ رسالة إكمال غير متوقعة: {confirm_message[:200]}...")
                            return False
                    else:
                        print("❌ لم يتم إرسال رسالة إكمال")
                        return False
                        
                finally:
                    FreeUsersManager.send_bulk_promo_codes_in_batches = original_batch_send
                
            else:
                print(f"❌ رسالة تأكيد غير متوقعة: {sent_message[:200]}...")
                return False
        else:
            print("❌ لم يتم إرسال رسالة تأكيد")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أمر الدفعات: {e}")
        return False

def test_batch_calculation():
    """اختبار حساب الدفعات"""
    try:
        print("\n📊 اختبار حساب الدفعات...")
        
        from free_users_manager import FreeUsersManager
        
        # جلب العدد الفعلي للمستخدمين
        all_users = FreeUsersManager.get_all_free_users()
        total_users = len(all_users)
        
        # حساب الدفعات
        batch_size = 50
        calculated_batches = (total_users + batch_size - 1) // batch_size
        
        print(f"📊 إجمالي المستخدمين: {total_users:,}")
        print(f"📦 حجم الدفعة: {batch_size}")
        print(f"🔢 عدد الدفعات المحسوب: {calculated_batches}")
        
        # حساب التوزيع
        full_batches = total_users // batch_size
        remaining_users = total_users % batch_size
        
        print(f"📦 دفعات كاملة (50 مستخدم): {full_batches}")
        if remaining_users > 0:
            print(f"📦 دفعة أخيرة ({remaining_users} مستخدم): 1")
        
        # حساب الوقت المتوقع
        estimated_time_minutes = calculated_batches * 1.5  # 1.5 دقيقة لكل دفعة
        estimated_time_hours = estimated_time_minutes / 60
        
        print(f"⏰ الوقت المتوقع: {estimated_time_minutes:.0f} دقيقة ({estimated_time_hours:.1f} ساعة)")
        
        # التحقق من المنطق
        if calculated_batches == full_batches + (1 if remaining_users > 0 else 0):
            print("✅ حساب الدفعات صحيح")
            return True
        else:
            print("❌ خطأ في حساب الدفعات")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حساب الدفعات: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار نظام الدفعات الجديد")
    print("=" * 60)
    
    tests = [
        ("اختبار حساب الدفعات", lambda: test_batch_calculation()),
        ("اختبار نظام الدفعات", test_batch_system),
        ("اختبار أمر الدفعات", test_batch_command),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed >= 2:  # نجاح معظم الاختبارات
        print("\n🎉 نظام الدفعات الجديد يعمل بكفاءة!")
        print("\n✅ الميزات الجديدة:")
        print("   ✅ إرسال للجميع على دفعات آمنة")
        print("   ✅ حد أقصى 50 مستخدم لكل دفعة")
        print("   ✅ انتظار 30 ثانية بين الدفعات")
        print("   ✅ تقارير مفصلة عن كل دفعة")
        print("   ✅ رسالة تأكيد قبل البدء")
        
        print("\n🚀 الأوامر الجديدة:")
        print("   /send_promo_active - للمستخدمين النشطين (سريع)")
        print("   /send_promo_all - لأول 50 مستخدم (آمن)")
        print("   /send_promo_all_batches - للجميع على دفعات (شامل)")
        
        print("\n💡 للإرسال للجميع:")
        print("   1. /send_promo_all_batches")
        print("   2. انتظر رسالة التأكيد")
        print("   3. أرسل: 'نعم أريد الإرسال للجميع'")
        print("   4. انتظر إكمال جميع الدفعات")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")

if __name__ == "__main__":
    asyncio.run(main())
