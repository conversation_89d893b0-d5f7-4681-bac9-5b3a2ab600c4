# 🔄 تحديثات process_data.py لدمج نظام أكواد الخصم

## 📋 الهدف
دمج نظام أكواد الخصم والرسائل التحفيزية الجديدة في ملف `process_data.py` لتحسين تجربة المستخدم وزيادة معدلات التحويل.

## 🛠️ التحديثات المطلوبة

### 1. تحديث دالة `can_use_service`

**الكود الحالي:**
```python
def can_use_service(user_id):
    user_data = UserManager.get_user_data(str(user_id))
    if not user_data:
        return False, "لم يتم العثور على بيانات المستخدم"
    
    subscription_type = user_data.get('subscription_type', 'free')
    
    if subscription_type == 'paid':
        if UserManager.is_subscription_active(user_data):
            return True, "مستخدم مدفوع نشط"
        else:
            return False, "انتهى الاشتراك"
    
    # مستخدم مجاني - فحص الحد اليومي
    if UserManager.can_user_make_request(str(user_id)):
        return True, "ضمن الحد المسموح"
    else:
        return False, "تم تجاوز الحد اليومي"
```

**الكود الجديد:**
```python
def can_use_service(user_id):
    user_data = UserManager.get_user_data(str(user_id))
    if not user_data:
        return False, "لم يتم العثور على بيانات المستخدم"
    
    subscription_type = user_data.get('subscription_type', 'free')
    
    if subscription_type in ['paid', 'trial']:
        if UserManager.is_subscription_active(user_data):
            return True, "مستخدم مدفوع نشط"
        else:
            # رسالة تحفيزية لتجديد الاشتراك
            renewal_message = UserManager.send_upgrade_message(user_id, "subscription_expired")
            return False, renewal_message
    
    # مستخدم مجاني - فحص الحد اليومي
    if UserManager.can_user_make_request(str(user_id)):
        # فحص إرسال رسائل تحفيزية حسب الاستخدام
        current_count = UserManager.get_user_daily_count(str(user_id))
        conversion_message = UserManager.check_and_send_conversion_message(user_id, current_count)
        
        if conversion_message:
            # إرسال رسالة تحفيزية إضافية إذا كان المستخدم قريب من الحد
            return True, f"تم قبول الطلب\n\n{conversion_message}"
        
        return True, "ضمن الحد المسموح"
    else:
        # رسالة تحفيزية عند الوصول للحد الأقصى
        upgrade_message = UserManager.send_upgrade_message(user_id, "daily_limit_reached")
        return False, upgrade_message
```

### 2. تحديث دوال التحليل الرئيسية

#### تحديث دالة `analyze_single_stock`:
```python
# في بداية الدالة، بعد فحص البيانات:
can_use, message = can_use_service(user_id)
if not can_use:
    return message  # الرسالة الآن تحتوي على محتوى تحفيزي

# باقي الكود كما هو...
```

#### تحديث دالة `get_stock_chart`:
```python
# إضافة نفس التحديث
can_use, message = can_use_service(user_id)
if not can_use:
    return message
```

#### تحديث دالة `get_stock_analysis`:
```python
# إضافة نفس التحديث
can_use, message = can_use_service(user_id)
if not can_use:
    return message
```

### 3. إضافة دالة جديدة للرسائل التحفيزية الذكية

```python
def send_smart_conversion_message(user_id, context="general"):
    """إرسال رسائل تحفيزية ذكية حسب السياق"""
    try:
        from arabic_messages import CONVERSION_CAMPAIGNS
        
        # اختيار الرسالة حسب السياق
        if context == "market_opportunity":
            return CONVERSION_CAMPAIGNS.get("market_opportunity", "")
        elif context == "success_stories":
            return CONVERSION_CAMPAIGNS.get("success_stories", "")
        elif context == "social_proof":
            return CONVERSION_CAMPAIGNS.get("social_proof", "")
        else:
            # رسالة عامة تحفيزية
            return UserManager.send_upgrade_message(user_id, "conversion_opportunity")
            
    except Exception as e:
        logger.error(f"خطأ في إرسال رسالة تحفيزية ذكية: {e}")
        return ""
```

### 4. تحديث رسائل الخطأ والحدود

**الكود الحالي:**
```python
# في حالة انتهاء الحد اليومي
return "لقد تجاوزت الحد المسموح من الطلبات اليومية. يرجى الانتظار حتى الغد أو الاشتراك في الباقة المدفوعة."
```

**الكود الجديد:**
```python
# استخدام الرسائل التحفيزية الجديدة
upgrade_message = UserManager.send_upgrade_message(user_id, "daily_limit_reached")
return upgrade_message
```

### 5. دمج أكواد الخصم في رسائل التحليل

```python
def add_promo_code_to_message(user_id, original_message):
    """إضافة كود خصم إلى رسالة التحليل إذا كان مناسباً"""
    try:
        # فحص إذا كان المستخدم مؤهل لرؤية كود خصم
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data:
            return original_message
            
        subscription_type = user_data.get('subscription_type', 'free')
        daily_count = UserManager.get_user_daily_count(str(user_id))
        
        # إضافة كود خصم للمستخدمين المجانيين النشطين
        if subscription_type == 'free' and daily_count >= 3:
            promo_footer = """
            
🎁 **عرض خاص لك:**
🎫 كود خصم 30%: `ACTIVE30`
💎 استخدم: `/redeem ACTIVE30`
            """
            return original_message + promo_footer
            
        return original_message
        
    except Exception as e:
        logger.error(f"خطأ في إضافة كود الخصم: {e}")
        return original_message
```

### 6. تحديث دالة `check_subscription_access`

```python
def check_subscription_access(user_id, feature_name="ميزة متقدمة"):
    """فحص الوصول للميزات المدفوعة مع رسائل تحفيزية محسنة"""
    user_data = UserManager.get_user_data(str(user_id))
    if not user_data:
        return False, "لم يتم العثور على بيانات المستخدم"
    
    subscription_type = user_data.get('subscription_type', 'free')
    
    if subscription_type in ['paid', 'trial'] and UserManager.is_subscription_active(user_data):
        return True, "الوصول مسموح"
    
    # رسالة تحفيزية مخصصة للميزة المطلوبة
    blocked_message = UserManager.send_upgrade_message(user_id, "premium_feature_blocked")
    
    # إضافة كود خصم خاص للميزة
    feature_promo = f"""

🎯 **خاص بـ {feature_name}:**
🎫 كود خصم فوري: `FEATURE25`
⚡ تفعيل: `/redeem FEATURE25`
    """
    
    return False, blocked_message + feature_promo
```

## 🎯 الهدف من التحديثات

### 1. تحسين معدل التحويل:
- رسائل تحفيزية مخصصة
- أكواد خصم استراتيجية
- رسائل سياقية ذكية

### 2. تجربة مستخدم محسنة:
- رسائل واضحة ومفيدة
- عروض مناسبة للسياق
- تدرج في التحفيز

### 3. زيادة المشاركة:
- عروض حصرية للمستخدمين النشطين
- رسائل متنوعة تجنب الملل
- حوافز قوية للاشتراك

## 📊 مراقبة النتائج

بعد تطبيق التحديثات، يمكن مراقبة:
- معدل استخدام أكواد الخصم
- معدل التحويل من مجاني لمدفوع
- تفاعل المستخدمين مع الرسائل الجديدة
- تحسن في معدلات الاحتفاظ بالمستخدمين

## 🔄 خطوات التطبيق

1. **النسخ الاحتياطي**: تم إنشاؤه مسبقاً
2. **تطبيق التحديثات**: نسخ الكود الجديد
3. **اختبار النظام**: فحص جميع السيناريوهات
4. **مراقبة الأداء**: تتبع المؤشرات
5. **التحسين المستمر**: تطوير الرسائل حسب النتائج

---

**📝 ملاحظة**: هذه التحديثات ستحول تجربة المستخدم من رسائل خطأ بسيطة إلى فرص تحفيزية قوية للتحويل للاشتراك المدفوع.
