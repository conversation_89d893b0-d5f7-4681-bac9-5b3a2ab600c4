#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استيراد وظائف أوامر البوت
"""

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوظائف"""
    try:
        print("🔍 اختبار استيراد main.py...")
        import main
        print("✅ استيراد main.py نجح")
        
        # فحص المتغيرات المطلوبة
        if hasattr(main, 'PROMO_CODES_AVAILABLE'):
            print(f"✅ PROMO_CODES_AVAILABLE: {main.PROMO_CODES_AVAILABLE}")
        else:
            print("❌ PROMO_CODES_AVAILABLE غير موجود")
        
        # فحص الدوال المطلوبة
        required_functions = [
            'create_trial_code_command',
            'create_discount_code_command', 
            'redeem_promo_code',
            'list_promo_codes'
        ]
        
        for func_name in required_functions:
            if hasattr(main, func_name):
                print(f"✅ {func_name} موجود")
            else:
                print(f"❌ {func_name} غير موجود")
        
        print("\n🎯 انتهى فحص الاستيراد!")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        import traceback
        traceback.print_exc()
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 بدء اختبار استيراد أوامر البوت...")
    test_imports()
