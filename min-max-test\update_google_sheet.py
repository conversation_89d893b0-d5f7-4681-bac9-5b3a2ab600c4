import pandas as pd
import aiogram
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, types
from aiogram.types import Message
from auth import open_google_sheet, dp, bot
from config import file_path, ADMIN_IDS

async def update_alertMessage(broadcast_message):
    sheet_name = "stock"
    sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
    users_data = sheet2.get_all_values()
    for row in users_data:
        chat_id = row[0]
        payment_status = row[2]
        if payment_status == "paid" or payment_status == "trail":
            try:
                print(f'Sending message to {chat_id}')
                await bot.send_message(chat_id, broadcast_message, parse_mode="MARKDOWN")
            except Exception as e:
                print(f"[X] Telegram Error while sending message to {chat_id}:\n>", e)

async def update_google_sheet():
    stock_code_col = 0
    high_price_col = 3

    status_col = 2
    t1_col = 7
    t2_col = 8
    t3_col = 9
    sl_col = 10

    sheet_name = "stock"
    excel_data = pd.read_excel(file_path)
    header = "الرمز\tالاسم\tإفتتاح\tأعلى\tأدنى\tالاغلاق\tالتغير %\tنسبة السيولة %\tفرص مضاربة ساعة\tهدف 1 (يوم)\tt1 (يوم)\tهدف 2 (يوم)\tt2 (يوم)\tهدف 3 (يوم)\tt3 (يوم)\tسيوله (يوم)\tوقف خسارة (يوم)\tحاله السهم (يوم)\tTK (يوم)\tKJ (يوم)\tma5 (يوم)\tma10 (يوم)\tma20 (يوم)\tma50 (يوم)\tma100 (يوم)\tma200 (يوم)\tصافي السيولة\tقيمة الإفتتاح\tالتاريخ\tالاسهم الحرة\tربح السهم (سنوي)\tالقيمة الدفترية\tمكرر الأرباح\tالعائد الربحي (٪)\tراصد الإشارات الفنية"
    header_list = header.split("\t")
    excel_data_filtered = pd.DataFrame(excel_data, columns=header_list)
    excel_data_filtered = excel_data_filtered.iloc[1:, [0, 1, 3, 5]]

    sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
    google_data = sheet3.get_all_values()
    google_data_filtered = pd.DataFrame(google_data, columns=['stock_code', 'stock_name', 'status', 'buy_price', 'buy_date', 'sell_price', 'sell_date', 't1', 't2', 't3', 'sl', 't1_date', 't2_date', 't3_date', 'sl_date'])
    google_data_filtered = google_data_filtered.iloc[1:, [0, 2, 3, 7, 8, 9, 10]]

    for i in range(len(excel_data_filtered)):
        excel_stock_code = excel_data_filtered.iloc[i, 0]
        for j in range(len(google_data_filtered)):
            broadcast_message = ""
            google_stock_code = google_data_filtered.iloc[j, 0]
            if excel_stock_code == google_stock_code:
                stock_name = excel_data_filtered.iloc[i, 1]
                high_price = excel_data_filtered.iloc[i, 2]
                close_price = excel_data_filtered.iloc[i, 3]
                buy_price = float(google_data_filtered.iloc[j, 2])
                t1 = float(google_data_filtered.iloc[j, 3])
                t2 = float(google_data_filtered.iloc[j, 4])
                t3 = float(google_data_filtered.iloc[j, 5])
                sl = float(google_data_filtered.iloc[j, 6])
                status = google_data_filtered.iloc[j, 1]
                if '#' in str(high_price):
                    continue
                if status == 'buy' and float(high_price) >= t1:
                    profit = round((float(high_price) / buy_price - 1) * 100, 2)
                    broadcast_message = f"لقد حقق سهم {stock_name} الهدف الأول المحدد بسعر {t1} بنسبة ربح {profit}%."
                    broadcast_message += f" وسيكون الهدف التالي عند السعر {t2} مع توصية بجني أرباح جزئي كلما ارتفع السهم."
                    sheet3.update_cell(j + 2, 12, str(pd.Timestamp.now().date()))
                    sheet3.update_cell(j + 2, status_col + 1, 't1done')
                    await update_alertMessage(broadcast_message)
                if status in ['buy', 't1done'] and float(high_price) >= t2:
                    profit = round((float(high_price) / buy_price - 1) * 100, 2)
                    broadcast_message = f"لقد حقق سهم {stock_name} الهدف الثاني المحدد بسعر {t2} بنسبة ربح {profit}%."
                    broadcast_message += f" وسيكون الهدف التالي عند السعر {t3} مع توصية بجني أرباح جزئي كلما ارتفع السهم."
                    sheet3.update_cell(j + 2, 13, str(pd.Timestamp.now().date()))
                    sheet3.update_cell(j + 2, status_col + 1, 't2done')
                    await update_alertMessage(broadcast_message)
                if status in ['buy', 't1done', 't2done'] and float(high_price) >= t3:
                    profit = round((float(high_price) / buy_price - 1) * 100, 2)
                    broadcast_message = f"لقد حقق سهم {stock_name} الهدف الثالث المحدد بسعر {t3} بنسبة ربح {profit}%."
                    sheet3.update_cell(j + 2, 14, str(pd.Timestamp.now().date()))
                    sheet3.update_cell(j + 2, status_col + 1, 't3done')
                    await update_alertMessage(broadcast_message)
                if status == 'buy' and float(close_price) <= sl:
                    loss = round((1 - float(close_price) / buy_price) * 100, 2)
                    broadcast_message = f"للأسف! قد تم تحقيق خسائر بنسبة {loss}% في سهم {stock_name} الذي قد تم شراؤه."
                    broadcast_message += " يرجى البيع الفوري لتحديد الخسائر قبل المزيد من التراجع."
                    sheet3.update_cell(j + 2, 15, str(pd.Timestamp.now().date()))
                    sheet3.update_cell(j + 2, status_col + 1, 'tsl')
                    await update_alertMessage(broadcast_message)
    print("Google Sheet updated successfully!")

@dp.message_handler(commands=['comp'])
async def update_sheet(message: Message):
    if message.from_user.id in ADMIN_IDS:
        await update_google_sheet()
        await message.reply("Google Sheet updated successfully!")
    else:
        await message.reply("Sorry, you don't have permission to use this command.")