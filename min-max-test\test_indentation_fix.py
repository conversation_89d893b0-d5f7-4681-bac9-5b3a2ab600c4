#!/usr/bin/env python3
"""
اختبار سريع لفحص إصلاح أوامر البرومو كود
"""

import sys
import subprocess

def test_main_syntax():
    """فحص syntax الملف الرئيسي"""
    print("🔍 فحص syntax الملف الرئيسي...")
    
    try:
        # تجميع الملف للتأكد من عدم وجود أخطاء syntax
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'main.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ main.py syntax صحيح")
            return True
        else:
            print(f"❌ خطأ في syntax main.py:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص syntax: {e}")
        return False

def check_indentation():
    """فحص المسافات في main.py"""
    print("\n🔍 فحص المسافات والترتيب...")
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # البحث عن تسجيل أوامر البرومو
    promo_start = None
    general_handler = None
    
    for i, line in enumerate(lines):
        if 'تسجيل أوامر نظام أكواد الخصم' in line:
            promo_start = i
            print(f"✅ وجد تسجيل أوامر البرومو في السطر {i+1}")
        
        if '@dp.message_handler()' in line:
            general_handler = i
            print(f"✅ وجد المعالج العام في السطر {i+1}")
    
    if promo_start and general_handler:
        if promo_start < general_handler:
            print("✅ ترتيب صحيح: أوامر البرومو قبل المعالج العام")
        else:
            print("❌ ترتيب خاطئ: أوامر البرومو بعد المعالج العام")
    
    # فحص المسافات
    if promo_start:
        promo_line = lines[promo_start]
        if promo_line.startswith('    #'):  # 4 مسافات
            print("✅ المسافات صحيحة (4 مسافات)")
        elif promo_line.startswith('      #'):  # 6 مسافات
            print("❌ المسافات خاطئة (6 مسافات - داخل دالة)")
        else:
            print(f"❓ مسافات غريبة: '{promo_line[:10]}'")

def check_functions_defined():
    """فحص تعريف الدوال"""
    print("\n🔍 فحص تعريف الدوال...")
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    functions = [
        'create_trial_code_command',
        'create_discount_code_command',
        'redeem_promo_code',
        'list_promo_codes'
    ]
    
    for func in functions:
        if f"async def {func}" in content:
            print(f"✅ {func} مُعرّفة")
        else:
            print(f"❌ {func} غير مُعرّفة")

if __name__ == "__main__":
    print("🚀 فحص إصلاح أوامر البرومو كود...")
    print("="*50)
    
    # فحص syntax
    syntax_ok = test_main_syntax()
    
    # فحص المسافات
    check_indentation() 
    
    # فحص تعريف الدوال
    check_functions_defined()
    
    print("\n" + "="*50)
    if syntax_ok:
        print("✅ الإصلاح تم بنجاح - يمكن تشغيل البوت الآن")
    else:
        print("❌ هناك أخطاء تحتاج إصلاح")
