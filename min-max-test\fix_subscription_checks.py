#!/usr/bin/env python3
"""
<PERSON>ript to fix subscription checks in process_data.py
This script replaces the pattern where check_user_limit is called before subscription check
"""

import re

def fix_subscription_checks():
    file_path = "c:\\Users\\<USER>\\Desktop\\min-max-test\\process_data.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to find and replace
    pattern = r'(\s+)subscriber_type, count = await check_user_limit\(message\)\s+if subscriber_type not in \["paid", "trail"\]:\s+await message\.reply\([^)]+\)(?:, parse_mode="Markdown")?\s+return'
    
    replacement = r'\1# Check subscription access first without updating count\n\1if not await check_subscription_access(message):\n\1    return\n\1\n\1# Now check user limit and update count\n\1subscriber_type, count = await check_user_limit(message)'
    
    # Apply the replacement
    new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("Subscription check fixes applied!")

if __name__ == "__main__":
    fix_subscription_checks()
