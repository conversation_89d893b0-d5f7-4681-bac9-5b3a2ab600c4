import asyncio
import datetime
from aiogram.types import Message
from auth import authenticate_google_sheet, dp, bot

@dp.message_handler(commands=['reset_counters'])
async def reset_counters_command(message: Message):
    # Reset the counters in the background
    failed_user_ids, expired_users, reminded_users = await reset_counters_background()
    
    # Send a message to confirm that the command was executed
    success_message = "Counters have been reset."
    if expired_users:
        success_message += f"\n{len(expired_users)} paid/trial subscriptions have expired and been converted to free users."
    if reminded_users:
        success_message += f"\n{len(reminded_users)} users received renewal reminders for upcoming subscription expiration."
    if failed_user_ids:
        success_message += f"\nFailed to send message to the following users: {', '.join(failed_user_ids)}"
    await bot.send_message(chat_id=message.chat.id, text=success_message)

# Define the function to reset the counters
async def reset_counters(sheet2):
    failed_user_ids = []
    expired_users = []
    reminded_users = []
    user_data = sheet2.get_all_values()
    header_row = 1  # Assuming first row is header, adjust if needed
    today = datetime.datetime.now().date()
    
    # Define reminder thresholds (days before expiration)
    reminder_thresholds = [7, 3, 1]  # Send reminder 7 days, 3 days, and 1 day before expiration
    
    for index, row in enumerate(user_data[header_row:], start=header_row+1):
        try:
            # Check and reset free user counters
            if len(row) > 2 and row[2] == "free" and int(row[1]) > 0:
                sheet2.update_cell(index, 2, 0)  # update the counter to 0
                await asyncio.sleep(1)
                
                usr_adv_message = "Your daily (3) stock reports is available now. You can use it. "
                usr_adv_message += "تقاريرك اليومية المجانيه متاحة الأن يمكنك الأطلاع عليها ومتابعه اسهمك "
                usr_adv_message += "لمزيد من التوصيات اللحظيه و اليومية و التقارير الأحترافيه "
                usr_adv_message += "للأشتراك اضغط على كلمه اشتراك فى لوحه التحكم واتبع التعليمات"
                
                try:
                    await bot.send_message(chat_id=row[0], text=usr_adv_message)
                    await asyncio.sleep(1)
                except Exception as e:
                    print(f"Failed to send message to user ID {row[0]}: {e}")
                    failed_user_ids.append(row[0])
            
            # Check for expired paid/trial subscriptions
            elif len(row) > 3 and (row[2] == "paid" or row[2] == "trail") and row[3]:
                try:
                    # Parse end date based on format YYYY-MM-DD
                    end_date_str = row[3]
                    end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                    
                    # Check if subscription has expired
                    if end_date < today:
                        print(f"User {row[0]} subscription has expired. Converting to free user.")
                        sheet2.update_cell(index, 3, "free")  # Change subscription type to free
                        sheet2.update_cell(index, 2, 0)  # Reset counter to 0
                        expired_users.append(row[0])
                        
                        # Send expired subscription message
                        expire_message = "⚠️ *انتهاء الاشتراك* ⚠️\n\n"
                        expire_message += "عزيزي المستخدم، لقد انتهت صلاحية اشتراكك المدفوع. "
                        expire_message += "تم تحويل حسابك إلى الإصدار المجاني المحدود.\n\n"
                        expire_message += "للاستمرار في الحصول على جميع المزايا الكاملة، يرجى تجديد اشتراكك "
                        expire_message += "باستخدام الأمر: /subscribe\n\n"
                        expire_message += "شكراً لاستخدامك خدماتنا!"
                        
                        try:
                            await bot.send_message(chat_id=row[0], text=expire_message, parse_mode="Markdown")
                            await asyncio.sleep(1)
                        except Exception as e:
                            print(f"Failed to send expiration message to user ID {row[0]}: {e}")
                            failed_user_ids.append(row[0])
                    
                    # Check if subscription is approaching expiration and send reminder
                    else:
                        days_until_expiration = (end_date - today).days
                        
                        # Send reminders at specific thresholds
                        if days_until_expiration in reminder_thresholds:
                            print(f"User {row[0]} subscription expires in {days_until_expiration} days. Sending reminder.")
                            reminded_users.append(row[0])
                            
                            # Get subscription type in Arabic
                            subscription_type_ar = "مدفوع" if row[2] == "paid" else "تجريبي"
                            
                            # Create renewal reminder message
                            reminder_message = f"🔔 *تذكير بتجديد الاشتراك* 🔔\n\n"
                            reminder_message += f"عزيزي المستخدم، سينتهي اشتراكك ال{subscription_type_ar} بعد *{days_until_expiration} يوم*.\n\n"
                            
                            if days_until_expiration <= 3:
                                reminder_message += "⚠️ *تنبيه:* الرجاء تجديد اشتراكك قريباً لتجنب انقطاع الخدمة وفقدان المزايا!\n\n"
                            
                            reminder_message += "للتجديد، يرجى استخدام أحد الخيارات التالية:\n"
                            reminder_message += "• استخدم الأمر: /subscribe\n"
                            reminder_message += "• اضغط على 'اشتراك / تجديد' في القائمة الرئيسية /menu\n\n"
                            
                            # Add feature highlights based on days remaining
                            if days_until_expiration <= 3:
                                reminder_message += "🌟 *أهم المزايا التي ستفقدها إذا لم تجدد:*\n"
                                reminder_message += "• توصيات شراء/بيع دقيقة مع الأهداف السعرية\n"
                                reminder_message += "• تحليلات غير محدودة للأسهم\n"
                                reminder_message += "• تنبيهات فورية للفرص الاستثمارية\n\n"
                            
                            reminder_message += "نشكرك على ثقتك بنا ونتطلع لاستمرار خدمتك! 🚀"
                            
                            try:
                                await bot.send_message(chat_id=row[0], text=reminder_message, parse_mode="Markdown")
                                await asyncio.sleep(1)
                            except Exception as e:
                                print(f"Failed to send renewal reminder to user ID {row[0]}: {e}")
                                failed_user_ids.append(row[0])
                    
                except (ValueError, TypeError) as e:
                    print(f"Error processing date for user {row[0]}: {e}")
                
        except Exception as e:
            print(f"Error processing user {row[0] if len(row) > 0 else 'unknown'}: {e}")
    
    print(f"Counters have been reset. {len(expired_users)} subscriptions expired. {len(reminded_users)} renewal reminders sent.")
    return failed_user_ids, expired_users, reminded_users

# Define the function to start the background task
async def reset_counters_background():
    sheet2 = authenticate_google_sheet()
    failed_user_ids, expired_users, reminded_users = await reset_counters(sheet2)
    return failed_user_ids, expired_users, reminded_users
