#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لأوامر البرومو كود
Simple test for promo code commands
"""

import sys
import os

def test_promo_system():
    """اختبار نظام البرومو كود"""
    print("🧪 اختبار نظام البرومو كود...")
    
    try:
        # اختبار استيراد النظام
        from promo_codes import PromoCodeManager
        from user_limit import UserManager
        import datetime
        print("✅ تم استيراد جميع المكونات بنجاح")
        
        # اختبار إنشاء كود تجربة
        print("\n📝 اختبار إنشاء كود تجربة...")
        trial_code = PromoCodeManager.create_trial_code(7, 30, "كود اختبار")
        if trial_code:
            print(f"✅ تم إنشاء كود التجربة: {trial_code}")
        else:
            print("❌ فشل في إنشاء كود التجربة")
        
        # اختبار إنشاء كود خصم
        print("\n💰 اختبار إنشاء كود خصم...")
        discount_code = PromoCodeManager.create_discount_code(25, 30, "خصم اختبار")
        if discount_code:
            print(f"✅ تم إنشاء كود الخصم: {discount_code}")
        else:
            print("❌ فشل في إنشاء كود الخصم")
        
        # اختبار عرض الأكواد
        print("\n📋 اختبار عرض الأكواد النشطة...")
        active_codes = PromoCodeManager.list_active_codes()
        if active_codes is not None:
            print(f"✅ تم العثور على {len(active_codes)} كود نشط")
            for code in active_codes[-2:]:  # عرض آخر كودين
                print(f"   - {code['code']}: {code['type']} ({code['value']})")
        else:
            print("❌ فشل في عرض الأكواد")
        
        # اختبار التحقق من كود (إذا تم إنشاء كود)
        if trial_code:
            print(f"\n🔍 اختبار التحقق من الكود: {trial_code}")
            is_valid, msg, data = PromoCodeManager.validate_promo_code(trial_code, "123456789")
            if is_valid:
                print(f"✅ الكود صحيح: {msg}")
                print(f"   النوع: {data['type']}, القيمة: {data['value']}")
            else:
                print(f"❌ الكود غير صحيح: {msg}")
        
        print("\n🎯 خلاصة الاختبار:")
        print("✅ نظام البرومو كود يعمل بشكل صحيح")
        print("✅ جميع الدوال الأساسية تعمل")
        print("✅ الاتصال بـ Google Sheets يعمل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_functions():
    """اختبار دوال الأوامر"""
    print("\n🤖 اختبار دوال أوامر البوت...")
    
    try:
        # محاولة استيراد الدوال من main.py (بدون تشغيل البوت)
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_module", "main.py")
        main_module = importlib.util.module_from_spec(spec)
        
        # تحديد الدوال المطلوبة
        required_functions = [
            'create_trial_code_command',
            'create_discount_code_command',
            'redeem_promo_code',
            'list_promo_codes'
        ]
        
        print("🔍 التحقق من وجود دوال الأوامر...")
        for func_name in required_functions:
            # محاولة قراءة الملف والبحث عن الدالة
            with open('main.py', 'r', encoding='utf-8') as f:
                content = f.read()
                if f"async def {func_name}" in content:
                    print(f"✅ {func_name}: موجودة")
                else:
                    print(f"❌ {func_name}: غير موجودة")
        
        print("✅ تم فحص دوال الأوامر")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص دوال الأوامر: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار شامل لنظام البرومو كود")
    print("=" * 50)
    
    # اختبار النظام الأساسي
    system_ok = test_promo_system()
    
    # اختبار دوال الأوامر
    commands_ok = test_command_functions()
    
    print("\n" + "=" * 50)
    print("📊 النتيجة النهائية:")
    if system_ok and commands_ok:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ أوامر البرومو كود جاهزة للاستخدام:")
        print("   - /create_trial_code")
        print("   - /create_discount_code") 
        print("   - /redeem")
        print("   - /list_codes")
    else:
        print("❌ بعض الاختبارات فشلت")
        if not system_ok:
            print("   - مشكلة في النظام الأساسي")
        if not commands_ok:
            print("   - مشكلة في دوال الأوامر")
    print("=" * 50)
