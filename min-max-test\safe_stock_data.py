"""
Module providing safe versions of stock data functions with robust error handling
"""
import pandas as pd
import logging

logger = logging.getLogger(__name__)

def safe_get_stock_data(file_path, stock_code, subscriber_type, get_stock_alert_func, sheet3=None):
    """
    A safer version of get_stock_data that handles missing or invalid values.
    
    Args:
        file_path: Path to the Excel file
        stock_code: Stock code to look up
        subscriber_type: Type of subscriber
        get_stock_alert_func: Function to get stock alerts
        sheet3: Google sheet for alerts data
        
    Returns:
        Formatted stock data string
    """
    try:
        # Load data from Excel
        df = pd.read_excel(file_path)
        stock_data = df[df.iloc[:, 0].astype(str) == stock_code].iloc[0]
        
        # Extract basic data
        code = stock_data[0]
        name = stock_data[1]
        
        # Safely extract and convert prices
        def safe_float(value, default=0.0):
            """Safely convert value to float, return default if conversion fails"""
            if value == '-' or pd.isna(value):
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                logger.warning(f"Could not convert '{value}' to float, using {default}")
                return default
                
        open_price = safe_float(stock_data[2])
        highx = safe_float(stock_data[3])
        lowx = safe_float(stock_data[4])
        closex = safe_float(stock_data[5])
        
        # Use a sensible rounding value
        rnd = 3 if closex < 2 else 2

        # Get stock alert if subscriber is paid or trial
        if subscriber_type in ["paid", "trail"] and sheet3:
            stock_alert = get_stock_alert_func(sheet3, stock_code, closex)
        else:
            stock_alert = "هذا الجزء مخصص فقط للمشتركين"

        # Extract other data
        volume = safe_float(stock_data[7])
        change = safe_float(stock_data[6])
        money_flow = stock_data[15] if len(stock_data) > 15 else "-"
        status = stock_data[17] if len(stock_data) > 17 else "-"
        net_flow = safe_float(stock_data[26]) if len(stock_data) > 26 else 0
        report_date = stock_data[28] if len(stock_data) > 28 else "-"
        free_stocks = stock_data[29] if len(stock_data) > 29 else "-"
        yearly_stock_profit = safe_float(stock_data[30]) if len(stock_data) > 30 else 0
        stock_inti_value = safe_float(stock_data[31]) if len(stock_data) > 31 else 0
        repeated_profit = safe_float(stock_data[32]) if len(stock_data) > 32 else 0
        stock_profit_prc = safe_float(stock_data[33]) if len(stock_data) > 33 else 0
        stock_vol = safe_float(stock_data[34]) if len(stock_data) > 34 else 0

        # Convert status and flow values to Arabic text
        from process_data import STATUS_MAP, MONEY_FLOW_MAP
        money_flow_ar = MONEY_FLOW_MAP.get(str(money_flow).strip(), "غير متوفر")
        status_ar = STATUS_MAP.get(str(status).strip(), "غير متوفر")
        yearly_stock_profit_status = " 🟢 شركة رابحه 🟢" if yearly_stock_profit > 0 else " 🔴 شركة خاسرة 🔴"

        # Safely extract target values
        def safe_target(value, rnd=2):
            if value == "-" or pd.isna(value):
                return "-"
            try:
                return round(float(value), rnd)
            except (ValueError, TypeError):
                return "-"
                
        target1 = safe_target(stock_data[9], rnd) if len(stock_data) > 9 else "-"
        target2 = safe_target(stock_data[11], rnd) if len(stock_data) > 11 else "-"
        target3 = safe_target(stock_data[13], rnd) if len(stock_data) > 13 else "-"
        stop_loss = safe_target(stock_data[16], rnd) if len(stock_data) > 16 else "-"

        # Check if targets are met
        target1_done_ar = "🟢 تحقق الهدف" if len(stock_data) > 10 and str(stock_data[10]).lower() == "true" else "🔴 لم يتحقق بعد"
        target2_done_ar = "🟢 تحقق الهدف" if len(stock_data) > 12 and str(stock_data[12]).lower() == "true" else "🔴 لم يتحقق بعد"
        target3_done_ar = "🟢 تحقق الهدف" if len(stock_data) > 14 and str(stock_data[14]).lower() == "true" else "🔴 لم يتحقق بعد"

        # Generate text for change and volume
        change_ar = "وبنسبة ارتفاع قدرة " if change > 0 else "وبنسبة انخفاض قدرة "
        volume_ar = "السيوله مرتفعه ونتوقع ارتفاع السعر" if volume >= 70 else "السيوله متوسطه" if 50 < volume < 70 else "السيوله منخفصة ونتوقع انخفاض السعر"

        # Safely get technical indicators
        tenkan = safe_target(stock_data[18], rnd) if len(stock_data) > 18 else 0
        kijun = safe_target(stock_data[19], rnd) if len(stock_data) > 19 else 0

        # Generate advice based on subscription
        if subscriber_type in ["paid", "trail"]:
            if tenkan >= kijun:
                if closex >= tenkan:
                    if closex >= kijun:
                        advice = f"نوصى بالأحتفاظ طالما السعر اعلى من {tenkan} مع جنى ارباح جزئى"
                    else:
                        advice = f"نوصى بالمتاجرة الحذرة طالما السعر اقل من {kijun} مع الحذر من كسر الدعم {kijun}"
                else:
                    advice = f"نوصى بتقليل المراكز الشرائية طالما السعر اقل من {tenkan}"
            else:
                if closex >= tenkan:
                    advice = f"متاجرة مع الحذر من كسر الدعم {kijun}"
                else:
                    advice = f"نوصى بتقليل المراكز الشرائية طالما السعر اقل من {tenkan}"
        else:
            advice = "هذا الجزء مخصص فقط للمشتركين"

        # Safely get moving averages
        ma_values = []
        for i in range(20, 26):
            if len(stock_data) > i:
                ma_values.append(round(safe_float(stock_data[i]), rnd))
            else:
                ma_values.append(0)
                
        ma5, ma10, ma20, ma50, ma100, ma200 = ma_values

        # Generate moving average analysis
        if subscriber_type in ["paid", "trail"]:
            m_avg = " الرؤية طبقا للمتوسطات المتحركة: "
            long_term_trend = "صاعد" if closex > ma200 else "هابط"
            m_avg += f"الإتجاه {long_term_trend} على المدى الطويل و "
            medium_term_trend = "صاعد" if closex > ma50 else "هابط"
            m_avg += f"الإتجاه {medium_term_trend} على المدى المتوسط و  "
            short_term_trend = "صاعد" if closex > ma20 else "هابط"
            m_avg += f"الإتجاه {short_term_trend} على المدى القصير و"
            if all(closex < ma for ma in [ma5, ma10, ma20, ma50, ma100, ma200]):
                m_avg += " اسفل  جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى مقاومة متحرك يحول دون صعودة  "
            elif all(closex > ma for ma in [ma5, ma10, ma20, ma50, ma100, ma200]):
                m_avg += "فوق جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى دعم متحرك يحول دون هبوطه  "
            else:
                m_avg += "فوق جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى دعم متحرك يحول دون هبوطه  "
                m_avg += " ماعدا "

            for ma in [(ma5, 5), (ma10, 10), (ma20, 20), (ma50, 50), (ma100, 100), (ma200, 200)]:
                if closex <= ma[0]:
                    m_avg += f" المتوسط المتحرك ({ma[1]}) يمثل مقاومة عند السعر ({ma[0]}) "

            m_avg = m_avg[:-1]  # Remove trailing space
        else:
            m_avg = "هذا الجزء مخصص فقط للمشتركين"

        # Calculate pivot levels
        p = round((highx + closex + lowx) / 3, rnd)
        s1 = round(2 * p - highx, rnd)
        s3 = round((lowx - 2 * (highx - p)), rnd)
        r1 = round((2 * p - lowx), rnd)
        s2 = round(p - (r1 - s1), rnd)
        r2 = round(p + (r1 - s1), rnd)
        r3 = round(highx + (2 * (p - lowx)), rnd)

        # Build the final report
        stock_datas = (
            f"تقرير فنى عن السهم بتاريخ: ({report_date})\n"
            f"{code} - {name}\n"
            f"كان سعر الفتح للسهم: ({open_price})\n"
            f"واعلى سعر: ({highx})\n"
            f"واقل سعر: ({lowx})\n"
            f"واغلق عند السعر: ({closex})\n"
            f"{change_ar}: ({change}%)\n"
            f"وكان حجم التداول للسهم هو :({stock_vol})\n"
            f"وكانت نسبة السيوله بالسهم: ( {volume}% )--{volume_ar}\n"
            f"نوع السيوله: {money_flow_ar}   صافى السيوله: ( {net_flow} ) \n"
            f"حاله السهم: {status_ar}\n"
            f"ارتكاز السهم: ({p})\n"
            f"الهدف الأول: {target1} {target1_done_ar}\n"
            f"الهدف الثانى: {target2} {target2_done_ar}\n"
            f"الهدف الثالث: {target3} {target3_done_ar}\n"
            f"وقف الخسارة: {stop_loss}\n"
            f"المقاومات اليومية امام السهم لجلسة الغد بالترتيب: {r1} - {r2} - {r3}\n"
            f"الدعوم اليومية امام السهم لجلسة الغد بالترتيب: {s1} - {s2} - {s3}\n"
            f"========================\n"
            f"التوصية: {advice} \n"
            f"توصيات المشتركين:{stock_alert} \n"
            f"{m_avg} \n"
            f"========================\n"
            f"التحليل المالى للشركة \n"
            f"اجمالى الأسهم الحرة: {free_stocks}  سهم \n"
            f" الربح السنوى للسهم بالجنية: {yearly_stock_profit}{yearly_stock_profit_status}\n"
            f"  القيمه الدفتريه : {stock_inti_value}\n "
            f" مكرر الربحية للسهم : {repeated_profit}\n"
            f"نسبة ربحية السهم طبقا للربح السنوى  : {stock_profit_prc} %  \n"
            f"========================\n"
            f"هذا التقرير صالح لجلسة الغد فقط وسيتم تحديثة يوميا \n"
            f"مع تحيات فريق عمل المحلل الألى لأسهم البورصه المصريه"
        )
        return stock_datas
        
    except Exception as e:
        logger.error(f"Error in get_stock_data: {e}", exc_info=True)
        return f"حدث خطأ أثناء معالجة بيانات السهم: {stock_code}. الرجاء المحاولة مرة أخرى لاحقًا."
