#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام معالجة المستخدمين المحظورين
Test blocked users handling system
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

async def test_blocked_user_detection():
    """اختبار كشف المستخدمين المحظورين"""
    try:
        print("🧪 اختبار كشف المستخدمين المحظورين...")
        
        from free_users_manager import FreeUsersManager
        
        # محاكاة أخطاء مختلفة
        test_cases = [
            {
                "error": "Forbidden: bot was blocked by the user",
                "expected_reason": "حظر البوت",
                "description": "المستخدم حظر البوت"
            },
            {
                "error": "Forbidden: user is deactivated", 
                "expected_reason": "حساب معطل",
                "description": "المستخدم ألغى تفعيل حسابه"
            },
            {
                "error": "Bad Request: chat not found",
                "expected_reason": "حساب غير موجود", 
                "description": "الحساب محذوف"
            },
            {
                "error": "Forbidden: access denied",
                "expected_reason": "محظور",
                "description": "محظور عام"
            }
        ]
        
        # محاكاة دالة معالجة المستخدم المحظور
        handled_users = []
        
        async def mock_handle_blocked_user(user_id, reason):
            handled_users.append({"user_id": user_id, "reason": reason})
            return True
        
        # استبدال الدالة الأصلية
        original_handle = FreeUsersManager.handle_blocked_user
        FreeUsersManager.handle_blocked_user = mock_handle_blocked_user
        
        try:
            # اختبار كل حالة
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n   📋 اختبار {i}: {test_case['description']}")
                
                # محاكاة إرسال رسالة مع خطأ
                with patch('free_users_manager.bot.send_message') as mock_send:
                    mock_send.side_effect = Exception(test_case['error'])
                    
                    # محاولة الإرسال
                    result = await FreeUsersManager.send_promo_code_to_user(
                        user_id=f"test_user_{i}",
                        promo_code="TEST123",
                        user_name=f"مستخدم اختبار {i}"
                    )
                    
                    # التحقق من النتيجة
                    if not result:  # يجب أن يفشل الإرسال
                        print(f"   ✅ تم كشف الخطأ بشكل صحيح")
                    else:
                        print(f"   ❌ لم يتم كشف الخطأ")
            
            # التحقق من معالجة المستخدمين المحظورين
            print(f"\n📊 نتائج المعالجة:")
            print(f"   - تم معالجة {len(handled_users)} مستخدم محظور")
            
            for user in handled_users:
                print(f"   - {user['user_id']}: {user['reason']}")
            
            if len(handled_users) == len(test_cases):
                print("✅ تم كشف ومعالجة جميع المستخدمين المحظورين")
                return True
            else:
                print("❌ لم يتم معالجة جميع المستخدمين المحظورين")
                return False
                
        finally:
            # إعادة الدالة الأصلية
            FreeUsersManager.handle_blocked_user = original_handle
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كشف المحظورين: {e}")
        return False

async def test_user_blocking_functions():
    """اختبار دوال حظر المستخدمين"""
    try:
        print("\n🧪 اختبار دوال حظر المستخدمين...")
        
        from user_subscription_manager import UserSubscriptionManager
        
        # محاكاة بيانات مستخدم
        test_user_id = "test_blocked_user"
        
        # محاكاة دوال الجدول
        original_find = UserSubscriptionManager.find_user_in_sheet
        original_update = UserSubscriptionManager.update_user_subscription
        
        def mock_find_user(user_id):
            if user_id == test_user_id:
                return {
                    'row': 100,
                    'user_id': user_id,
                    'subscription_type': 'free',
                    'first_name': 'مستخدم',
                    'last_name': 'اختبار'
                }
            return None
        
        def mock_update_subscription(user_id, sub_type, end_date=None):
            print(f"   📝 محاكاة تحديث: {user_id} -> {sub_type}")
            return True
        
        UserSubscriptionManager.find_user_in_sheet = mock_find_user
        UserSubscriptionManager.update_user_subscription = mock_update_subscription
        
        try:
            # اختبار وضع علامة حظر
            print("   🚫 اختبار وضع علامة حظر:")
            result = UserSubscriptionManager.mark_user_as_blocked(test_user_id, "اختبار")
            
            if result:
                print("   ✅ تم وضع علامة الحظر بنجاح")
            else:
                print("   ❌ فشل في وضع علامة الحظر")
            
            return result
            
        finally:
            # إعادة الدوال الأصلية
            UserSubscriptionManager.find_user_in_sheet = original_find
            UserSubscriptionManager.update_user_subscription = original_update
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الحظر: {e}")
        return False

async def test_cleanup_commands():
    """اختبار أوامر التنظيف"""
    try:
        print("\n🤖 اختبار أوامر التنظيف...")
        
        from free_users_manager import (
            clean_blocked_users_command,
            confirm_clean_blocked_users_command,
            blocked_users_stats_command
        )
        
        # محاكاة رسالة من المدير
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 868182073  # معرف المدير
        
        # اختبار أمر الإحصائيات
        print("   📊 اختبار أمر الإحصائيات:")
        await blocked_users_stats_command(mock_message)
        
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            
            if "إحصائيات المستخدمين والمحظورين" in sent_message:
                print("   ✅ أمر الإحصائيات يعمل")
                stats_working = True
            else:
                print("   ❌ أمر الإحصائيات لا يعمل")
                stats_working = False
        else:
            print("   ❌ لم يتم إرسال رسالة إحصائيات")
            stats_working = False
        
        # إعادة تعيين المحاكاة
        mock_message.reply.reset_mock()
        
        # اختبار أمر التنظيف
        print("   🧹 اختبار أمر التنظيف:")
        
        # محاكاة وجود مستخدمين محظورين
        from free_users_manager import FreeUsersManager
        original_get_blocked = FreeUsersManager.get_blocked_users_count
        FreeUsersManager.get_blocked_users_count = lambda: 5  # محاكاة 5 مستخدمين محظورين
        
        try:
            await clean_blocked_users_command(mock_message)
            
            if mock_message.reply.called:
                call_args = mock_message.reply.call_args
                sent_message = call_args[0][0]
                
                if "تنظيف المستخدمين المحظورين" in sent_message:
                    print("   ✅ أمر التنظيف يعمل")
                    cleanup_working = True
                else:
                    print("   ❌ أمر التنظيف لا يعمل")
                    cleanup_working = False
            else:
                print("   ❌ لم يتم إرسال رسالة تنظيف")
                cleanup_working = False
                
        finally:
            FreeUsersManager.get_blocked_users_count = original_get_blocked
        
        return stats_working and cleanup_working
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أوامر التنظيف: {e}")
        return False

def test_error_patterns():
    """اختبار أنماط الأخطاء"""
    try:
        print("\n🔍 اختبار أنماط الأخطاء:")
        
        # أنماط الأخطاء المختلفة
        error_patterns = [
            ("Forbidden: bot was blocked by the user", "حظر البوت"),
            ("Forbidden: user is deactivated", "حساب معطل"),
            ("Bad Request: chat not found", "حساب غير موجود"),
            ("Forbidden: access denied", "محظور"),
            ("Network error", "خطأ شبكة")  # لا يجب معالجته كحظر
        ]
        
        detected_blocks = 0
        
        for error_msg, expected_type in error_patterns:
            print(f"   🧪 اختبار: {error_msg}")
            
            # فحص ما إذا كان الخطأ يستدعي الحظر
            should_block = any(keyword in error_msg for keyword in [
                "bot was blocked by the user",
                "user is deactivated", 
                "chat not found",
                "Forbidden"
            ])
            
            if should_block and expected_type != "خطأ شبكة":
                detected_blocks += 1
                print(f"   ✅ تم كشف كحظر: {expected_type}")
            elif not should_block and expected_type == "خطأ شبكة":
                print(f"   ✅ لم يتم كشف كحظر (صحيح): {expected_type}")
            else:
                print(f"   ❌ كشف خاطئ: {expected_type}")
        
        expected_blocks = 4  # 4 أنواع حظر من أصل 5 أخطاء
        
        if detected_blocks == expected_blocks:
            print(f"✅ تم كشف {detected_blocks} نوع حظر بشكل صحيح")
            return True
        else:
            print(f"❌ تم كشف {detected_blocks} من أصل {expected_blocks} نوع حظر")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أنماط الأخطاء: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار نظام معالجة المستخدمين المحظورين")
    print("=" * 70)
    
    tests = [
        ("اختبار أنماط الأخطاء", lambda: test_error_patterns()),
        ("اختبار كشف المحظورين", test_blocked_user_detection),
        ("اختبار دوال الحظر", test_user_blocking_functions),
        ("اختبار أوامر التنظيف", test_cleanup_commands),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed >= 3:  # نجاح معظم الاختبارات
        print("\n🎉 نظام معالجة المحظورين يعمل بكفاءة!")
        print("\n✅ الميزات المحققة:")
        print("   ✅ كشف تلقائي للمستخدمين المحظورين")
        print("   ✅ معالجة أخطاء حظر البوت")
        print("   ✅ معالجة الحسابات المعطلة")
        print("   ✅ معالجة الحسابات المحذوفة")
        print("   ✅ وضع علامات حظر بدلاً من الحذف")
        print("   ✅ أوامر تنظيف إدارية")
        
        print("\n🚀 الأوامر الجديدة:")
        print("   /blocked_stats - إحصائيات المحظورين")
        print("   /clean_blocked - تنظيف المحظورين")
        
        print("\n💡 النتيجة المتوقعة:")
        print("   - تحسين معدل نجاح الإرسال")
        print("   - تنظيف تلقائي لقاعدة البيانات")
        print("   - إحصائيات دقيقة للمستخدمين")
        print("   - لا مزيد من الأخطاء المتكررة")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")

if __name__ == "__main__":
    asyncio.run(main())
