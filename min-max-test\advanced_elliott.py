import numpy as np
import pandas as pd
import logging
import talib
from scipy.signal import find_peaks
import math

# Configure logger
logger = logging.getLogger(__name__)

class AdvancedElliottWaveAnalysis:
    """
    تحليل متقدم لموجات إليوت يتضمن الأنماط المتقدمة، وتحليل متعدد الإطارات الزمنية، 
    والتصنيف الدقيق لدرجات الموجات
    """
    
    def __init__(self):
        # قواعد النسب المثالية للموجات
        self.fibonacci_ratios = {
            'wave1_to_wave3': 1.618,  # نسبة امتداد الموجة 3 مقارنة بالموجة 1
            'wave1_to_wave5': 0.618,   # نسبة امتداد الموجة 5 مقارنة بالموجة 1
            'wave2_retracement': [0.382, 0.5, 0.618],  # نسب تصحيح الموجة 2
            'wave4_retracement': [0.236, 0.382, 0.5],  # نسب تصحيح الموجة 4
            'extension_ratios': [1.618, 2.618, 4.236],  # نسب امتداد فيبوناتشي
            'abc_ratios': {
                'flat': {'b_to_a': 0.9, 'c_to_a': 1.0},
                'zigzag': {'b_to_a': 0.618, 'c_to_a': 1.618},
                'triangle': {'b_to_a': 0.618, 'c_to_b': 0.618, 'd_to_c': 0.618, 'e_to_d': 0.618}
            }
        }
        
        # درجات الموجات من الأكبر إلى الأصغر
        self.wave_degrees = [
            "Grand Supercycle", "Supercycle", "Cycle", "Primary", 
            "Intermediate", "Minor", "Minute", "Minuette", "Subminuette"
        ]
    
    def _find_swing_points(self, df, method='price_swing', threshold=3):
        """
        تحديد نقاط التأرجح (القمم والقيعان) في البيانات
        
        Args:
            df: إطار البيانات
            method: طريقة تحديد نقاط التأرجح ('price_swing', 'zigzag', 'advanced')
            threshold: قيمة العتبة لتحديد نقاط التأرجح
            
        Returns:
            list: قائمة بنقاط التأرجح المحددة
        """
        swing_points = []
        
        if method == 'price_swing':
            # طريقة نقاط التأرجح التقليدية
            for i in range(threshold, len(df) - threshold):
                # القمم المحلية
                if df['HIGH'].iloc[i] == df['HIGH'].iloc[i-threshold:i+threshold+1].max():
                    swing_points.append({
                        'type': 'high',
                        'price': df['HIGH'].iloc[i],
                        'index': df.index[i],
                        'position': i
                    })
                # القيعان المحلية
                elif df['LOW'].iloc[i] == df['LOW'].iloc[i-threshold:i+threshold+1].min():
                    swing_points.append({
                        'type': 'low',
                        'price': df['LOW'].iloc[i],
                        'index': df.index[i],
                        'position': i
                    })
        
        elif method == 'zigzag':
            # استخدام خوارزمية ZigZag بنسبة تغير محددة
            change_threshold = 0.03  # 3% تغيير على الأقل
            highs = np.array(df['HIGH'])
            lows = np.array(df['LOW'])
            
            # ابدأ من أول نقطة
            prev_extreme = {'type': 'unknown', 'price': df['CLOSE'].iloc[0], 'index': df.index[0], 'position': 0}
            direction = None
            
            for i in range(1, len(df)):
                # حدد الاتجاه الأولي
                if direction is None:
                    if df['CLOSE'].iloc[i] > prev_extreme['price']:
                        direction = 'up'
                    else:
                        direction = 'down'
                
                # ابحث عن القمم والقيعان المحتملة بناء على التغير بالنسبة المئوية
                if direction == 'up':
                    # ابحث عن قمة محتملة
                    if df['HIGH'].iloc[i] > prev_extreme['price']:
                        prev_extreme = {'type': 'high', 'price': df['HIGH'].iloc[i], 'index': df.index[i], 'position': i}
                    # تحقق إذا كان هناك انعكاس هبوطي كبير
                    elif (prev_extreme['price'] - df['LOW'].iloc[i]) / prev_extreme['price'] > change_threshold:
                        if prev_extreme['type'] == 'high':
                            swing_points.append(prev_extreme)
                        prev_extreme = {'type': 'low', 'price': df['LOW'].iloc[i], 'index': df.index[i], 'position': i}
                        direction = 'down'
                
                else:  # direction == 'down'
                    # ابحث عن قاع محتمل
                    if df['LOW'].iloc[i] < prev_extreme['price']:
                        prev_extreme = {'type': 'low', 'price': df['LOW'].iloc[i], 'index': df.index[i], 'position': i}
                    # تحقق إذا كان هناك انعكاس صعودي كبير
                    elif (df['HIGH'].iloc[i] - prev_extreme['price']) / prev_extreme['price'] > change_threshold:
                        if prev_extreme['type'] == 'low':
                            swing_points.append(prev_extreme)
                        prev_extreme = {'type': 'high', 'price': df['HIGH'].iloc[i], 'index': df.index[i], 'position': i}
                        direction = 'up'
            
            # أضف آخر نقطة تأرجح
            if prev_extreme['type'] != 'unknown' and prev_extreme not in swing_points:
                swing_points.append(prev_extreme)
                
        elif method == 'advanced':
            # طريقة متقدمة تستخدم الذروات المحلية مع تصفية أفضل
            # استخدام scipy.signal.find_peaks للعثور على القمم والقيعان
            high_values = df['HIGH'].values
            low_values = df['LOW'].values
            
            # ابحث عن القمم
            high_peaks, _ = find_peaks(high_values, distance=threshold, prominence=np.std(high_values)*0.5)
            # ابحث عن القيعان (عن طريق عكس القيم)
            low_peaks, _ = find_peaks(-low_values, distance=threshold, prominence=np.std(low_values)*0.5)
            
            # أضف القمم
            for peak in high_peaks:
                swing_points.append({
                    'type': 'high',
                    'price': df['HIGH'].iloc[peak],
                    'index': df.index[peak],
                    'position': peak
                })
            
            # أضف القيعان
            for peak in low_peaks:
                swing_points.append({
                    'type': 'low',
                    'price': df['LOW'].iloc[peak],
                    'index': df.index[peak],
                    'position': peak
                })
            
            # رتّب نقاط التأرجح حسب الموضع
            swing_points.sort(key=lambda x: x['position'])
        
        return swing_points
    
    def _classify_wave_degree(self, wave_length, price_range):
        """
        تصنيف درجة الموجة بناء على طولها ونطاق السعر
        
        Args:
            wave_length: طول الموجة بالشموع
            price_range: نطاق السعر في الموجة
            
        Returns:
            str: درجة الموجة
        """
        # تصنيف تقريبي لدرجة الموجة بناء على طولها
        if wave_length > 1000:
            return "Grand Supercycle"
        elif wave_length > 500:
            return "Supercycle"
        elif wave_length > 200:
            return "Cycle"
        elif wave_length > 100:
            return "Primary"
        elif wave_length > 50:
            return "Intermediate"
        elif wave_length > 20:
            return "Minor"
        elif wave_length > 10:
            return "Minute"
        elif wave_length > 5:
            return "Minuette"
        else:
            return "Subminuette"
    
    def _calculate_waveform_quality(self, waves, direction):
        """
        حساب جودة شكل الموجات بناءً على القواعد النموذجية لموجات إليوت
        
        Args:
            waves: قائمة بالموجات
            direction: اتجاه الموجات ('صعودي' أو 'هبوطي')
            
        Returns:
            float: درجة جودة شكل الموجات (0.0 إلى 1.0)
        """
        quality_score = 0.0
        rules_passed = 0
        total_rules = 6
        
        # استخراج أسعار الموجات
        w1_price = waves[0]['price'] if len(waves) > 0 else None
        w2_price = waves[1]['price'] if len(waves) > 1 else None
        w3_price = waves[2]['price'] if len(waves) > 2 else None
        w4_price = waves[3]['price'] if len(waves) > 3 else None
        w5_price = waves[4]['price'] if len(waves) > 4 else None
        
        if w1_price is None or w2_price is None or w3_price is None:
            return 0.0  # لا يمكن تقييم الجودة بدون الموجات الثلاث الأولى على الأقل
        
        # القاعدة 1: الموجة 2 لا تتجاوز بداية الموجة 1
        if direction == 'صعودي':
            if w2_price > w1_price:
                rules_passed += 1
        else:  # هبوطي
            if w2_price < w1_price:
                rules_passed += 1
        
        # القاعدة 2: نسب تصحيح الموجة 2 يجب أن تكون في النطاق الفيبوناتشي المثالي
        retracement_2 = abs((w2_price - w1_price) / (w1_price * 0.01))
        expected_retrace_2 = [p * 100 for p in self.fibonacci_ratios['wave2_retracement']]
        if any(abs(retracement_2 - r) < 10 for r in expected_retrace_2):  # ضمن 10% من النسب المثالية
            rules_passed += 1
        
        # القاعدة 3: الموجة 3 يجب أن تكون الأطول في معظم الحالات
        if w4_price is not None:
            wave1_length = abs(w1_price - w2_price)
            wave3_length = abs(w2_price - w3_price)
            if wave3_length > wave1_length:
                rules_passed += 1
        
        # القاعدة 4: الموجة 4 لا تتداخل مع نطاق سعر الموجة 1
        if w4_price is not None:
            if direction == 'صعودي' and w4_price > w1_price:
                rules_passed += 1
            elif direction == 'هبوطي' and w4_price < w1_price:
                rules_passed += 1
        
        # القاعدة 5: نسب تصحيح الموجة 4 يجب أن تكون في النطاق الفيبوناتشي المثالي
        if w4_price is not None:
            wave3_length = abs(w3_price - w2_price)
            retracement_4 = abs((w4_price - w3_price) / wave3_length)
            expected_retrace_4 = self.fibonacci_ratios['wave4_retracement']
            if any(abs(retracement_4 - r) < 0.1 for r in expected_retrace_4):  # ضمن 10% من النسب المثالية
                rules_passed += 1
        
        # القاعدة 6: تناوب في هيكل الموجات 2 و 4 (إذا كانت إحداهما حادة، تكون الأخرى مسطحة)
        if w4_price is not None:
            wave2_duration = waves[1]['position'] - waves[0]['position']
            wave4_duration = waves[3]['position'] - waves[2]['position']
            wave2_price_range = abs(w2_price - w1_price)
            wave4_price_range = abs(w4_price - w3_price)
            
            wave2_steepness = wave2_price_range / wave2_duration
            wave4_steepness = wave4_price_range / wave4_duration
            
            if (wave2_steepness > wave4_steepness * 1.5) or (wave4_steepness > wave2_steepness * 1.5):
                rules_passed += 1
        
        # حساب النتيجة النهائية بناءً على القواعد التي تم اجتيازها
        applicable_rules = total_rules
        if w4_price is None:
            applicable_rules -= 3  # تجاهل القواعد 4-6 إذا لم تكن الموجة 4 متوفرة
        
        quality_score = rules_passed / applicable_rules if applicable_rules > 0 else 0
        return quality_score
    
    def _validate_corrective_pattern(self, waves, pattern_type):
        """
        التحقق من صحة نمط التصحيح (مثل Flat, Zigzag, Triangle)
        
        Args:
            waves: قائمة بموجات التصحيح
            pattern_type: نوع نمط التصحيح
            
        Returns:
            bool: ما إذا كان النمط صالحًا أم لا
            float: درجة صحة النمط (0.0 إلى 1.0)
        """
        if len(waves) < 3:
            return False, 0.0
        
        valid_pattern = False
        pattern_score = 0.0
        
        a_price = waves[0]['price']
        b_price = waves[1]['price']
        c_price = waves[2]['price']
        
        if pattern_type == 'flat':
            # في نمط Flat، الموجة B تصحح تقريبًا 100% من الموجة A والموجة C تساوي A تقريبًا
            expected_b = a_price * self.fibonacci_ratios['abc_ratios']['flat']['b_to_a']
            expected_c = a_price * self.fibonacci_ratios['abc_ratios']['flat']['c_to_a']
            
            b_accuracy = 1.0 - min(abs(b_price - expected_b) / abs(expected_b), 1.0)
            c_accuracy = 1.0 - min(abs(c_price - expected_c) / abs(expected_c), 1.0)
            
            pattern_score = (b_accuracy + c_accuracy) / 2
            valid_pattern = pattern_score > 0.6
            
        elif pattern_type == 'zigzag':
            # في نمط Zigzag، الموجة B تصحح أقل من 100% من الموجة A والموجة C تمتد أبعد من A
            expected_b_max = a_price * 0.78  # تصحيح B يجب أن يكون أقل من 78.6% من A
            expected_c_min = a_price * 1.0   # الموجة C يجب أن تكون أطول من A
            
            if (b_price < expected_b_max and c_price > expected_c_min):
                valid_pattern = True
                pattern_score = 0.8
            else:
                pattern_score = 0.4
                
        elif pattern_type == 'triangle':
            # نحتاج 5 موجات على الأقل للمثلث
            if len(waves) < 5:
                return False, 0.0
                
            # في نمط Triangle، الموجات تتناقص في النطاق تدريجيًا
            valid_convergence = True
            for i in range(2, len(waves)-1, 2):
                if abs(waves[i]['price'] - waves[i+1]['price']) >= abs(waves[i-2]['price'] - waves[i-1]['price']):
                    valid_convergence = False
                    break
            
            valid_pattern = valid_convergence
            pattern_score = 0.7 if valid_convergence else 0.3
        
        return valid_pattern, pattern_score
    
    def _calculate_wave_projections(self, waves, direction):
        """
        حساب التوقعات المستقبلية للموجات بناء على نسب فيبوناتشي
        
        Args:
            waves: قائمة بالموجات
            direction: اتجاه الموجات ('صعودي' أو 'هبوطي')
            
        Returns:
            dict: قاموس بالتوقعات المختلفة
        """
        projections = {}
        
        if len(waves) < 3:
            return projections
            
        # استخراج آخر سعر معروف
        last_price = waves[-1]['price']
        
        if len(waves) >= 5:  # توقعات بعد الموجة 5
            w1_price = waves[0]['price']
            w2_price = waves[1]['price']
            w3_price = waves[2]['price']
            w4_price = waves[3]['price']
            w5_price = waves[4]['price']
            
            # توقعات تصحيح ABC بعد موجة دافعة
            if direction == 'صعودي':
                a_proj = w5_price * 0.764  # تصحيح 76.4% من الموجة 5
                b_proj = (w5_price + a_proj) / 2  # تصحيح موجة B حوالي 50% من A
                c_proj = a_proj - (w5_price - a_proj) * 0.618  # موجة C حوالي 61.8% من A
            else:
                a_proj = w5_price * 1.236  # امتداد 123.6% من الموجة 5
                b_proj = (w5_price + a_proj) / 2  # تصحيح موجة B حوالي 50% من A
                c_proj = a_proj + (a_proj - w5_price) * 0.618  # موجة C حوالي 61.8% من A
                
            projections['abc_correction'] = {
                'a_target': a_proj,
                'b_target': b_proj,
                'c_target': c_proj
            }
            
        elif len(waves) == 3:  # توقعات بعد موجة تصحيحية
            a_price = waves[0]['price']
            b_price = waves[1]['price']
            c_price = waves[2]['price']
            
            # توقعات موجة دافعة بعد تصحيح
            if direction == 'هبوطي':  # تصحيح هبوطي، توقع موجة صعودية
                wave1_proj = c_price * 1.236
                wave3_proj = wave1_proj * 1.618
                wave5_proj = wave3_proj * 1.0
            else:  # تصحيح صعودي، توقع موجة هبوطية
                wave1_proj = c_price * 0.764
                wave3_proj = wave1_proj * 0.618
                wave5_proj = wave3_proj * 1.0
                
            projections['impulse_wave'] = {
                'wave1': wave1_proj,
                'wave3': wave3_proj,
                'wave5': wave5_proj
            }
            
        # حساب مناطق الدعم والمقاومة بناء على مستويات فيبوناتشي
        if len(waves) >= 2:
            high = max(w['price'] for w in waves)
            low = min(w['price'] for w in waves)
            range_price = high - low
            
            projections['fib_levels'] = {
                '0': low,
                '0.236': low + range_price * 0.236,
                '0.382': low + range_price * 0.382,
                '0.5': low + range_price * 0.5,
                '0.618': low + range_price * 0.618,
                '0.786': low + range_price * 0.786,
                '1.0': high,
                '1.618': high + range_price * 0.618,
                '2.618': high + range_price * 1.618
            }
            
        return projections

    def identify_impulse_waves(self, df, window=120, detection_method='advanced', min_quality=0.5):
        """
        تحديد الموجات الدافعة (Impulse Waves) في بيانات الأسعار
        
        Args:
            df: إطار البيانات مع أسعار OHLC
            window: عدد الشموع للتحليل
            detection_method: طريقة اكتشاف نقاط التأرجح
            min_quality: الحد الأدنى لجودة النمط
            
        Returns:
            list: قائمة بأنماط الموجات الدافعة المكتشفة
        """
        if len(df) < window:
            return []
            
        # استخدام الفترة الأخيرة من البيانات
        df_window = df.iloc[-window:].copy()
        
        # العثور على نقاط التأرجح
        swing_points = self._find_swing_points(df_window, method=detection_method)
        
        # لا يمكن تحديد الموجات إذا لم يكن هناك نقاط تأرجح كافية
        if len(swing_points) < 5:
            logger.debug("عدد نقاط التأرجح غير كافٍ لتحديد الموجات الدافعة")
            return []
            
        # البحث عن أنماط موجات دافعة
        potential_patterns = []
        
        for i in range(len(swing_points) - 4):
            wave1_type = swing_points[i]['type']
            wave2_type = swing_points[i+1]['type']
            
            # الموجة 1 والموجة 2 يجب أن تكونا في اتجاهين متعاكسين
            if wave1_type == wave2_type:
                continue
                
            wave3_type = swing_points[i+2]['type']
            
            # الموجة 3 يجب أن تكون في نفس اتجاه الموجة 1
            if wave1_type != wave3_type:
                continue
                
            wave4_type = swing_points[i+3]['type']
            
            # الموجة 4 يجب أن تكون في نفس اتجاه الموجة 2
            if wave2_type != wave4_type:
                continue
                
            wave5_type = swing_points[i+4]['type']
            
            # الموجة 5 يجب أن تكون في نفس اتجاه الموجة 1 و3
            if wave1_type != wave5_type:
                continue
                
            # استخراج بيانات الموجات
            waves = [
                {'wave': 1, 'type': wave1_type, 'price': swing_points[i]['price'], 
                 'index': swing_points[i]['index'], 'position': swing_points[i]['position']},
                {'wave': 2, 'type': wave2_type, 'price': swing_points[i+1]['price'], 
                 'index': swing_points[i+1]['index'], 'position': swing_points[i+1]['position']},
                {'wave': 3, 'type': wave3_type, 'price': swing_points[i+2]['price'], 
                 'index': swing_points[i+2]['index'], 'position': swing_points[i+2]['position']},
                {'wave': 4, 'type': wave4_type, 'price': swing_points[i+3]['price'], 
                 'index': swing_points[i+3]['index'], 'position': swing_points[i+3]['position']},
                {'wave': 5, 'type': wave5_type, 'price': swing_points[i+4]['price'], 
                 'index': swing_points[i+4]['index'], 'position': swing_points[i+4]['position']}
            ]
            
            # تحديد اتجاه النمط
            direction = "صعودي" if wave1_type == "low" else "هبوطي"
            
            # حساب جودة النمط بناءً على قواعد موجات إليوت
            pattern_quality = self._calculate_waveform_quality(waves, direction)
            
            if pattern_quality >= min_quality:
                # حساب نسب الموجات
                w1_price = waves[0]['price']
                w2_price = waves[1]['price']
                w3_price = waves[2]['price']
                w4_price = waves[3]['price']
                w5_price = waves[4]['price']
                
                # حساب نسب التصحيح والامتداد
                wave2_retracement = abs((w2_price - w1_price) / w1_price) * 100
                wave3_extension = abs((w3_price - w2_price) / abs(w1_price - w2_price))
                wave4_retracement = abs((w4_price - w3_price) / abs(w3_price - w2_price)) * 100
                wave5_extension = abs((w5_price - w4_price) / abs(w1_price - w2_price))
                
                # تحديد درجة الموجة بناء على طولها
                wave_length = waves[4]['position'] - waves[0]['position']
                price_range = max(df_window['HIGH']) - min(df_window['LOW'])
                wave_degree = self._classify_wave_degree(wave_length, price_range)
                
                # حساب التوقعات بناء على الموجات المكتشفة
                projections = self._calculate_wave_projections(waves, direction)
                
                # إضافة النمط المكتشف
                potential_patterns.append({
                    'pattern': 'ELLIOTT_IMPULSE',
                    'direction': direction,
                    'degree': wave_degree,
                    'start_index': waves[0]['index'],
                    'end_index': waves[4]['index'],
                    'waves': waves,
                    'quality': pattern_quality,
                    'confidence': pattern_quality * 100,  # تحويل إلى نسبة مئوية
                    'metrics': {
                        'wave2_retracement': f"{wave2_retracement:.2f}%",
                        'wave3_extension': f"{wave3_extension:.2f}x",
                        'wave4_retracement': f"{wave4_retracement:.2f}%",
                        'wave5_extension': f"{wave5_extension:.2f}x"
                    },
                    'projections': projections
                })
                
        # ترتيب الأنماط حسب الجودة
        potential_patterns.sort(key=lambda x: x['quality'], reverse=True)
        
        return potential_patterns

    def identify_corrective_patterns(self, df, window=60, detection_method='advanced', min_quality=0.5):
        """
        تحديد أنماط التصحيح (Corrective Patterns) في بيانات الأسعار
        
        Args:
            df: إطار البيانات مع أسعار OHLC
            window: عدد الشموع للتحليل
            detection_method: طريقة اكتشاف نقاط التأرجح
            min_quality: الحد الأدنى لجودة النمط
            
        Returns:
            list: قائمة بأنماط التصحيح المكتشفة
        """
        if len(df) < window:
            return []
            
        # استخدام الفترة الأخيرة من البيانات
        df_window = df.iloc[-window:].copy()
        
        # العثور على نقاط التأرجح
        swing_points = self._find_swing_points(df_window, method=detection_method)
        
        # لا يمكن تحديد الموجات إذا لم يكن هناك نقاط تأرجح كافية
        if len(swing_points) < 3:
            logger.debug("عدد نقاط التأرجح غير كافٍ لتحديد أنماط التصحيح")
            return []
            
        # تحديد أنماط التصحيح المختلفة
        correction_patterns = []
        
        # البحث عن نمط Zigzag (A-B-C)
        for i in range(len(swing_points) - 2):
            wave_a_type = swing_points[i]['type']
            wave_b_type = swing_points[i+1]['type']
            
            # الموجة A والموجة B يجب أن تكونا في اتجاهين متعاكسين
            if wave_a_type == wave_b_type:
                continue
                
            wave_c_type = swing_points[i+2]['type']
            
            # الموجة C يجب أن تكون في نفس اتجاه الموجة A
            if wave_a_type != wave_c_type:
                continue
                
            # استخراج بيانات الموجات
            waves = [
                {'wave': 'A', 'type': wave_a_type, 'price': swing_points[i]['price'], 
                 'index': swing_points[i]['index'], 'position': swing_points[i]['position']},
                {'wave': 'B', 'type': wave_b_type, 'price': swing_points[i+1]['price'], 
                 'index': swing_points[i+1]['index'], 'position': swing_points[i+1]['position']},
                {'wave': 'C', 'type': wave_c_type, 'price': swing_points[i+2]['price'], 
                 'index': swing_points[i+2]['index'], 'position': swing_points[i+2]['position']}
            ]
            
            # تحديد اتجاه النمط
            direction = "هبوطي" if wave_a_type == "high" else "صعودي"
            
            wave_a_price = swing_points[i]['price']
            wave_b_price = swing_points[i+1]['price']
            wave_c_price = swing_points[i+2]['price']
            
            # حساب نسب الموجات
            wave_b_retracement_pct = (wave_b_price - wave_a_price) / abs(wave_a_price) * 100
            wave_c_extension = abs((wave_c_price - wave_b_price) / abs(wave_b_price - wave_a_price))
            
            # تحديد نوع التصحيح بناء على نسب الموجات
            correction_type = ""
            pattern_quality = 0.0
            
            # تحقق من نمط Zigzag
            zigzag_valid, zigzag_score = self._validate_corrective_pattern(waves, 'zigzag')
            if zigzag_valid and zigzag_score >= min_quality:
                correction_type = "Zigzag"
                pattern_quality = zigzag_score
            
            # تحقق من نمط Flat
            flat_valid, flat_score = self._validate_corrective_pattern(waves, 'flat')
            if flat_valid and flat_score > pattern_quality:
                correction_type = "Flat"
                pattern_quality = flat_score
            
            # إذا لم يتم تحديد نوع محدد، فقم بتصنيفه كتصحيح عام
            if not correction_type and pattern_quality < min_quality:
                correction_type = "تصحيح غير قياسي"
                pattern_quality = 0.5  # درجة ثقة متوسطة
            
            if pattern_quality >= min_quality:
                # تحديد درجة الموجة بناء على طولها
                wave_length = waves[2]['position'] - waves[0]['position']
                price_range = max(df_window['HIGH']) - min(df_window['LOW'])
                wave_degree = self._classify_wave_degree(wave_length, price_range)
                
                # حساب التوقعات بناء على الموجات المكتشفة
                projections = self._calculate_wave_projections(waves, direction)
                
                # إضافة نمط التصحيح المكتشف
                correction_patterns.append({
                    'pattern': 'ELLIOTT_CORRECTION',
                    'type': correction_type,
                    'direction': direction,
                    'degree': wave_degree,
                    'start_index': waves[0]['index'],
                    'end_index': waves[2]['index'],
                    'waves': waves,
                    'quality': pattern_quality,
                    'confidence': pattern_quality * 100,  # تحويل إلى نسبة مئوية
                    'metrics': {
                        'wave_b_retracement': f"{wave_b_retracement_pct:.2f}%",
                        'wave_c_extension': f"{wave_c_extension:.2f}x"
                    },
                    'projections': projections
                })
                
        # البحث عن نمط Triangle (A-B-C-D-E)
        if len(swing_points) >= 5:
            # تنفيذ اكتشاف المثلث هنا...
            # المثلثات تحتاج إلى 5 نقاط تأرجح على الأقل (A-B-C-D-E)
            for i in range(len(swing_points) - 4):
                # استخراج بيانات الموجات الخمس المحتملة
                waves = [
                    {'wave': 'A', 'type': swing_points[i]['type'], 'price': swing_points[i]['price'], 
                     'index': swing_points[i]['index'], 'position': swing_points[i]['position']},
                    {'wave': 'B', 'type': swing_points[i+1]['type'], 'price': swing_points[i+1]['price'], 
                     'index': swing_points[i+1]['index'], 'position': swing_points[i+1]['position']},
                    {'wave': 'C', 'type': swing_points[i+2]['type'], 'price': swing_points[i+2]['price'], 
                     'index': swing_points[i+2]['index'], 'position': swing_points[i+2]['position']},
                    {'wave': 'D', 'type': swing_points[i+3]['type'], 'price': swing_points[i+3]['price'], 
                     'index': swing_points[i+3]['index'], 'position': swing_points[i+3]['position']},
                    {'wave': 'E', 'type': swing_points[i+4]['type'], 'price': swing_points[i+4]['price'], 
                     'index': swing_points[i+4]['index'], 'position': swing_points[i+4]['position']}
                ]
                
                # تحقق من نمط المثلث
                triangle_valid, triangle_score = self._validate_corrective_pattern(waves, 'triangle')
                
                if triangle_valid and triangle_score >= min_quality:
                    # تحديد نوع المثلث (متماثل، صاعد، هابط، متسع)
                    triangle_type = "مثلث متماثل"  # افتراضي
                    
                    # تحليل اتجاه القمم والقيعان لتحديد نوع المثلث
                    tops_trend = "flat"
                    bottoms_trend = "flat"
                    
                    # وضع المنطق هنا لتحديد اتجاهات المثلث...
                    
                    # تحديد اتجاه النمط
                    direction = "محايد"  # معظم المثلثات استمرارية
                    
                    # تحديد درجة الموجة
                    wave_length = waves[4]['position'] - waves[0]['position']
                    price_range = max(df_window['HIGH']) - min(df_window['LOW'])
                    wave_degree = self._classify_wave_degree(wave_length, price_range)
                    
                    # إضافة نمط المثلث المكتشف
                    correction_patterns.append({
                        'pattern': 'ELLIOTT_TRIANGLE',
                        'type': triangle_type,
                        'direction': direction,
                        'degree': wave_degree,
                        'start_index': waves[0]['index'],
                        'end_index': waves[4]['index'],
                        'waves': waves,
                        'quality': triangle_score,
                        'confidence': triangle_score * 100,  # تحويل إلى نسبة مئوية
                        'projections': self._calculate_wave_projections(waves, direction)
                    })
            
        # ترتيب أنماط التصحيح حسب الجودة
        correction_patterns.sort(key=lambda x: x['quality'], reverse=True)
        
        return correction_patterns
        
    def analyze_elliott_wave_structure(self, df, detection_method='advanced', timeframes=['short', 'medium', 'long']):
        """
        تحليل شامل لهيكل موجات إليوت عبر إطارات زمنية متعددة
        
        Args:
            df: إطار البيانات مع أسعار OHLC
            detection_method: طريقة اكتشاف نقاط التأرجح
            timeframes: الإطارات الزمنية للتحليل
            
        Returns:
            dict: تحليل شامل لهيكل موجات إليوت
        """
        if len(df) < 200:
            return {'error': 'البيانات غير كافية للتحليل الشامل لموجات إليوت'}
        
        analysis_results = {
            'overall_trend': 'غير محدد',
            'current_structure': 'غير محدد',
            'wave_counts': {},
            'signals': {},
            'projections': {},
            'confidence': 0.0
        }
        
        # تحديد الاتجاه العام بناء على المتوسط المتحرك
        try:
            ma50 = talib.SMA(df['CLOSE'].values, timeperiod=50)
            ma200 = talib.SMA(df['CLOSE'].values, timeperiod=200)
            
            if ma50[-1] > ma200[-1]:
                analysis_results['overall_trend'] = "صعودي"
            elif ma50[-1] < ma200[-1]:
                analysis_results['overall_trend'] = "هبوطي"
            else:
                analysis_results['overall_trend'] = "محايد"
        except Exception as e:
            logger.error(f"خطأ في حساب المتوسطات المتحركة: {e}")
            analysis_results['overall_trend'] = "غير محدد"
        
        # تحليل على إطارات زمنية متعددة
        time_windows = {
            'short': min(60, len(df)),
            'medium': min(120, len(df)),
            'long': min(200, len(df))
        }
        
        all_patterns = []
        
        for tf in timeframes:
            window = time_windows.get(tf, 120)
            
            # تحديد الموجات الدافعة
            impulse_patterns = self.identify_impulse_waves(df, window=window, detection_method=detection_method)
            
            # تحديد أنماط التصحيح
            correction_patterns = self.identify_corrective_patterns(df, window=window, detection_method=detection_method)
            
            # تخزين النتائج لهذا الإطار الزمني
            analysis_results['wave_counts'][tf] = {
                'impulse_patterns': len(impulse_patterns),
                'correction_patterns': len(correction_patterns),
                'latest_pattern': None
            }
            
            # تحديد أحدث نمط مكتشف
            latest_pattern = None
            latest_end_idx = 0
            
            if impulse_patterns:
                latest_impulse = impulse_patterns[0]
                latest_impulse_end_idx = df.index.get_loc(latest_impulse['end_index'])
                
                if latest_impulse_end_idx > latest_end_idx:
                    latest_pattern = latest_impulse
                    latest_end_idx = latest_impulse_end_idx
            
            if correction_patterns:
                latest_correction = correction_patterns[0]
                latest_correction_end_idx = df.index.get_loc(latest_correction['end_index'])
                
                if latest_correction_end_idx > latest_end_idx:
                    latest_pattern = latest_correction
                    latest_end_idx = latest_correction_end_idx
            
            # تخزين أحدث نمط
            if latest_pattern:
                pattern_type = "موجة دافعة" if latest_pattern.get('pattern') == 'ELLIOTT_IMPULSE' else "موجة تصحيحية"
                
                analysis_results['wave_counts'][tf]['latest_pattern'] = {
                    'type': pattern_type,
                    'direction': latest_pattern.get('direction', 'غير محدد'),
                    'confidence': latest_pattern.get('confidence', 0.0),
                    'degree': latest_pattern.get('degree', 'غير محدد'),
                    'details': latest_pattern
                }
                
                all_patterns.append({
                    'timeframe': tf,
                    'pattern': latest_pattern,
                    'end_idx': latest_end_idx
                })
        
        # تحديد هيكل الموجة الحالي على جميع الإطارات الزمنية
        if all_patterns:
            # ترتيب الأنماط حسب حداثتها
            all_patterns.sort(key=lambda x: x['end_idx'], reverse=True)
            latest_overall_pattern = all_patterns[0]['pattern']
            
            if latest_overall_pattern.get('pattern') == 'ELLIOTT_IMPULSE':
                # أنهينا للتو موجة دافعة، لذا نتوقع تصحيحًا
                current_structure = f"اكتمال موجة دافعة {latest_overall_pattern.get('degree', '')} {latest_overall_pattern.get('direction', 'غير محدد')}"
                analysis_results['current_structure'] = current_structure
                
                # إشارة محتملة
                if latest_overall_pattern.get('direction') == "صعودي":
                    analysis_results['signals']['action'] = "بيع محتمل"
                    analysis_results['signals']['bias'] = "هبوطي قصير المدى"
                else:
                    analysis_results['signals']['action'] = "شراء محتمل"
                    analysis_results['signals']['bias'] = "صعودي قصير المدى"
                    
            elif latest_overall_pattern.get('pattern') == 'ELLIOTT_CORRECTION':
                # أنهينا للتو موجة تصحيحية، لذا نتوقع موجة دافعة جديدة
                current_structure = f"اكتمال تصحيح {latest_overall_pattern.get('type', '')} {latest_overall_pattern.get('degree', '')} {latest_overall_pattern.get('direction', 'غير محدد')}"
                analysis_results['current_structure'] = current_structure
                
                # إشارة محتملة
                if latest_overall_pattern.get('direction') == "صعودي":
                    analysis_results['signals']['action'] = "شراء محتمل"
                    analysis_results['signals']['bias'] = "صعودي متوسط المدى"
                else:
                    analysis_results['signals']['action'] = "بيع محتمل"
                    analysis_results['signals']['bias'] = "هبوطي متوسط المدى"
                
            # نسخ التوقعات من أحدث نمط
            if 'projections' in latest_overall_pattern:
                analysis_results['projections'] = latest_overall_pattern['projections']
                
            # تقدير الثقة الإجمالية
            analysis_results['confidence'] = latest_overall_pattern.get('confidence', 0.0)
            
        # حساب مستويات فيبوناتشي للإطار الزمني الكامل
        high = df['HIGH'].max()
        low = df['LOW'].min()
        range_price = high - low
        
        analysis_results['fibonacci_levels'] = {
            '0': low,
            '0.236': low + range_price * 0.236,
            '0.382': low + range_price * 0.382,
            '0.5': low + range_price * 0.5,
            '0.618': low + range_price * 0.618,
            '0.786': low + range_price * 0.786,
            '1.0': high
        }
        
        return analysis_results