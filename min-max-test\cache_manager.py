import redis
import json
import time
import logging
from functools import wraps
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

try:
    # Try to connect to Redis
    redis_client = redis.Redis(host='localhost', port=6379, db=0)
    redis_client.ping()  # Test the connection
    REDIS_AVAILABLE = True
except:
    logger.warning("Redis not available, falling back to in-memory cache")
    REDIS_AVAILABLE = False
    # Fallback in-memory cache
    in_memory_cache = {}

def cache_result(expiry=3600):
    """Cache function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Create a cache key from function name and arguments
            key = f"{func.__name__}:{str(args)}:{str(kwargs)}"
            
            # Try to get from cache
            if REDIS_AVAILABLE:
                cached_value = redis_client.get(key)
                if cached_value:
                    return json.loads(cached_value)
            else:
                if key in in_memory_cache and in_memory_cache[key]['expiry'] > time.time():
                    return in_memory_cache[key]['value']
            
            # Not in cache, compute the result
            result = func(*args, **kwargs)
            
            # Store in cache
            if REDIS_AVAILABLE:
                redis_client.setex(key, expiry, json.dumps(result))
            else:
                in_memory_cache[key] = {
                    'value': result,
                    'expiry': time.time() + expiry
                }
                
            return result
        return wrapper
    return decorator
