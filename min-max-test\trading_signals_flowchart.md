# Trading Signals Logic Flowchart

## نظرة عامة على منطق إشارات التداول

هذا التدفق يوضح كيفية معالجة النظام لإشارات التداول المختلفة وتحديد الإجراءات المناسبة لكل حالة.

```mermaid
flowchart TD
    A[📨 استقبال إشارة تداول جديدة] --> B{📋 نوع الإشارة؟}
      %% Buy Signal Flow
    B -->|Buy| C[🔍 البحث عن رمز السهم في Sheet4]
    C --> D{🗃️ السهم موجود في Sheet4؟}
    
    %% Existing Stock - Buy Signal
    D -->|نعم| E[✅ تحقق من وجود صفقة مفتوحة]
    E --> F{💼 توجد صفقة مفتوحة؟}
    F -->|نعم| G1[🔍 مقارنة أسعار الشراء]
    G1 --> G2{💰 السعر الجديد أقل؟}
    G2 -->|نعم| G3[📈 إشارة تعزيز مراكز]
    G3 --> G4[💾 تحديث سعر الشراء والأهداف]
    G4 --> G5[📧 إرسال رسالة تعزيز للإدارة والأعضاء]
    G2 -->|لا| G[⚠️ إرسال تنبيه للإدارة: إشارة مكررة]
    F -->|لا| H0[📊 تحليل المؤشرات الفنية]
    H0 --> H1{⚠️ مؤشرات خطر؟}
    H1 -->|نعم| H2[🚨 رفع مستوى المخاطرة]
    H1 -->|لا| H3[📊 مستوى مخاطرة عادي]
    H2 --> H[💾 تحديث بيانات السهم]
    H3 --> H[💾 تحديث بيانات السهم]
    H --> I[📊 حساب المقاييس والمخاطر]
    I --> J[📢 إرسال توصية شراء للمستخدمين]
      %% New Stock - Buy Signal
    D -->|لا| K[🔍 البحث عن السهم في Sheet1]
    K --> L{📑 السهم موجود في Sheet1؟}
    L -->|نعم| M[✅ تحقق من وجود صفقة مفتوحة]
    M --> N{💼 توجد صفقة مفتوحة؟}
    N -->|نعم| N1[🔍 مقارنة أسعار الشراء]
    N1 --> N2{💰 السعر الجديد أقل؟}
    N2 -->|نعم| N3[📈 إشارة تعزيز مراكز]
    N3 --> N4[💾 تحديث سعر الشراء والأهداف]
    N4 --> N5[📧 إرسال رسالة تعزيز للإدارة والأعضاء]
    N2 -->|لا| O[⚠️ إرسال تنبيه للإدارة: إشارة مكررة]
    N -->|لا| P0[📊 تحليل المؤشرات الفنية]
    P0 --> P1{⚠️ مؤشرات خطر؟}
    P1 -->|نعم| P2[🚨 رفع مستوى المخاطرة]
    P1 -->|لا| P3[📊 مستوى مخاطرة عادي]
    P2 --> P[➕ إضافة سجل جديد في Sheet4]
    P3 --> P[➕ إضافة سجل جديد في Sheet4]
    P --> Q[📊 حساب المقاييس والمخاطر]
    Q --> R[📢 إرسال توصية شراء للمستخدمين]
    
    L -->|لا| S[❓ إضافة السهم بدون إرسال توصيات]
    S --> T[📧 إرسال تنبيه للإدارة: سهم جديد غير مدرج]
    
    %% Target 1 Achieved Flow
    B -->|T1Done| U[🎯 الهدف الأول محقق]
    U --> V[💾 تحديث حالة السهم: t1done]
    V --> W[🔒 رفع مستوى وقف الخسارة]
    W --> X[📊 حساب نسبة الربح والوقت المستغرق]
    X --> Y[📢 إرسال إشعار تحقق الهدف الأول]
    
    %% Target 2 Achieved Flow
    B -->|T2Done| Z[🎯 الهدف الثاني محقق]
    Z --> AA[💾 تحديث حالة السهم: t2done]
    AA --> BB[🔒 رفع مستوى وقف الخسارة]
    BB --> CC[📊 حساب نسبة الربح والوقت المستغرق]
    CC --> DD[📢 إرسال إشعار تحقق الهدف الثاني]
    
    %% Target 3 Achieved Flow
    B -->|T3Done| EE[🏆 الهدف الثالث محقق]
    EE --> FF[💾 تحديث حالة السهم: t3done]
    FF --> GG[🔒 رفع مستوى وقف الخسارة]
    GG --> HH[📊 حساب نسبة الربح والوقت المستغرق]
    HH --> II[🎉 إرسال إشعار تحقق جميع الأهداف]    %% Stop Loss Flow
    B -->|TSL| JJ[🛑 إشارة وقف خسارة]
    JJ --> KK[🔍 تحقق من وجود صفقة مفتوحة]
    KK --> LL{💼 توجد صفقة مفتوحة؟}
    LL -->|لا| MM[⚠️ إرسال تنبيه للإدارة: لا توجد صفقة]
    LL -->|نعم| NN[💾 تحديث حالة السهم: tsl]
    NN --> OO[🔍 تحقق من الأهداف المحققة]
    OO --> PP{✅ توجد أهداف محققة؟}
    PP -->|نعم| PP1[🎯 استخراج سعر وتاريخ آخر هدف محقق]
    PP1 --> PP2[📊 البحث في البيانات التاريخية للفترة]
    PP2 --> PP3{📈 توجد بيانات تاريخية؟}
    PP3 -->|نعم| PP4[💰 حساب الربح على أعلى سعر تاريخي]
    PP3 -->|لا| PP5[💰 حساب الربح على سعر آخر هدف محقق]
    PP4 --> QQ[📢 إرسال إشعار حماية الأرباح مع التفاصيل]
    PP5 --> QQ
    PP -->|لا| RR[📢 إرسال إشعار تفعيل وقف الخسارة]
    
    %% Sell Signal Flow
    B -->|Sell| SS[💰 إشارة بيع]
    SS --> TT[🔍 تحقق من وجود صفقة مفتوحة]
    TT --> UU{💼 توجد صفقة مفتوحة؟}
    UU -->|لا| VV[⚠️ إرسال تنبيه للإدارة: لا توجد صفقة]
    UU -->|نعم| WW[💾 تحديث بيانات البيع]
    WW --> XX[🔍 تحقق من الأهداف المحققة]
    XX --> YY{✅ توجد أهداف محققة؟}
    YY -->|نعم| YY1[🎯 استخراج سعر وتاريخ آخر هدف محقق]
    YY1 --> YY2[📊 البحث في البيانات التاريخية للفترة]
    YY2 --> YY3{📈 توجد بيانات تاريخية؟}
    YY3 -->|نعم| YY4[💰 حساب الربح على أعلى سعر تاريخي]
    YY3 -->|لا| YY5[💰 حساب الربح على سعر آخر هدف محقق]
    YY4 --> ZZ[📢 إرسال إشعار حماية الأرباح مع التفاصيل]
    YY5 --> ZZ
    YY -->|لا| AAA[📢 إرسال تحذير بيع]
    
    %% Other Signals for New Stocks
    B -->|Other| BBB[❓ إشارة أخرى على سهم جديد]
    BBB --> CCC[⚠️ إرسال تنبيه للإدارة: إشارة مجهولة]
      %% End Points
    G --> DDD[🔚 انتهاء المعالجة]
    G5 --> DDD
    J --> DDD
    O --> DDD
    N5 --> DDD
    R --> DDD
    T --> DDD
    Y --> DDD
    DD --> DDD
    II --> DDD
    MM --> DDD
    QQ --> DDD
    RR --> DDD
    VV --> DDD
    ZZ --> DDD
    AAA --> DDD
    CCC --> DDD
      style A fill:#e1f5fe
    style DDD fill:#f3e5f5
    style G fill:#ffecb3
    style G5 fill:#c8e6c9
    style N5 fill:#c8e6c9
    style MM fill:#ffecb3
    style VV fill:#ffecb3
    style CCC fill:#ffecb3
    style J fill:#c8e6c9
    style R fill:#c8e6c9
    style Y fill:#c8e6c9
    style DD fill:#c8e6c9
    style II fill:#c8e6c9
    style QQ fill:#c8e6c9
    style RR fill:#ffcdd2
    style ZZ fill:#c8e6c9
    style AAA fill:#ffcdd2
    style H2 fill:#ff8a80
    style P2 fill:#ff8a80
```

## شرح مفصل لمنطق النظام

### 1. معالجة إشارات الشراء (Buy Signals)

#### أ) السهم موجود في Sheet4:
- ✅ **تحقق من الصفقات المفتوحة**: إذا كانت هناك صفقة مفتوحة، يتم تجاهل الإشارة وإرسال تنبيه للإدارة
- 📊 **معالجة الإشارة الجديدة**: تحديث البيانات وحساب المقاييس المالية وإرسال التوصية

#### ب) السهم غير موجود في Sheet4:
- 🔍 **البحث في Sheet1**: التحقق من وجود السهم في قاعدة البيانات الرئيسية
- ✅ **سهم موجود**: إضافة سجل جديد وإرسال التوصية
- ❌ **سهم غير موجود**: إضافة السهم بدون إرسال توصيات وإشعار الإدارة

### 2. معالجة تحقق الأهداف (Target Achievement)

#### الهدف الأول (T1Done):
- 📊 حساب نسبة الربح المحققة
- 🔒 رفع مستوى وقف الخسارة لحماية الأرباح
- 📢 إرسال إشعار للمستخدمين بتحقق الهدف

#### الهدف الثاني (T2Done):
- 📊 حساب الربح التراكمي
- 🔒 تحديث وقف الخسارة
- 📢 إشعار بتحقق الهدف الثاني

#### الهدف الثالث (T3Done):
- 🏆 إنجاز كامل للتوصية
- 📊 حساب إجمالي الأرباح
- 🎉 إشعار بتحقق جميع الأهداف

### 3. معالجة وقف الخسارة (Stop Loss)

#### التحقق من الصفقات المفتوحة:
- 💼 **صفقة مفتوحة موجودة**: تفعيل وقف الخسارة
- ❌ **لا توجد صفقة**: تجاهل الإشارة وإشعار الإدارة

#### نوع الرسالة:
- ✅ **أهداف محققة**: رسالة حماية الأرباح
- ❌ **لا توجد أهداف**: رسالة تحذير من الخسائر

### 4. معالجة إشارات البيع (Sell Signals)

#### التحقق من الصفقات:
- 💼 **صفقة مفتوحة**: معالجة إشارة البيع
- ❌ **لا توجد صفقة**: تجاهل وإشعار الإدارة

#### نوع رسالة البيع:
- ✅ **أهداف محققة**: رسالة حماية الأرباح
- ⚠️ **لا توجد أهداف**: تحذير بيع احترازي

### 5. آليات الحماية والتحقق

#### منع الإشارات المكررة:
- 🔍 التحقق من وجود صفقات مفتوحة قبل المعالجة
- ⚠️ إشعار الإدارة بالإشارات المكررة أو المشبوهة

#### إدارة الأخطاء:
- 📧 إشعار الإدارة بالأسهم غير المدرجة
- 🚫 تجاهل الإشارات على أسهم بدون صفقات مفتوحة

#### حماية المستخدمين:
- 🔒 إزالة المستخدمين الذين حظروا البوت تلقائياً
- 📊 إرسال إحصائيات مفصلة مع كل توصية

## المقاييس المحسوبة

### مقاييس المخاطرة والعائد:
- 📈 **نسبة الربح**: (سعر الهدف - سعر الشراء) / سعر الشراء × 100
- 📉 **نسبة الخسارة**: (سعر الشراء - وقف الخسارة) / سعر الشراء × 100
- ⚖️ **نسبة المخاطرة/العائد**: نسبة الربح / نسبة الخسارة
- ⏱️ **الوقت المتوقع**: تقدير تقريبي بناءً على نسبة الربح المستهدفة

### تقييم مستوى المخاطرة:
- 🟢 **منخفضة**: نسبة مخاطرة/عائد > 2
- 🟡 **متوسطة**: نسبة مخاطرة/عائد 1-2
- 🔴 **عالية**: نسبة مخاطرة/عائد < 1

## حساب الربح والخسارة في إشارات البيع ووقف الخسارة

### 🔢 **آليات الحساب المالي**

#### 📊 **1. في حالة إشارة البيع (Sell Signal)**

```python
# البيانات المطلوبة للحساب
buy_price = السعر المسجل في عمود 4 (سعر الشراء الأصلي)
sell_price = alert_message["sell_price"] (سعر البيع المقترح من الإشارة)
achieved_targets = الأهداف المحققة سابقاً (t1, t2, t3)

# حساب الربح/الخسارة الحالية
current_profit_pct = round((sell_price - buy_price) / buy_price * 100, 2)

# حساب الأرباح من الأهداف المحققة سابقاً
for target in achieved_targets:
    target_price = السعر المحقق للهدف
    profit_pct = round((target_price - buy_price) / buy_price * 100, 2)
```

#### 🛑 **2. في حالة وقف الخسارة (Stop Loss)**

```python
# البيانات المطلوبة
buy_price = السعر المسجل في عمود 4
sl_price = alert_message["sl"] (سعر وقف الخسارة)
achieved_targets = تحقق من الأهداف المحققة

# حساب الخسارة المتوقعة
loss_pct = round((sl_price - buy_price) / buy_price * 100, 2)

# تحديد نوع الرسالة حسب الأهداف المحققة
if has_achieved_targets:
    # رسالة حماية الأرباح - تم تحقيق أهداف سابقاً
    message_type = "profit_protection"
else:
    # رسالة تحذير خسارة - لم يتم تحقيق أهداف
    message_type = "loss_warning"
```

### 📈 **تفصيل آليات الحساب**

#### **أ) حساب نسبة الربح/الخسارة:**
```
نسبة التغيير = ((السعر الحالي - سعر الشراء) / سعر الشراء) × 100

مثال:
- سعر الشراء: 100 جنيه
- سعر البيع: 110 جنيه
- نسبة الربح = ((110 - 100) / 100) × 100 = 10%
```

#### **ب) حساب الأرباح المحققة من الأهداف السابقة:**
```python
profit_details = []
for target in ['t1', 't2', 't3']:
    if target in achieved_targets:
        target_price = target_details[f'tp{target[1]}']  # tp1, tp2, tp3
        profit_pct = ((target_price - buy_price) / buy_price) * 100
        profit_details.append(f"الهدف {target[1]}: {profit_pct}%")

profit_summary = " | ".join(profit_details)
```

#### **ج) تحديد نوع الإشعار:**

**في حالة البيع:**
- ✅ **أهداف محققة** → `💰 حماية الأرباح - إشارة بيع`
- ❌ **لا توجد أهداف** → `🔻 تحذير بيع`

**في حالة وقف الخسارة:**
- ✅ **أهداف محققة** → `🛡️ حماية الأرباح` (تم تفعيل وقف الخسارة لحماية الأرباح)
- ❌ **لا توجد أهداف** → `⚠️ تفعيل وقف الخسارة` (تحذير من الخسائر)

### 🗂️ **بنية البيانات في Google Sheets**

#### **Sheet4 - تتبع الصفقات:**
```
العمود 1: stock_code (رمز السهم)
العمود 2: stock_name (اسم السهم) 
العمود 3: status (حالة الصفقة)
العمود 4: buy_price (سعر الشراء)
العمود 5: buy_date (تاريخ الشراء)
العمود 6: sell_price (سعر البيع)
العمود 7: sell_date (تاريخ البيع)
العمود 8: tp1 (الهدف الأول)
العمود 9: tp2 (الهدف الثاني)
العمود 10: tp3 (الهدف الثالث)
العمود 11: sl (وقف الخسارة)
العمود 12: t1_date (تاريخ تحقق الهدف الأول)
العمود 13: t2_date (تاريخ تحقق الهدف الثاني)
العمود 14: t3_date (تاريخ تحقق الهدف الثالث)
العمود 15: tsl_date (تاريخ تفعيل وقف الخسارة)
```

### 💰 **أمثلة عملية للحساب**

#### **مثال 1: إشارة بيع مع أهداف محققة**
```
سعر الشراء: 50 جنيه
الهدف الأول المحقق: 55 جنيه (ربح 10%)
الهدف الثاني المحقق: 60 جنيه (ربح 20%)
سعر البيع المقترح: 58 جنيه

الحساب:
- الربح الحالي = ((58 - 50) / 50) × 100 = 16%
- الأهداف المحققة = "الهدف 1: 10% | الهدف 2: 20%"
- نوع الرسالة = "حماية الأرباح"
```

#### **مثال 2: وقف خسارة بدون أهداف محققة**
```
سعر الشراء: 100 جنيه
سعر وقف الخسارة: 95 جنيه
أهداف محققة: لا يوجد

الحساب:
- نسبة الخسارة = ((95 - 100) / 100) × 100 = -5%
- نوع الرسالة = "تفعيل وقف الخسارة"
```

#### **مثال 3: وقف خسارة مع أهداف محققة**
```
سعر الشراء: 80 جنيه
الهدف الأول المحقق: 85 جنيه (ربح 6.25%)
سعر وقف الخسارة الجديد: 82 جنيه

الحساب:
- نسبة الربح المحمي = ((82 - 80) / 80) × 100 = 2.5%
- الأهداف المحققة = "الهدف 1: 6.25%"
- نوع الرسالة = "حماية الأرباح" (الوقف أعلى من سعر الشراء)
```

### 🔄 **تدفق حساب الربح/الخسارة**

```mermaid
graph TD
    A[📨 استلام إشارة بيع/وقف خسارة] --> B[📊 استخراج بيانات الصفقة]
    B --> C[💰 سعر الشراء الأصلي]
    B --> D[🎯 الأهداف المحققة سابقاً]
    B --> E[💲 السعر الحالي للإشارة]
    
    C --> F[🧮 حساب نسبة التغيير]
    F --> G{📈 ربح أم خسارة؟}
    
    G -->|ربح| H[✅ حساب نسبة الربح]
    G -->|خسارة| I[❌ حساب نسبة الخسارة]
    
    D --> J{🎯 توجد أهداف محققة؟}
    J -->|نعم| K[📊 حساب أرباح الأهداف السابقة]
    J -->|لا| L[⚠️ لا توجد أرباح سابقة]
    
    H --> M[📢 رسالة حماية الأرباح]
    I --> N{🎯 أهداف محققة؟}
    N -->|نعم| M
    N -->|لا| O[📢 رسالة تحذير خسارة]
    
    K --> M
    L --> O
```

## التحسينات الجديدة لنظام إشارات التداول

### 🚀 **1. نظام تعزيز المراكز (Position Enhancement)**

#### **آلية العمل:**
```python
# في حالة وجود صفقة مفتوحة + إشارة شراء جديدة
if existing_position and new_buy_signal:
    current_buy_price = float(sheet.cell(row, 4).value)
    new_buy_price = float(alert_message["buy_price"])
    
    if new_buy_price < current_buy_price:
        # تعزيز المراكز - السعر الجديد أفضل
        enhancement_ratio = (current_buy_price - new_buy_price) / current_buy_price * 100
        
        # تحديث السعر والأهداف
        updated_buy_price = new_buy_price
        updated_targets = calculate_new_targets(new_buy_price)
        
        # إرسال رسالة تعزيز
        send_enhancement_message(stock_code, enhancement_ratio)
```

#### **رسالة تعزيز المراكز:**
```
🔥 *فرصة تعزيز مراكز* 🔥

🔹 *السهم:* {stock_name} ({stock_code})
📈 *السعر السابق:* {old_price}
💰 *السعر الجديد:* {new_price}
🎯 *تحسن السعر:* {improvement_pct}%

✅ *الأهداف المحدثة:*
⭐️ الهدف الأول: {new_tp1}
⭐️ الهدف الثاني: {new_tp2} 
⭐️ الهدف الثالث: {new_tp3}

💡 *فرصة ممتازة لتحسين متوسط سعر الشراء*
```

### 📊 **2. نظام تحليل المؤشرات الفنية للمخاطر**

#### **المؤشرات المراقبة:**
```python
def analyze_technical_risk(stock_data):
    risk_factors = []
    risk_level = "عادي"
    
    # 1. مؤشر RSI - التشبع الشرائي
    rsi = calculate_rsi(stock_data)
    if rsi > 70:
        risk_factors.append("تشبع شرائي (RSI > 70)")
        risk_level = "مرتفع"
    
    # 2. مؤشر MACD السلبي
    macd_signal = calculate_macd(stock_data)
    if macd_signal < 0:
        risk_factors.append("إشارة MACD سلبية")
        risk_level = "متوسط" if risk_level == "عادي" else "مرتفع"
    
    # 3. حجم التداول الضعيف
    volume_avg = calculate_volume_average(stock_data, 20)
    current_volume = get_current_volume(stock_data)
    if current_volume < volume_avg * 0.5:
        risk_factors.append("حجم تداول ضعيف")
        risk_level = "متوسط" if risk_level == "عادي" else "مرتفع"
    
    return risk_level, risk_factors
```

#### **رسائل تنبيه المخاطر:**
```
⚠️ *تنبيه مستوى مخاطرة مرتفع* ⚠️

🔹 *السهم:* {stock_name} ({stock_code})
🚨 *مستوى المخاطرة:* {risk_level}

📊 *المؤشرات التحذيرية:*
{risk_factors_list}

💡 *ننصح بالحذر وتقليل حجم المركز أو انتظار إشارات أفضل*
```

### 💰 **3. حساب الربح الفعلي المحقق - المحدث**

#### **خوارزمية الحساب الجديدة:**
```python
def calculate_actual_profit_enhanced(buy_price, last_target_date, current_date, stock_code, alert_price, last_target_price=None):
    """
    حساب الربح الفعلي المحقق مع استخدام البدائل المناسبة
    """
    # 1. محاولة الحصول على البيانات التاريخية
    historical_data = get_highest_price_between_dates(
        stock_code, last_target_date, current_date, last_target_price
    )
    
    if historical_data and historical_data['data_source'] == 'historical':
        # استخدام أعلى سعر من البيانات التاريخية
        best_price = max(historical_data['highest_price'], float(alert_price))
        calculation_method = "البيانات التاريخية"
        
    elif last_target_price is not None:
        # استخدام سعر آخر هدف محقق كبديل
        best_price = max(float(last_target_price), float(alert_price))
        calculation_method = "سعر آخر هدف محقق"
        
    else:
        # استخدام سعر الإشارة فقط
        best_price = float(alert_price)
        calculation_method = "سعر الإشارة"
    
    # حساب الربح النهائي
    profit_pct = ((best_price - float(buy_price)) / float(buy_price)) * 100
    
    return {
        'profit_pct': round(profit_pct, 2),
        'best_price': best_price,
        'calculation_method': calculation_method,
        'data_reliability': 'عالية' if calculation_method == "البيانات التاريخية" else 'متوسطة'
    }
```

#### **السيناريوهات المختلفة:**

##### **السيناريو الأول: بيانات تاريخية متوفرة**
```
المدخلات:
- سعر الشراء: 100 جنيه
- آخر هدف محقق: 120 جنيه (2025-06-15)
- إشارة البيع: 115 جنيه (2025-06-22)
- أعلى سعر تاريخي: 125 جنيه (2025-06-18)

النتيجة:
✅ أفضل سعر: 125 جنيه (أعلى من سعر البيع)
📊 الربح الفعلي: 25%
🔍 طريقة الحساب: "البيانات التاريخية"
📈 موثوقية البيانات: عالية
```

##### **السيناريو الثاني: لا توجد بيانات تاريخية - استخدام آخر هدف**
```
المدخلات:
- سعر الشراء: 80 جنيه
- آخر هدف محقق: 96 جنيه
- إشارة وقف خسارة: 85 جنيه
- البيانات التاريخية: غير متوفرة

النتيجة:
✅ أفضل سعر: 96 جنيه (سعر آخر هدف)
📊 الربح الفعلي: 20%
🔍 طريقة الحساب: "سعر آخر هدف محقق"
📈 موثوقية البيانات: متوسطة
```

##### **السيناريو الثالث: لا توجد أهداف محققة**
```
المدخلات:
- سعر الشراء: 50 جنيه
- إشارة بيع: 45 جنيه
- أهداف محققة: لا يوجد

النتيجة:
❌ الخسارة: -10%
🔍 طريقة الحساب: "سعر الإشارة"
📈 موثوقية البيانات: متوسطة
```

### 🔧 **4. بنية البيانات المطلوبة في Google Sheets**

#### **إضافات جديدة لـ Sheet4:**
```
العمود 16: enhancement_count (عدد مرات التعزيز)
العمود 17: original_buy_price (السعر الأصلي قبل التعزيز)
العمود 18: risk_level (مستوى المخاطرة)
العمود 19: risk_factors (عوامل المخاطرة)
العمود 20: highest_price_achieved (أعلى سعر محقق)
العمود 21: actual_profit_pct (نسبة الربح الفعلية)
```

#### **Sheet جديد للتحليل الفني:**
```
Sheet5 - Technical Analysis:
العمود 1: stock_code
العمود 2: date
العمود 3: rsi_value
العمود 4: macd_signal
العمود 5: volume_ratio
العمود 6: risk_assessment
```

### 📈 **5. أمثلة عملية للميزات الجديدة**

#### **مثال 1: تعزيز المراكز**
```
الإشارة الأولى: شراء بسعر 100 جنيه
الإشارة الثانية: شراء بسعر 95 جنيه

النتيجة:
- تحسن السعر: 5%
- تحديث السعر إلى 95 جنيه
- إعادة حساب الأهداف من السعر الجديد
- إرسال رسالة تعزيز للأعضاء والإدارة
```

#### **مثال 2: تنبيه المخاطر**
```
تحليل السهم COMI:
- RSI = 75 (تشبع شرائي)
- MACD = -0.5 (إشارة سلبية)
- حجم التداول = 30% من المتوسط

النتيجة:
- مستوى المخاطرة: مرتفع
- إضافة تحذيرات للرسالة
- تعديل توصيات حجم المركز
```

#### **مثال 3: حساب الربح الفعلي**
```
بيانات الصفقة:
- سعر الشراء: 50 جنيه
- الهدف الأول محقق: 55 جنيه (تاريخ 2025-06-01)
- الهدف الثاني محقق: 60 جنيه (تاريخ 2025-06-10)
- أعلى سعر بين 2025-06-10 و 2025-06-22: 62 جنيه
- إشارة بيع بسعر: 58 جنيه

الحساب:
- الربح على أعلى سعر: ((62-50)/50) × 100 = 24%
- الربح على سعر البيع: ((58-50)/50) × 100 = 16%
- الربح الفعلي المحقق: 24% (الأعلى)
```

### 🎯 **6. تدفق القرارات الجديد**

```mermaid
graph TD
    A[📨 إشارة شراء جديدة] --> B{💼 صفقة مفتوحة؟}
    B -->|نعم| C{💰 السعر أقل؟}
    C -->|نعم| D[🔥 تعزيز المراكز]
    C -->|لا| E[⚠️ إشارة مكررة]
    B -->|لا| F[📊 تحليل المؤشرات]
    F --> G{🚨 مخاطر عالية؟}
    G -->|نعم| H[⚠️ توصية بحذر]
    G -->|لا| I[✅ توصية عادية]
    
    D --> J[💾 تحديث البيانات]
    H --> J
    I --> J
    
    J --> K[📢 إرسال الرسائل]
```

### 📋 **مقارنة بين النظام القديم والجديد**

| الميزة | النظام القديم ❌ | النظام الجديد ✅ |
|--------|-----------------|------------------|
| **إشارة شراء مكررة** | تجاهل تام + تنبيه إدارة | فحص السعر → تعزيز مراكز إذا كان أفضل |
| **حساب الربح في البيع/وقف الخسارة** | على سعر البيع/الوقف فقط | على أعلى سعر محقق بين آخر هدف والخروج |
| **تقييم المخاطر** | ثابت للجميع | ديناميكي حسب المؤشرات الفنية |
| **رسائل التنبيه** | عامة | مخصصة حسب مستوى المخاطرة |
| **تتبع الأداء** | أساسي | شامل مع أعلى الأسعار المحققة |

### 🎨 **قوالب الرسائل الجديدة**

#### **1. رسالة تعزيز المراكز:**
```markdown
🔥 *فرصة تعزيز مراكز ممتازة* 🔥

🔹 *السهم:* البنك التجاري الدولي (COMI)
📊 *السعر السابق:* 50.00 جنيه
💰 *السعر المحسن:* 47.50 جنيه
📈 *تحسن بنسبة:* 5.00%

🎯 *الأهداف المحدثة:*
⭐️ الهدف الأول: 52.25 جنيه (ربح 10%)
⭐️ الهدف الثاني: 55.10 جنيه (ربح 16%)
⭐️ الهدف الثالث: 57.00 جنيه (ربح 20%)

🛑 *وقف الخسارة المحدث:* 45.13 جنيه

💡 *فرصة ذهبية لتحسين متوسط سعر الشراء وزيادة الأرباح المتوقعة*

⏰ *تاريخ التعزيز:* 2025-06-22

📊 المحلل الآلي لأسهم البورصة المصرية
```

#### **2. رسالة توصية مع تحذير مخاطر:**
```markdown
⚠️ *توصية شراء - مستوى مخاطرة مرتفع* ⚠️

🔹 *السهم:* شركة مصر للألومنيوم (EGAL)
💰 *سعر الشراء:* 18.50 جنيه

🎯 *الأهداف:*
⭐️ الهدف الأول: 20.35 جنيه (ربح 10%)
⭐️ الهدف الثاني: 22.20 جنيه (ربح 20%)
⭐️ الهدف الثالث: 24.05 جنيه (ربح 30%)

🛑 *وقف الخسارة:* 16.65 جنيه (خسارة 10%)

🚨 *تحذيرات المخاطرة:*
⚠️ مؤشر RSI يظهر تشبع شرائي (RSI: 72)
⚠️ إشارة MACD سلبية
⚠️ حجم التداول أقل من المتوسط بنسبة 40%

💡 *التوصية:* 
- تقليل حجم المركز إلى 50% من المعتاد
- مراقبة لصيقة للسهم
- الخروج عند أول إشارة ضعف

⏰ *تاريخ التوصية:* 2025-06-22

📊 المحلل الآلي لأسهم البورصة المصرية
```

#### **3. رسالة ربح فعلي محقق:**
```markdown
💎 *تحقق ربح فعلي ممتاز* 💎

🔹 *السهم:* المصرية للاتصالات (ETEL)
💰 *سعر الشراء:* 15.00 جنيه
🏆 *أعلى سعر محقق:* 19.50 جنيه
📈 *الربح الفعلي المحقق:* 30.00%

✅ *تفاصيل الأهداف المحققة:*
⭐️ الهدف الأول: 16.50 جنيه (ربح 10%) - محقق في 2025-06-05
⭐️ الهدف الثاني: 18.00 جنيه (ربح 20%) - محقق في 2025-06-12
⭐️ الهدف الثالث: 19.50 جنيه (ربح 30%) - محقق في 2025-06-18

🛑 *سبب الخروج:* تفعيل وقف الخسارة الوقائي
⏰ *تاريخ الخروج:* 2025-06-22
📊 *إجمالي مدة الصفقة:* 17 يوم

🎉 *تهانينا! تم تحقيق أقصى ربح ممكن من الصفقة*

📊 المحلل الآلي لأسهم البورصة المصرية
```

### 🔄 **خوارزمية تحديد أعلى سعر محقق**

```python
def get_highest_price_between_dates(stock_code, start_date, end_date):
    """
    تحديد أعلى سعر محقق للسهم بين تاريخين
    """
    try:
        # الحصول على البيانات التاريخية للسهم
        historical_data = get_stock_historical_data(stock_code, start_date, end_date)
        
        # البحث عن أعلى سعر (High price)
        highest_price = max(historical_data['High'])
        
        # التحقق من أسعار الإغلاق أيضاً
        highest_close = max(historical_data['Close'])
        
        # اختيار الأعلى بين الاثنين
        final_highest = max(highest_price, highest_close)
        
        # تسجيل التاريخ الذي تحقق فيه أعلى سعر
        highest_date = historical_data[
            historical_data['High'] == final_highest
        ]['Date'].iloc[0]
        
        return {
            'highest_price': final_highest,
            'date_achieved': highest_date,
            'period_days': (end_date - start_date).days
        }
        
    except Exception as e:
        logger.error(f"Error getting highest price for {stock_code}: {e}")
        return None
```

### 📊 **نظام تقييم المخاطر الديناميكي**

```python
def calculate_dynamic_risk_level(stock_data):
    """
    حساب مستوى المخاطرة بناءً على عدة مؤشرات
    """
    risk_score = 0
    risk_factors = []
    
    # 1. مؤشر RSI (وزن: 30%)
    rsi = calculate_rsi(stock_data, period=14)
    if rsi > 80:
        risk_score += 30
        risk_factors.append(f"تشبع شرائي شديد (RSI: {rsi:.1f})")
    elif rsi > 70:
        risk_score += 20
        risk_factors.append(f"تشبع شرائي (RSI: {rsi:.1f})")
    elif rsi < 30:
        risk_score -= 10  # إشارة إيجابية
    
    # 2. مؤشر MACD (وزن: 25%)
    macd_line, signal_line = calculate_macd(stock_data)
    macd_histogram = macd_line - signal_line
    
    if macd_histogram < -0.5:
        risk_score += 25
        risk_factors.append("إشارة MACD سلبية قوية")
    elif macd_histogram < 0:
        risk_score += 15
        risk_factors.append("إشارة MACD سلبية")
    
    # 3. حجم التداول (وزن: 20%)
    volume_ratio = calculate_volume_ratio(stock_data, period=20)
    if volume_ratio < 0.3:
        risk_score += 20
        risk_factors.append(f"حجم تداول ضعيف جداً ({volume_ratio*100:.0f}%)")
    elif volume_ratio < 0.6:
        risk_score += 10
        risk_factors.append(f"حجم تداول منخفض ({volume_ratio*100:.0f}%)")
    
    # 4. تقلبات السعر (وزن: 15%)
    volatility = calculate_price_volatility(stock_data, period=10)
    if volatility > 5:
        risk_score += 15
        risk_factors.append(f"تقلبات سعرية عالية ({volatility:.1f}%)")
    
    # 5. الاتجاه العام (وزن: 10%)
    trend = calculate_trend_direction(stock_data, period=20)
    if trend == "bearish":
        risk_score += 10
        risk_factors.append("الاتجاه العام هبوطي")
    
    # تحديد مستوى المخاطرة النهائي
    if risk_score >= 60:
        risk_level = "عالي جداً"
        risk_emoji = "🔴"
        recommendation = "تجنب الدخول أو تقليل المركز إلى 25%"
    elif risk_score >= 40:
        risk_level = "عالي"
        risk_emoji = "🟠"
        recommendation = "حذر شديد - تقليل المركز إلى 50%"
    elif risk_score >= 20:
        risk_level = "متوسط"
        risk_emoji = "🟡"
        recommendation = "حذر معتدل - مراقبة مستمرة"
    else:
        risk_level = "منخفض"
        risk_emoji = "🟢"
        recommendation = "مستوى مخاطرة مقبول"
    
    return {
        'risk_level': risk_level,
        'risk_score': risk_score,
        'risk_emoji': risk_emoji,
        'risk_factors': risk_factors,
        'recommendation': recommendation
    }
```

هذه التحسينات تجعل النظام أكثر ذكاءً ودقة في:

1. **🔄 تعزيز المراكز**: استغلال الفرص الأفضل بدلاً من تجاهلها
2. **💰 حساب الربح الدقيق**: على أعلى سعر محقق فعلياً وليس على سعر الخروج فقط  
3. **⚠️ تقييم المخاطر**: تحذيرات ذكية مبنية على التحليل الفني
4. **📊 رسائل مخصصة**: محتوى متغير حسب مستوى المخاطرة والظروف

النظام الآن يوفر تجربة تداول أكثر احترافية وأماناً للمستخدمين!
