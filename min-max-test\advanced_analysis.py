"""
وحدة التحليلات المتقدمة للأسهم
تتضمن خوارزميات متقدمة للتحليل الفني للأسهم بما في ذلك:
- تحليل مؤشر القوة النسبية متعدد الإطارات الزمنية (MTF RSI)
- تحليل نقاط الارتكاز الأسبوعية والشهرية (Pivot Points)
- تحليل فيبوناتشي الزمني لتوقع أوقات الانعكاس (Fibonacci Time Analysis)
- تحليل التذبذب والزخم (Volatility & Momentum Analysis)
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math
import talib
import logging
import statistics

# تهيئة السجل
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class AdvancedAnalysis:
    def __init__(self):
        """تهيئة محلل التحليلات المتقدمة"""
        pass
        
    def calculate_mtf_rsi(self, df, periods=[2, 7, 14, 21, 30]):
        """
        حساب مؤشر القوة النسبية (RSI) عبر أطر زمنية متعددة
        
        Args:
            df: DataFrame مع بيانات OHLC
            periods: فترات الحساب للـ RSI
            
        Returns:
            dict: قيم RSI المتعددة مع معلومات التقارب/التباعد
        """
        try:
            if df.empty or len(df) < max(periods) + 10:
                logger.warning("البيانات غير كافية لحساب RSI متعدد الإطارات الزمنية")
                return {}

            close_prices = df['CLOSE'].values
            
            # حساب RSI لمختلف الفترات
            rsi_values = {}
            for period in periods:
                rsi = talib.RSI(close_prices, timeperiod=period)
                rsi_values[f'rsi_{period}'] = rsi[-1]  # آخر قيمة
            
            # حساب متوسط متعدد الإطارات الزمنية
            mtf_rsi_average = sum(rsi_values.values()) / len(rsi_values)
            
            # تحديد توافق الإطار الزمني (إذا كانت جميع القيم متقاربة)
            values = list(rsi_values.values())
            std_dev = statistics.stdev(values) if len(values) > 1 else 0
            alignment = "قوي" if std_dev < 5 else "متوسط" if std_dev < 10 else "ضعيف"
            
            # تحديد انحرافات مؤشر RSI بين الأطر الزمنية المختلفة
            divergences = []
            
            # RSI قصير المدى مقابل طويل المدى (bearish/bullish divergence)
            if rsi_values[f'rsi_{periods[0]}'] > 70 and rsi_values[f'rsi_{periods[-1]}'] < 50:
                divergences.append("تباعد هبوطي محتمل: RSI قصير المدى متشبع شرائيًا بينما RSI طويل المدى لم يتأكد")
                
            if rsi_values[f'rsi_{periods[0]}'] < 30 and rsi_values[f'rsi_{periods[-1]}'] > 50:
                divergences.append("تباعد صعودي محتمل: RSI قصير المدى متشبع بيعيًا بينما RSI طويل المدى لم يتأكد")
            
            # تباعد إيجابي للاتجاه (trend strength confirmation)
            if all(rsi_values[f'rsi_{periods[i]}'] > rsi_values[f'rsi_{periods[i+1]}'] for i in range(len(periods)-1)):
                divergences.append("تأكيد صعودي: تناقص تدريجي في قيم RSI من الأطر الزمنية القصيرة إلى الطويلة")
                
            if all(rsi_values[f'rsi_{periods[i]}'] < rsi_values[f'rsi_{periods[i+1]}'] for i in range(len(periods)-1)):
                divergences.append("تأكيد هبوطي: تزايد تدريجي في قيم RSI من الأطر الزمنية القصيرة إلى الطويلة")
                
            # الحالة العامة للـ RSI المتعدد
            if mtf_rsi_average > 70:
                condition = "تشبع شرائي"
                strength = "مرتفع جداً"
            elif mtf_rsi_average > 60:
                condition = "قوة شرائية"
                strength = "مرتفع"
            elif mtf_rsi_average > 50:
                condition = "إيجابي"
                strength = "معتدل"
            elif mtf_rsi_average > 40:
                condition = "سلبي"
                strength = "معتدل"
            elif mtf_rsi_average > 30:
                condition = "ضعف"
                strength = "منخفض"
            else:
                condition = "تشبع بيعي"
                strength = "منخفض جداً"
            
            return {
                'values': rsi_values,
                'average': mtf_rsi_average,
                'condition': condition,
                'strength': strength,
                'alignment': alignment,
                'std_dev': std_dev,
                'divergences': divergences
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب RSI متعدد الإطارات الزمنية: {e}", exc_info=True)
            return {}
            
    def calculate_pivot_points(self, df, timeframe='weekly'):
        """
        حساب نقاط الارتكاز الكلاسيكية وكاماريلا للإطار الزمني المحدد
        
        Args:
            df: DataFrame مع بيانات OHLC
            timeframe: الإطار الزمني ('weekly' أو 'monthly')
            
        Returns:
            dict: نقاط الارتكاز محسوبة حسب أنواع مختلفة
        """
        try:
            if df.empty or len(df) < 10:
                logger.warning(f"البيانات غير كافية لحساب نقاط الارتكاز للإطار الزمني {timeframe}")
                return {}
            
            # استخراج آخر OHLC كامل حسب الإطار الزمني
            if timeframe == 'weekly':
                # أخذ سعر الإغلاق للأسبوع الماضي
                period_high = df['HIGH'].iloc[-6:-1].max() if len(df) >= 6 else df['HIGH'].max()
                period_low = df['LOW'].iloc[-6:-1].min() if len(df) >= 6 else df['LOW'].min()
                period_close = df['CLOSE'].iloc[-2] if len(df) >= 2 else df['CLOSE'].iloc[-1]
            elif timeframe == 'monthly':
                # أخذ سعر الإغلاق للشهر الماضي (تقريبًا 22 يوم تداول)
                period_high = df['HIGH'].iloc[-23:-1].max() if len(df) >= 23 else df['HIGH'].max()
                period_low = df['LOW'].iloc[-23:-1].min() if len(df) >= 23 else df['LOW'].min()
                period_close = df['CLOSE'].iloc[-2] if len(df) >= 2 else df['CLOSE'].iloc[-1]
            else:
                logger.warning(f"الإطار الزمني غير معروف: {timeframe}")
                return {}
            
            # حساب نقطة الارتكاز الكلاسيكية: (H + L + C) / 3
            pp = (period_high + period_low + period_close) / 3
            
            # مستويات الدعم الكلاسيكية
            s1 = pp - (period_high - period_low)
            s2 = pp - 2 * (period_high - period_low)
            s3 = pp - 3 * (period_high - period_low)
            
            # مستويات المقاومة الكلاسيكية
            r1 = pp + (period_high - period_low)
            r2 = pp + 2 * (period_high - period_low)
            r3 = pp + 3 * (period_high - period_low)
            
            # مستويات كاماريلا (أكثر دقة للتداول اليومي)
            range_val = period_high - period_low
            
            h1 = period_close + range_val * 1.1 / 12
            h2 = period_close + range_val * 1.1 / 6
            h3 = period_close + range_val * 1.1 / 4
            h4 = period_close + range_val * 1.1 / 2
            
            l1 = period_close - range_val * 1.1 / 12
            l2 = period_close - range_val * 1.1 / 6
            l3 = period_close - range_val * 1.1 / 4
            l4 = period_close - range_val * 1.1 / 2
            
            # وودي (Woodie) نقاط الارتكاز - تعطي وزنًا أكبر لسعر الإغلاق
            pp_w = (period_high + period_low + 2 * period_close) / 4
            r1_w = 2 * pp_w - period_low
            r2_w = pp_w + period_high - period_low
            s1_w = 2 * pp_w - period_high
            s2_w = pp_w - period_high + period_low
            
            # تجميع النتائج
            pivot_data = {
                'timeframe': timeframe,
                'classic': {
                    'pivot': pp,
                    'r1': r1,
                    'r2': r2,
                    'r3': r3,
                    's1': s1,
                    's2': s2,
                    's3': s3
                },
                'camarilla': {
                    'h1': h1,
                    'h2': h2,
                    'h3': h3,
                    'h4': h4,
                    'l1': l1,
                    'l2': l2,
                    'l3': l3,
                    'l4': l4
                },
                'woodie': {
                    'pivot': pp_w,
                    'r1': r1_w,
                    'r2': r2_w,
                    's1': s1_w,
                    's2': s2_w
                },
                'previous': {
                    'high': period_high,
                    'low': period_low,
                    'close': period_close
                }
            }
            
            return pivot_data
            
        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الارتكاز: {e}", exc_info=True)
            return {}
            
    def calculate_fibonacci_time_zones(self, df):
        """
        حساب مناطق فيبوناتشي الزمنية استنادًا إلى نقاط التأرجح الهامة
        هذه التحليل يسمح بتوقع النقاط الزمنية المحتملة للانعكاس أو الاستمرار
        
        Args:
            df: DataFrame مع بيانات OHLC
            
        Returns:
            dict: نقاط زمنية فيبوناتشي محسوبة مع توقعات الانعكاس المحتملة
        """
        try:
            if df.empty or len(df) < 30:  # نحتاج لبيانات كافية لتحديد نقاط التأرجح
                logger.warning("البيانات غير كافية لحساب مناطق فيبوناتشي الزمنية")
                return {}
            
            # استخراج بيانات السعر
            prices = df['CLOSE'].values
            dates = pd.to_datetime(df.index) if isinstance(df.index, pd.DatetimeIndex) else pd.date_range(end=datetime.now(), periods=len(df), freq='D')
            
            # تحديد نقاط التأرجح الرئيسية (العليا والسفلى)
            window_size = min(30, len(prices) // 3)  # حجم نافذة للبحث عن نقاط التأرجح
            
            # إيجاد القمم المحلية
            peak_indices = []
            for i in range(window_size, len(prices) - window_size):
                if all(prices[i] > prices[j] for j in range(i - window_size, i)) and all(prices[i] >= prices[j] for j in range(i + 1, min(i + window_size, len(prices)))):
                    peak_indices.append(i)
                    
            # إيجاد القيعان المحلية
            trough_indices = []
            for i in range(window_size, len(prices) - window_size):
                if all(prices[i] < prices[j] for j in range(i - window_size, i)) and all(prices[i] <= prices[j] for j in range(i + 1, min(i + window_size, len(prices)))):
                    trough_indices.append(i)
            
            # تحديد نقاط تأرجح هامة (5 نقاط كحد أقصى)
            swing_points = sorted(peak_indices + trough_indices)
            
            # اختيار نقاط تأرجح كبيرة بناءً على حجم التغير النسبي
            significant_swings = []
            for idx in swing_points:
                # حساب التغير النسبي من النقطة السابقة
                prev_idx = max([j for j in swing_points if j < idx], default=0) if idx > 0 else 0
                change_pct = abs((prices[idx] - prices[prev_idx]) / prices[prev_idx]) * 100 if prices[prev_idx] != 0 else 0
                
                # إضافة النقاط المهمة فقط (تغير > 5%)
                if change_pct > 5:
                    significant_swings.append(idx)
            
            # اختيار أخر 3 نقاط تأرجح هامة كحد أقصى
            significant_swings = significant_swings[-3:] if significant_swings else [0]
            
            # تحسين: اختيار نقطة مرجعية جيدة
            # - إذا كان الاتجاه السعري الحالي صعودي، البحث عن أقل قاع
            # - إذا كان الاتجاه السعري الحالي هبوطي، البحث عن أعلى قمة
            current_trend = "up" if len(prices) >= 10 and prices[-1] > np.mean(prices[-10:]) else "down"
            
            reference_point = None
            reference_date = None
            if current_trend == "up":
                # البحث عن أهم قاع
                if trough_indices:
                    # اختيار القاع الأخير
                    reference_idx = trough_indices[-1]
                    reference_point = prices[reference_idx]
                    reference_date = dates[reference_idx] if reference_idx < len(dates) else dates[-1]
            else:
                # البحث عن أهم قمة
                if peak_indices:
                    # اختيار القمة الأخيرة
                    reference_idx = peak_indices[-1]
                    reference_point = prices[reference_idx]
                    reference_date = dates[reference_idx] if reference_idx < len(dates) else dates[-1]
            
            # استخدام آخر نقطة تأرجح مهمة إذا لم نجد مرجعًا
            if reference_point is None:
                reference_idx = significant_swings[-1] if significant_swings else 0
                reference_point = prices[reference_idx]
                reference_date = dates[reference_idx] if reference_idx < len(dates) else dates[-1]
            
            # حساب نسب فيبوناتشي الزمنية (0.382, 0.5, 0.618, 1.0, 1.618, 2.0, 2.618, 3.0, 4.236)
            fib_ratios = [0.382, 0.5, 0.618, 1.0, 1.618, 2.0, 2.618, 3.0, 4.236]
            time_zones = {}
            
            # تاريخ اليوم
            today = datetime.now().date()
            
            # آخر تاريخ في البيانات
            last_date = dates[-1].date() if len(dates) > 0 else today
            
            # حساب الفاصل الزمني بين نقطة المرجع واليوم
            try:
                if reference_date:
                    ref_date = reference_date.date() if isinstance(reference_date, pd.Timestamp) else reference_date
                    days_since_ref = (last_date - ref_date).days
                    
                    # بالنسبة لكل نسبة فيبوناتشي، حساب التاريخ المتوقع للانعكاس
                    for ratio in fib_ratios:
                        days_to_add = int(days_since_ref * ratio)
                        projected_date = ref_date + timedelta(days=days_to_add)
                        
                        # تخزين فقط التواريخ المستقبلية
                        if projected_date >= today:
                            time_zones[f'fib_{str(ratio).replace(".", "_")}'] = {
                                'date': projected_date.strftime('%Y-%m-%d'),
                                'days_from_now': (projected_date - today).days,
                                'ratio': ratio
                            }
                
                # تحديد المنطقة الحالية (أي نسبة فيبوناتشي نحن فيها الآن)
                current_zone = None
                min_diff = float('inf')
                for ratio in fib_ratios:
                    days_projected = int(days_since_ref * ratio)
                    projected_date = ref_date + timedelta(days=days_projected)
                    days_diff = abs((today - projected_date).days)
                    
                    if days_diff < min_diff:
                        min_diff = days_diff
                        current_zone = ratio
                
                # تحسين: تحليل إضافي للأنماط الموسمية وتكرار الانعكاسات
                seasonal_pattern = self._analyze_seasonal_patterns(df)
                
                return {
                    'reference_point': {
                        'date': ref_date.strftime('%Y-%m-%d') if ref_date else None,
                        'price': reference_point,
                        'type': 'trough' if current_trend == 'up' else 'peak'
                    },
                    'time_zones': time_zones,
                    'current_zone': current_zone,
                    'seasonal_pattern': seasonal_pattern,
                    'trend': current_trend
                }
            except Exception as e:
                logger.error(f"خطأ في حساب مناطق فيبوناتشي الزمنية (جزء التواريخ): {e}", exc_info=True)
                return {}
                
        except Exception as e:
            logger.error(f"خطأ في حساب مناطق فيبوناتشي الزمنية: {e}", exc_info=True)
            return {}
            
    def _analyze_seasonal_patterns(self, df):
        """
        تحليل الأنماط الموسمية في تاريخ السعر
        
        Args:
            df: DataFrame مع بيانات OHLC
        
        Returns:
            dict: تحليل الأنماط الموسمية
        """
        try:
            if df.empty or len(df) < 90:  # نحتاج لبيانات كافية على الأقل ربع سنة
                return {}
            
            # استخراج بيانات السعر
            prices = df['CLOSE'].values
            
            # تحليل التكرار الأسبوعي
            weekly_returns = [0] * 5  # الأحد إلى الخميس (أيام التداول)
            weekly_counts = [0] * 5
            
            # تحليل التكرار الشهري
            monthly_returns = [0] * 12  # يناير إلى ديسمبر
            monthly_counts = [0] * 12
            
            # حساب العوائد اليومية والأسبوعية والشهرية
            dates = pd.to_datetime(df.index) if isinstance(df.index, pd.DatetimeIndex) else pd.date_range(end=datetime.now(), periods=len(df), freq='D')
            
            for i in range(1, len(prices)):
                if i < len(dates):
                    day_of_week = dates[i].weekday()  # 0 = الاثنين، 4 = الجمعة
                    month = dates[i].month - 1  # 0 = يناير
                    
                    # حساب العائد اليومي
                    daily_return = (prices[i] - prices[i-1]) / prices[i-1] * 100
                    
                    # إضافة إلى التكرارات الأسبوعية
                    if 0 <= day_of_week < 5:  # الأيام من الاثنين إلى الجمعة
                        weekly_returns[day_of_week] += daily_return
                        weekly_counts[day_of_week] += 1
                    
                    # إضافة إلى التكرارات الشهرية
                    monthly_returns[month] += daily_return
                    monthly_counts[month] += 1
            
            # حساب متوسط العوائد
            avg_weekly_returns = [weekly_returns[i] / weekly_counts[i] if weekly_counts[i] > 0 else 0 for i in range(5)]
            avg_monthly_returns = [monthly_returns[i] / monthly_counts[i] if monthly_counts[i] > 0 else 0 for i in range(12)]
            
            # تحديد أفضل وأسوأ يوم/شهر
            best_day_idx = avg_weekly_returns.index(max(avg_weekly_returns))
            worst_day_idx = avg_weekly_returns.index(min(avg_weekly_returns))
            
            best_month_idx = avg_monthly_returns.index(max(avg_monthly_returns))
            worst_month_idx = avg_monthly_returns.index(min(avg_monthly_returns))
            
            # ترجمة الأيام
            day_names = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة"]
            month_names = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]
            
            return {
                'weekly': {
                    'best_day': day_names[best_day_idx],
                    'best_day_return': avg_weekly_returns[best_day_idx],
                    'worst_day': day_names[worst_day_idx],
                    'worst_day_return': avg_weekly_returns[worst_day_idx]
                },
                'monthly': {
                    'best_month': month_names[best_month_idx],
                    'best_month_return': avg_monthly_returns[best_month_idx],
                    'worst_month': month_names[worst_month_idx],
                    'worst_month_return': avg_monthly_returns[worst_month_idx]
                }
            }
            
        except Exception as e:
            logger.error(f"خطأ في تحليل الأنماط الموسمية: {e}", exc_info=True)
            return {}
            
    def calculate_volatility_metrics(self, df):
        """
        حساب مقاييس التذبذب المختلفة
        يوفر نظرة متعددة للتقلبات السعرية ويساعد في تحديد نوعية السوق
        
        Args:
            df: DataFrame مع بيانات OHLC
            
        Returns:
            dict: مقاييس التذبذب (ATR, Choppiness Index, Volatility %)
        """
        try:
            if df.empty or len(df) < 14:  # حد أدنى للبيانات
                logger.warning("البيانات غير كافية لحساب مقاييس التذبذب")
                return {}
            
            # حساب المدى الحقيقي المتوسط (ATR)
            high = df['HIGH'].values
            low = df['LOW'].values
            close = df['CLOSE'].values
            
            # حسب المدى الحقيقي أولاً
            tr = np.zeros(len(close))
            for i in range(1, len(close)):
                hl = high[i] - low[i]
                hc = abs(high[i] - close[i-1])
                lc = abs(low[i] - close[i-1])
                tr[i] = max(hl, hc, lc)
            
            # حساب ATR - المتوسط المتحرك الأسي للمدى الحقيقي
            n = 14  # الفترة المعيارية للـ ATR
            atr = np.zeros(len(close))
            atr[0] = tr[1] if len(tr) > 1 else 0  # قيمة ابتدائية
            
            for i in range(1, len(close)):
                atr[i] = (atr[i-1] * (n - 1) + tr[i]) / n
            
            current_atr = atr[-1]
            atr_percent = (current_atr / close[-1]) * 100  # ATR كنسبة مئوية من السعر الحالي
            
            # حساب مؤشر التشويش (Choppiness Index)
            # CI = 100 * LOG10( SUM(ATR(1), n) / (MaxHi(n) - MinLo(n)) ) / LOG10(n)
            # يقيس مدى وجود اتجاه واضح في السوق، القيم المنخفضة تشير لاتجاه قوي والعالية تشير لسوق متذبذب
            n = 14
            sum_atr = sum(tr[-n:])
            max_high = max(high[-n:])
            min_low = min(low[-n:])
            
            denominator = max_high - min_low
            if denominator == 0:  # تجنب القسمة على صفر
                ci = 50  # قيمة محايدة
            else:
                ci = 100 * math.log10(sum_atr / denominator) / math.log10(n)
            
            # حساب تقلب السعر اليومي
            daily_returns = np.diff(close) / close[:-1]
            volatility_percent = np.std(daily_returns) * 100
            
            # حساب متوسط التقلب الضمني للأيام الماضية
            implied_volatility = volatility_percent * math.sqrt(252)  # 252 هو عدد أيام التداول في السنة
            
            # حساب نطاق التذبذب اليومي المتوقع (Expected Daily Range)
            expected_daily_range = close[-1] * volatility_percent / 100
            
            # حساب مؤشر VIX المبسط (مؤشر الخوف والجشع) باستخدام متوسط نطاق بوليجر
            upper, middle, lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
            bb_width = (upper[-1] - lower[-1]) / middle[-1] * 100
            
            # تحديد نوع السوق بناءً على مؤشر التشويش
            if ci < 38.2:  # قيمة فيبوناتشي مهمة
                market_type = "اتجاهي قوي"
                strategy = "المتاجرة مع الاتجاه، استراتيجيات الاختراق"
            elif ci < 50:
                market_type = "اتجاهي"
                strategy = "المتاجرة مع الاتجاه، مع الحذر عند علامات التشبع"
            elif ci < 61.8:  # قيمة فيبوناتشي مهمة أخرى
                market_type = "مختلط"
                strategy = "الانتظار للإشارات القوية أو استراتيجيات المدى المحدود"
            else:
                market_type = "متذبذب"
                strategy = "استراتيجيات المدى المحدود، الشراء عند الدعم والبيع عند المقاومة"
            
            return {
                'atr': current_atr,
                'atr_percent': atr_percent,
                'choppiness_index': ci,
                'market_type': market_type,
                'strategy': strategy,
                'daily_volatility': volatility_percent,
                'annualized_volatility': implied_volatility,
                'expected_daily_range': expected_daily_range,
                'bollinger_width': bb_width
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب مقاييس التذبذب: {e}", exc_info=True)
            return {}
            
    def format_mtf_rsi(self, mtf_data):
        """
        تنسيق نتائج تحليل RSI متعدد الإطارات الزمنية إلى نص مقروء
        
        Args:
            mtf_data: نتائج تحليل RSI متعدد الإطارات الزمنية
            
        Returns:
            str: نص التحليل المنسق
        """
        if not mtf_data or 'values' not in mtf_data:
            return ""
            
        output = "📊 **تحليل مؤشر القوة النسبية متعدد الإطارات الزمنية**\n\n"
        
        # إضافة قيم RSI لمختلف الفترات
        output += "• قراءات مؤشر القوة النسبية (RSI):\n"
        
        for key, value in mtf_data['values'].items():
            period = key.split('_')[1]
            
            # إضافة رموز للإشارة إلى حالة التشبع
            icon = "🔴" if value > 70 else "🟢" if value < 30 else "⚪"
            
            output += f"  {icon} RSI-{period}: {value:.1f}"
            
            # إضافة وصف للحالة
            if value > 70:
                output += " (تشبع شرائي)\n"
            elif value > 60:
                output += " (قوة شرائية)\n"
            elif value > 50:
                output += " (إيجابي)\n"
            elif value > 40:
                output += " (سلبي)\n"
            elif value > 30:
                output += " (ضعف)\n"
            else:
                output += " (تشبع بيعي)\n"
        
        # إضافة متوسط RSI
        output += f"\n• متوسط القراءات: {mtf_data['average']:.1f} - الحالة: {mtf_data['condition']}\n"
        
        # إضافة التوافق بين الفترات
        alignment_icon = "✅" if mtf_data['alignment'] == "قوي" else "⚠️" if mtf_data['alignment'] == "متوسط" else "❌"
        output += f"• توافق الإطارات الزمنية: {alignment_icon} {mtf_data['alignment']}\n"
        
        # إضافة الانحرافات المهمة
        if mtf_data['divergences']:
            output += "\n• ملاحظات مهمة:\n"
            for div in mtf_data['divergences']:
                output += f"  ↔️ {div}\n"
        
        return output
        
    def format_pivot_points(self, pivot_data, current_price):
        """
        تنسيق نتائج نقاط الارتكاز إلى نص مقروء
        
        Args:
            pivot_data: نتائج حساب نقاط الارتكاز
            current_price: السعر الحالي للسهم
            
        Returns:
            str: نص التحليل المنسق
        """
        if not pivot_data or 'classic' not in pivot_data:
            return ""
            
        # تحديد نوع الإطار الزمني للعنوان
        timeframe_ar = "الأسبوعية" if pivot_data['timeframe'] == 'weekly' else "الشهرية"
        output = f"📍 **نقاط الارتكاز {timeframe_ar}**\n\n"
        
        # إضافة الجزء الخاص بنقاط الارتكاز الكلاسيكية
        pp = pivot_data['classic']['pivot']
        
        # تحديد موقع السعر الحالي من نقطة الارتكاز
        price_position = "فوق" if current_price > pp else "تحت"
        
        output += f"• نقطة الارتكاز الرئيسية: {pp:.2f} (السعر الحالي {price_position} نقطة الارتكاز)\n\n"
        
        # إضافة مستويات الدعم
        output += "• مستويات الدعم:\n"
        
        s_levels = sorted([(k, v) for k, v in pivot_data['classic'].items() if k.startswith('s')], 
                         key=lambda x: int(x[0][1:]))
        
        for key, value in s_levels:
            distance = ((value / current_price) - 1) * 100
            nearby = abs(distance) < 3  # قريب جدًا إذا كانت المسافة أقل من 3%
            
            icon = "🔔" if nearby else "  "
            
            output += f"  {icon} {key.upper()}: {value:.2f} ({distance:.1f}%)\n"
        
        # إضافة مستويات المقاومة
        output += "\n• مستويات المقاومة:\n"
        
        r_levels = sorted([(k, v) for k, v in pivot_data['classic'].items() if k.startswith('r')], 
                         key=lambda x: int(x[0][1:]))
        
        for key, value in r_levels:
            distance = ((value / current_price) - 1) * 100
            nearby = abs(distance) < 3  # قريب جدًا إذا كانت المسافة أقل من 3%
            
            icon = "🔔" if nearby else "  "
            
            output += f"  {icon} {key.upper()}: {value:.2f} ({distance:.1f}%)\n"
        
        return output
        
    def format_fibonacci_time(self, fib_data):
        """
        تنسيق نتائج تحليل فيبوناتشي الزمني إلى نص مقروء
        
        Args:
            fib_data: نتائج تحليل فيبوناتشي الزمني
            
        Returns:
            str: نص التحليل المنسق
        """
        if not fib_data or 'time_zones' not in fib_data:
            return ""
            
        output = "⏱️ **تحليل فيبوناتشي الزمني**\n\n"
        
        # إضافة معلومات نقطة المرجع
        if 'reference_point' in fib_data and fib_data['reference_point']['date']:
            ref_type = "قاع" if fib_data['reference_point']['type'] == 'trough' else "قمة"
            output += f"• نقطة البداية: {ref_type} هام تم تحديده عند {fib_data['reference_point']['date']}\n"
            output += f"• الاتجاه الحالي: {'صاعد' if fib_data['trend'] == 'up' else 'هابط'}\n\n"
            
            # نقطة التأرجح الزمني من Fibonacci
            if 'current_zone' in fib_data and fib_data['current_zone']:
                output += f"• نحن حالياً في/قرب منطقة فيبوناتشي الزمنية {fib_data['current_zone']}\n\n"
                
            # تواريخ الانعكاس المتوقعة المقبلة
            output += "• تواريخ الانعكاس المحتملة القادمة:\n"
            
            # فرز المناطق الزمنية بناءً على المسافة الزمنية
            sorted_zones = sorted(fib_data['time_zones'].items(), key=lambda x: x[1]['days_from_now'])
            
            # عرض أقرب 5 تواريخ فقط
            count = 0
            for key, zone in sorted_zones:
                if count >= 5:
                    break
                    
                # عرض فقط التواريخ المستقبلية
                days = zone['days_from_now']
                if days > 0:
                    # تحديد أهمية التاريخ بناءً على نسبة فيبوناتشي
                    importance = ""
                    if zone['ratio'] in [1.0, 1.618, 2.618, 4.236]:
                        importance = "⭐⭐" if zone['ratio'] in [1.618, 2.618] else "⭐"
                        
                    output += f"  📅 {zone['date']} (بعد {days} يوم) - نسبة {zone['ratio']} {importance}\n"
                    count += 1
                    
            # إضافة معلومات الأنماط الموسمية إذا كانت متاحة
            if 'seasonal_pattern' in fib_data and fib_data['seasonal_pattern']:
                output += "\n• التحليل الموسمي للسهم:\n"
                
                seasonal = fib_data['seasonal_pattern']
                if 'weekly' in seasonal:
                    output += f"  🔹 أفضل يوم للتداول: {seasonal['weekly']['best_day']} بمتوسط عائد {seasonal['weekly']['best_day_return']:.2f}%\n"
                    output += f"  🔹 أسوأ يوم للتداول: {seasonal['weekly']['worst_day']} بمتوسط عائد {seasonal['weekly']['worst_day_return']:.2f}%\n"
                    
                if 'monthly' in seasonal:
                    output += f"  🔹 أفضل شهر: {seasonal['monthly']['best_month']} بمتوسط عائد {seasonal['monthly']['best_month_return']:.2f}%\n"
                    output += f"  🔹 أسوأ شهر: {seasonal['monthly']['worst_month']} بمتوسط عائد {seasonal['monthly']['worst_month_return']:.2f}%\n"
        
        return output
        
    def format_volatility_metrics(self, volatility_data):
        """
        تنسيق نتائج تحليل التذبذب إلى نص مقروء
        
        Args:
            volatility_data: نتائج تحليل التذبذب
            
        Returns:
            str: نص التحليل المنسق
        """
        if not volatility_data:
            return ""
            
        output = "💹 **تحليل التذبذب والزخم**\n\n"
        
        # إضافة حالة السوق من مؤشر التشويش
        output += f"• حالة السوق: {volatility_data['market_type']}\n"
        output += f"• الاستراتيجية المناسبة: {volatility_data['strategy']}\n\n"
        
        # إضافة مقاييس التذبذب الرئيسية
        ci = volatility_data['choppiness_index']
        ci_icon = "📊" if ci > 61.8 else "📈" if ci < 38.2 else "↔️"
        output += f"• مؤشر التشويش: {ci_icon} {ci:.1f}% "
        output += "(متذبذب جداً)\n" if ci > 61.8 else "(نطاق محدود)\n" if ci > 50 else "(اتجاهي)\n" if ci > 38.2 else "(اتجاهي قوي)\n"
        
        # إضافة ATR
        atr = volatility_data['atr']
        atr_percent = volatility_data['atr_percent']
        atr_icon = "💫" if atr_percent > 3 else "🔹"
        output += f"• المدى اليومي المتوقع: {atr_icon} {atr:.3f} ({atr_percent:.1f}% من السعر)\n"
        
        # إضافة التذبذب السنوي
        vol = volatility_data['annualized_volatility']
        vol_level = "مرتفع جداً" if vol > 50 else "مرتفع" if vol > 35 else "متوسط" if vol > 20 else "منخفض"
        vol_icon = "📛" if vol > 50 else "⚠️" if vol > 35 else "📊" if vol > 20 else "🔹"
        
        output += f"• التذبذب السنوي: {vol_icon} {vol:.1f}% ({vol_level})\n"
        
        # إضافة عرض بولينجر
        bb_width = volatility_data['bollinger_width']
        bb_icon = "📊" if bb_width > 8 else "🔹"
        output += f"• عرض بولينجر: {bb_icon} {bb_width:.1f}%\n"
        
        return output