"""
Security configuration for the application
"""
import os
import logging

# Rate limiting settings
RATE_LIMIT = {
    "anonymous": {
        "limit": 30,  # requests per window
        "window": 60   # window in seconds
    },
    "free": {
        "limit": 60,
        "window": 60
    },
    "trail": {
        "limit": 120,
        "window": 60
    },
    "paid": {
        "limit": 150,
        "window": 60
    },
    "admin": {
        "limit": 300,
        "window": 60
    }
}

# Security headers
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Content-Security-Policy": "default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline' https://cdn.rtlcss.com; script-src 'self' 'unsafe-inline' https://code.jquery.com https://cdn.jsdelivr.net",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Cache-Control": "no-store, no-cache, must-revalidate, max-age=0",
    "Pragma": "no-cache"
}

# CORS settings
CORS_SETTINGS = {
    "allowed_origins": [
        "http://localhost:3000",
        "http://localhost:9000",
        "https://yourdomain.com"
    ],
    "allowed_methods": ["GET", "POST", "OPTIONS"],
    "allowed_headers": ["Content-Type", "Authorization"],
    "expose_headers": ["Content-Length"],
    "max_age": 86400  # 24 hours
}

# Input validation settings
INPUT_VALIDATION = {
    "max_parameter_length": 500,  # Maximum length of a request parameter
    "max_content_length": 16 * 1024 * 1024,  # 16 MB
    "allowed_content_types": [
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data"
    ]
}

# Suspicious activity detection
SUSPICIOUS_ACTIVITY = {
    "user_agent_patterns": [
        r'(bot|curl|wget|python-requests|scrapy)',
        r'sqlmap',
        r'nikto',
        r'nmap'
    ],
    "ip_blacklist": [],  # Add known malicious IPs here
    "max_failed_attempts": 5,  # Max failed login attempts before rate limit increases
    "lockout_period": 300  # 5 minutes lockout after too many failed attempts
}

# Load environment-specific settings
if os.environ.get('FLASK_ENV') == 'production':
    # Production settings
    DEBUG = False
    LOG_LEVEL = logging.INFO
    # Stricter rate limits in production
    for key in RATE_LIMIT:
        # Reduce limits by 20% in production
        RATE_LIMIT[key]["limit"] = int(RATE_LIMIT[key]["limit"] * 0.8)
else:
    # Development settings
    DEBUG = True
    LOG_LEVEL = logging.DEBUG
