# 1. Imports and Initialization
import pandas as pd
import logging
import tracemalloc
from aiogram import Bo<PERSON>, Dispatcher, executor, types
from aiogram.types import Message, CallbackQuery, Reply<PERSON>eyboardMarkup, KeyboardButton, ContentType
from aiogram.dispatcher.filters import Command
from aiogram.dispatcher import FSMContext
from aiogram.utils.markdown import escape_md
from fuzzywuzzy import process
from user_limit import check_user_limit, display_refer_link, DAILY_FREE_LIMIT, check_subscription_access

# إعداد التسجيل
logger = logging.getLogger(__name__)

# إضافة استيراد الدوال الجديدة
try:
    from user_limit import UserManager
    from arabic_messages import UPGRADE_MOTIVATIONAL_MESSAGES, CONVERSION_CAMPAIGNS
    ENHANCED_MESSAGING_AVAILABLE = True
except ImportError:
    ENHANCED_MESSAGING_AVAILABLE = False
    logger.warning("Enhanced messaging system not available")

from auth import open_google_sheet, dp, bot
from plot import get_stock_price, plot_all, is_support, is_resistance, is_far_from_level, plot_stock_data
from config import sheet_name, file_path, STATUS_MAP, MONEY_FLOW_MAP, HV_MAP, HELP_MESSAGE, SUBSCRIBE_MESSAGE,ADMIN_IDS
from stock_analyzer import StockAnalyzer
from arabic_messages import (
    WELCOME_MESSAGE, 
    MAIN_MENU_MESSAGE, 
    SUBSCRIPTION_BENEFITS, 
    SUBSCRIPTION_PLANS,
    FREE_ANALYSIS_LIMIT_REACHED,
    ANALYSIS_FOOTER,
    COMPREHENSIVE_HELP,
    BOT_REACTIONS,
    FEATURE_PROMOTION,
    SUCCESS_STORIES
)
import random
import asyncio
from markdown_utils import clean_analysis_text

import functools
# Initialize tracemalloc and logging
tracemalloc.start()
logging.basicConfig(filename='error_log.log', level=logging.ERROR)

# Initialize StockAnalyzer instance
stock_analyzer = StockAnalyzer()

# Load Google Sheets and Excel file
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
df = pd.read_excel(file_path)

# Helper function to handle duplicate headers in Google Sheets
def get_sheet_records_safe(worksheet):
    """
    Get all records from a Google Sheet with safe handling of duplicate headers
    """
    try:
        return worksheet.get_all_records()
    except Exception as e:
        error_msg = str(e).lower()
        if "header row in the worksheet is not unique" in error_msg or "duplicate" in error_msg:
            logging.warning(f"Duplicate headers detected in sheet {worksheet.title}, using fallback approach")
            try:
                # Get all values and manually process
                all_values = worksheet.get_all_values()
                if not all_values:
                    return []
                
                headers = all_values[0]
                records = []
                
                # Create unique headers by adding suffix for duplicates
                unique_headers = []
                header_counts = {}
                for header in headers:
                    if header in header_counts:
                        header_counts[header] += 1
                        unique_header = f"{header}_{header_counts[header]}"
                    else:
                        header_counts[header] = 0
                        unique_header = header
                    unique_headers.append(unique_header)
                
                # Build records with unique headers
                for row in all_values[1:]:
                    record = {}
                    for i, value in enumerate(row):
                        if i < len(unique_headers):
                            record[unique_headers[i]] = value
                        else:
                            # Handle rows with more columns than headers
                            record[f"extra_col_{i}"] = value
                    records.append(record)
                
                logging.info(f"Successfully processed {len(records)} records with fallback method")
                return records
                
            except Exception as inner_e:
                logging.error(f"Failed to get sheet records even with fallback: {inner_e}")
                return []
        else:
            logging.error(f"Error getting sheet records: {e}")
            return []




# 2. Helper Functions

# Emergency error handling decorator
def emergency_error_handler(func):
    '''Decorator to catch and log all errors in async functions'''
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            # Get the message object if it exists in args
            message = next((arg for arg in args if hasattr(arg, 'reply')), None)
            
            error_message = f"❌ Error in {func.__name__}: {e}"
            logging.error(error_message, exc_info=True)
            
            if message is not None:
                try:
                    await message.reply("⚠️ حدث خطأ أثناء تنفيذ الأمر. يرجى المحاولة مرة أخرى لاحقاً.")
                except Exception as reply_error:
                    logging.error(f"Failed to send error message: {reply_error}")
            
            return None
    return wrapper

async def send_long_message(chat_id, text, parse_mode=None):
    """Send long messages in chunks with safe parsing."""
    try:
        chunks = [text[i:i + 4096] for i in range(0, len(text), 4096)]
        for chunk in chunks:
            try:
                if parse_mode:
                    await bot.send_message(chat_id=chat_id, text=chunk, parse_mode=parse_mode)
                else:
                    await bot.send_message(chat_id=chat_id, text=chunk)
            except Exception as e:
                # If parsing fails, try without parse_mode
                if parse_mode and "parse entities" in str(e).lower():
                    logging.warning(f"Markdown parsing failed, sending as plain text: {e}")
                    await bot.send_message(chat_id=chat_id, text=chunk)
                else:
                    logging.error(f"Error sending message chunk: {e}")
                    # Try to send a simplified error message
                    try:
                        await bot.send_message(chat_id=chat_id, text="❌ حدث خطأ في إرسال الرسالة")
                    except:
                        pass
    except Exception as e:
        logging.error(f"Critical error in send_long_message: {e}")




def get_stock_data(file_path, stock_code, subscriber_type):
    """Fetch and format stock data."""
    df = pd.read_excel(file_path)
    stock_data = df[df.iloc[:, 0].astype(str) == stock_code].iloc[0]
    code, name, open_price, high, low, close = stock_data[:6]
    highx, lowx, closex = map(float, (high, low, close))
    rnd = 3 if closex < 2 else 2

    if subscriber_type in ["paid", "trail"]:
        stock_alert = get_stock_alert(sheet3, stock_code, closex)
    else:
        stock_alert = "هذا الجزء مخصص فقط للمشتركين"

    volume, change, money_flow, status, net_flow, report_date, free_stocks, yearly_stock_profit, stock_inti_value, repeated_profit, stock_profit_prc, stock_vol = stock_data[
        [7, 6, 15, 17, 26, 28, 29, 30, 31, 32, 33, 34]]
    
    # تحسين معالجة القيم غير الصالحة أو المفقودة
    money_flow_ar = MONEY_FLOW_MAP.get(str(money_flow).strip(), "غير محدد")
    status_ar = STATUS_MAP.get(str(status).strip(), "غير محدد")
    yearly_stock_profit_status = " 🟢 شركة رابحه 🟢" if float(yearly_stock_profit) > 0 else " 🔴 شركة خاسرة 🔴"

    # Calculate and round values for stock data
    target1 = round(float(stock_data[9]), rnd) if stock_data[9] != "-" else "-"
    target2 = round(float(stock_data[11]), rnd) if stock_data[11] != "-" else "-"
    target3 = round(float(stock_data[13]), rnd) if stock_data[13] != "-" else "-"
    stop_loss = round(float(stock_data[16]), rnd) if stock_data[16] != "-" else "-"

    target1_done_ar = "🟢 تحقق الهدف" if str(stock_data[10]).lower() == "true" else "🔴 لم يتحقق بعد"
    target2_done_ar = "🟢 تحقق الهدف" if str(stock_data[12]).lower() == "true" else "🔴 لم يتحقق بعد"
    target3_done_ar = "🟢 تحقق الهدف" if str(stock_data[14]).lower() == "true" else "🔴 لم يتحقق بعد"

    change_ar = "وبنسبة ارتفاع قدرة " if float(change) > 0 else "وبنسبة انخفاض قدرة "
    volume_ar = "السيوله مرتفعه ونتوقع ارتفاع السعر" if float(volume) >= 70 else "السيوله متوسطه" if 50 < float(
        volume) < 70 else "السيوله منخفصة ونتوقع انخفاض السعر"

    if subscriber_type in ["paid", "trail"]:
        tenkan = round(float(stock_data[18]), rnd)
        kijun = round(float(stock_data[19]), rnd)
        if tenkan >= kijun:
            if closex >= tenkan:
                if closex >= kijun:
                    advice = f"نوصى بالأحتفاظ طالما السعر اعلى من {tenkan} مع جنى ارباح جزئى"
                else:
                    advice = f"نوصى بالمتاجرة الحذرة طالما السعر اقل من {kijun} مع الحذر من كسر الدعم {kijun}"
            else:
                advice = f"نوصى بتقليل المراكز الشرائية طالما السعر اقل من {tenkan}"
        else:
            if closex >= tenkan:
                advice = f"متاجرة مع الحذر من كسر الدعم {kijun}"
            else:
                advice = f"نوصى بتقليل المراكز الشرائية طالما السعر اقل من {tenkan}"
    else:
        advice = "هذا الجزء مخصص فقط للمشتركين"

    ma5, ma10, ma20, ma50, ma100, ma200 = [round(float(stock_data[i]), rnd) for i in range(20, 26)]

    if subscriber_type in ["paid", "trail"]:
        m_avg = " الرؤية طبقا للمتوسطات المتحركة: "
        long_term_trend = "صاعد" if closex > ma200 else "هابط"
        m_avg += f"الإتجاه {long_term_trend} على المدى الطويل و "
        medium_term_trend = "صاعد" if closex > ma50 else "هابط"
        m_avg += f"الإتجاه {medium_term_trend} على المدى المتوسط و  "
        short_term_trend = "صاعد" if closex > ma20 else "هابط"
        m_avg += f"الإتجاه {short_term_trend} على المدى القصير و"
        if all(closex < ma for ma in [ma5, ma10, ma20, ma50, ma100, ma200]):
            m_avg += " اسفل  جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى مقاومة متحرك يحول دون صعودة  "
        elif all(closex > ma for ma in [ma5, ma10, ma20, ma50, ma100, ma200]):
            m_avg += "فوق جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى دعم متحرك يحول دون هبوطه  "
        else:
            m_avg += "فوق جميع متوسطاته المتحركة القياسية والتي تعمل له كمستوى دعم متحرك يحول دون هبوطه  "
            m_avg += " ماعدا "

        for ma in [(ma5, 5), (ma10, 10), (ma20, 20), (ma50, 50), (ma100, 100), (ma200, 200)]:
            if closex <= ma[0]:
                m_avg += f" المتوسط المتحرك ({ma[1]}) يمثل مقاومة عند السعر ({ma[0]}) "

        m_avg = m_avg[:-1]
    else:
        m_avg = "هذا الجزء مخصص فقط للمشتركين"

    p = round((highx + closex + lowx) / 3, rnd)
    s1 = round(2 * p - highx, rnd)
    s3 = round((lowx - 2 * (highx - p)), rnd)
    r1 = round((2 * p - lowx), rnd)
    s2 = round(p - (r1 - s1), rnd)
    r2 = round(p + (r1 - s1), rnd)
    r3 = round(highx + (2 * (p - lowx)), rnd)

    # تنسيق قيم السيولة والحالة
    net_flow_formatted = net_flow if pd.notna(net_flow) and net_flow != "" else "غير متوفر"
    
    stock_datas = (
        f"تقرير فنى عن السهم بتاريخ: ({report_date})\n"
        f"{code} - {name}\n"
        f"كان سعر الفتح للسهم: ({open_price})\n"
        f"واعلى سعر: ({high})\n"
        f"واقل سعر: ({low})\n"
        f"واغلق عند السعر: ({close})\n"
        f"{change_ar}: ({change}%)\n"
        f"وكان حجم التداول للسهم هو :({stock_vol})\n"
        f"وكانت نسبة السيوله بالسهم: ( {volume}% )--{volume_ar}\n"
        f"نوع السيوله: {money_flow_ar}   صافى السيوله: ( {net_flow_formatted} ) \n"
        f"حاله السهم: {status_ar}\n"
        f"ارتكاز السهم: ({p})\n"
        f"الهدف الأول: {target1} {target1_done_ar}\n"
        f"الهدف الثانى: {target2} {target2_done_ar}\n"
        f"الهدف الثالث: {target3} {target3_done_ar}\n"
        f"وقف الخسارة: {stop_loss}\n"
        f"المقاومات اليومية امام السهم لجلسة الغد بالترتيب: {r1} - {r2} - {r3}\n"
        f"الدعوم اليومية امام السهم لجلسة الغد بالترتيب: {s1} - {s2} - {s3}\n"
        f"========================\n"
        f"التوصية: {advice} \n"
        f"توصيات المشتركين:{stock_alert} \n"
        f"{m_avg} \n"
        f"========================\n"
        f"التحليل المالى للشركة \n"
        f"اجمالى الأسهم الحرة: {free_stocks}  سهم \n"
        f" الربح السنوى للسهم بالجنية: {yearly_stock_profit}{yearly_stock_profit_status}\n"
        f"  القيمه الدفتريه : {stock_inti_value}\n "
        f" مكرر الربحية للسهم : {repeated_profit}\n"
        f"نسبة ربحية السهم طبقا للربح السنوى  : {stock_profit_prc} %  \n"
        f"========================\n"
        f"هذا التقرير صالح لجلسة الغد فقط وسيتم تحديثة يوميا \n"
        f"مع تحيات فريق عمل المحلل الألى لأسهم البورصه المصريه"
    )
    return stock_datas

def get_stock_alert(sheet3, stock_code, closex):
    """Fetch stock alerts from Google Sheets."""
    alert_row = sheet3.find(stock_code)
    if alert_row:
        signal = sheet3.cell(alert_row.row, 3).value
        if signal == "buy":
            buy_price = float(sheet3.cell(alert_row.row, 4).value)
            buy_date = sheet3.cell(alert_row.row, 5).value
            tp1_price = float(sheet3.cell(alert_row.row, 8).value)
            tp2_price = float(sheet3.cell(alert_row.row, 9).value)
            tp3_price = float(sheet3.cell(alert_row.row, 10).value)
            sl_price = float(sheet3.cell(alert_row.row, 11).value)
            profito = round((closex - buy_price) / buy_price * 100, 2)
            return f"لقد اوصينا بالشراء عند السعر ({buy_price}) و مازالت الصفقه مفتوحه وبنسبة ربح ({profito} %) وسيكون الهدف الاول عند ({tp1_price}) و الهدف الثانى عند ({tp2_price}) و الهدف الثالث عند ({tp3_price}) ووقف الخسارة للتوصيه عند ({sl_price})"
        elif signal == "t1done":
            buy_price = float(sheet3.cell(alert_row.row, 4).value)
            buy_date = sheet3.cell(alert_row.row, 5).value
            tp1_price = float(sheet3.cell(alert_row.row, 8).value)
            tp2_price = float(sheet3.cell(alert_row.row, 9).value)
            tp3_price = float(sheet3.cell(alert_row.row, 10).value)
            tp1_date = sheet3.cell(alert_row.row, 12).value
            profito = round((tp1_price - buy_price) / buy_price * 100, 2)
            return f"لقد اوصينا بالدخول فى السهم بتاريخ {buy_date} عند السعر {buy_price} وقد تحقق الهدف الأول عند السعر {tp1_price} بتاريخ {tp1_date} وبنسبة ربح {profito}% وسيكون الهدف الثانى عند السعر {tp2_price} والهدف الثالث عند السعر {tp3_price} مع رفع حمايه الأرباح للهدف المتحقق"
        elif signal == "t2done":
            buy_price = float(sheet3.cell(alert_row.row, 4).value)
            buy_date = sheet3.cell(alert_row.row, 5).value
            tp2_price = float(sheet3.cell(alert_row.row, 9).value)
            tp3_price = float(sheet3.cell(alert_row.row, 10).value)
            tp2_date = sheet3.cell(alert_row.row, 13).value
            profito = round((tp2_price - buy_price) / buy_price * 100, 2)
            return f"لقد اوصينا بالدخول فى السهم بتاريخ {buy_date} عند السعر {buy_price} وقد تحقق الهدف الثانى عند السعر {tp2_price} وفى تاريخ {tp2_date} وبنسبة ربح {profito}% وسيكون الهدف الثالث عند السعر {tp3_price} مع رفع حمايه الأرباح للهدف المتحقق"
        elif signal == "t3done":
            buy_price = float(sheet3.cell(alert_row.row, 4).value)
            buy_date = sheet3.cell(alert_row.row, 5).value
            tp3_price = float(sheet3.cell(alert_row.row, 10).value)
            tp3_date = sheet3.cell(alert_row.row, 14).value
            if closex >= tp3_price:
                profito = round((closex - buy_price) / buy_price * 100, 2)
                return f"لقد اوصينا بالدخول فى السهم بتاريخ {buy_date} عند السعر {buy_price} وقد تحقق الهدف الثالث عند السعر {tp3_price} بتاريخ {tp3_date} وبنسبة ربح {profito}% بعد أن تجاوز السعر  الهدف."
            else:
                profito = round((tp3_price - buy_price) / buy_price * 100, 2)
                return f"لقد اوصينا بالدخول فى السهم بتاريخ {buy_date} عند السعر {buy_price} وقد تحقق الهدف الثالث عند السعر {tp3_price} بتاريخ {tp3_date} وبنسبة ربح {profito}% مع رفع حماية الأرباح للهدف المتحقق."
        elif signal == "tsl":
            buy_price = float(sheet3.cell(alert_row.row, 4).value)
            buy_date = sheet3.cell(alert_row.row, 5).value
            tsl_price = float(sheet3.cell(alert_row.row, 11).value)
            tsl_date = sheet3.cell(alert_row.row, 15).value
            loss = round((tsl_price - buy_price) / buy_price * 100, 2)
            return f"لقد اوصينا بالدخول فى السهم بتاريخ {buy_date} عند السعر {buy_price} وكسرت الصفقة حد وقف الخسارة عند السعر {tsl_price} بتاريخ {tsl_date} وبنسبة خسارة {loss}%"
        elif signal == "sell":
            sell_price = float(sheet3.cell(alert_row.row, 6).value)
            sell_date = sheet3.cell(alert_row.row, 7).value
            return f"لقد اوصينا بالخروج من السهم بتاريخ {sell_date} عند السعر {sell_price} وسنعاود الدخول عندما تظهر على السهم علامات الأيجابية"
    return "      "


# 3. Command Handlers
@dp.message_handler(commands=['help'])
async def process_help(message: Message):
    """Send comprehensive help message with enhanced formatting"""
    await message.reply(COMPREHENSIVE_HELP, parse_mode="Markdown")


@dp.message_handler(commands=['subscribe'])
async def process_subscribe(message: Message):
    """Send subscription details with benefits and pricing"""
    # Send benefits first
    await message.reply(SUBSCRIPTION_BENEFITS, parse_mode="Markdown")
    
    # Then send pricing plans
    await message.reply(SUBSCRIPTION_PLANS, parse_mode="Markdown")
    
    # Add a success story for motivation
    await message.reply(random.choice(SUCCESS_STORIES), parse_mode="Markdown")


@dp.message_handler(commands=['modarba_all'])
async def process_modarba_all(message: Message):
    """Handle /modarba_all command."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)

    df = pd.read_excel(file_path)
    modarba_data = df[df.iloc[:, 8] == True]
    message_text = "🟢 تفصيل اسهم المضاربة عن اليوم\n"
    for _, row in modarba_data.iterrows():
        target1_done_ar = "🟢 تحقق الهدف" if row[10] else "🔴 لم يتحقق بعد"
        target2_done_ar = "🟢 تحقق الهدف" if row[12] else "🔴 لم يتحقق بعد"
        target3_done_ar = "🟢 تحقق الهدف" if row[14] else "🔴 لم يتحقق بعد"

        money_flow_ar = MONEY_FLOW_MAP.get(str(row[15]).strip(), "Invalid value")
        status_ar = STATUS_MAP.get(str(row[17]).strip(), "Invalid value")

        message_text += f"{row[0]} - {row[1]}\nنسبة الربح من متوسط سعر الشراء اليوم : {row[6]} %\n"
        message_text += f"الهدف الأول: {row[9]} {target1_done_ar}\n"
        message_text += f"الهدف الثانى: {row[11]} {target2_done_ar}\n"
        message_text += f"الهدف الثالث: {row[13]} {target3_done_ar}\n"
        message_text += f"وقف الخسارة: {row[16]}\n"
        message_text += f"نوع السيوله: {money_flow_ar}\n"
        message_text += f"حاله السهم: {status_ar}\n"
    message_text += "مع تحيات فريق عمل المحلل الألى لأسهم البورصه المصريه"

    await send_long_message(message.chat.id, message_text, parse_mode="Markdown")


@dp.message_handler(commands=['modarba'])
async def process_modarba(message: Message):
    """Handle /modarba command with enhanced messaging."""
    user_id = message.from_user.id
    
    # فحص الوصول للميزة مع رسائل تحفيزية محسنة
    if not await check_subscription_access(message):
        # إرسال رسالة تحفيزية خاصة بالميزة (أسهم المضاربة)
        if ENHANCED_MESSAGING_AVAILABLE:
            blocked_message = UserManager.send_upgrade_message(str(user_id), "premium_feature_blocked")
            feature_promo = """

🎯 **خاص بأسهم المضاربة:**
🎫 كود خصم فوري: `MODARBA25`
⚡ تفعيل: `/redeem MODARBA25`
            """
            await message.reply(blocked_message + feature_promo, parse_mode="Markdown")
        return
    
    # الآن فحص حد المستخدم وتحديث العدد
    subscriber_type, count = await check_user_limit(message)

    df = pd.read_excel(file_path)
    modarba_data = df[df.iloc[:, 8] == True].values.tolist()

    await message.answer("🟢 اسهم المضاربة عن اليوم")
    for index, row in enumerate(modarba_data):
        code = row[0]
        name = row[1]
        try:
            message_text = f"{code} - {name}\n"
            await bot.send_message(chat_id=message.chat.id, text=message_text)
        except:
            await message.answer("An error occurred while trying to send the message.")


async def process_hv(message: Message, hv_code: str):
    """Handle liquidity commands."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)

    df = pd.read_excel(file_path)
    hv_col_index = 15
    hv_ar = HV_MAP.get(hv_code, "Invalid value")
    hv_data = df[df.iloc[:, hv_col_index].astype(str) == str(hv_ar)]

    if hv_ar == "1":
        await message.answer("🟢 اسهم السيوله الداخلة عن اليوم ")
    else:
        await message.answer("🔴 اسهم السيوله الخارجه عن اليوم ")

    for index, row in hv_data.iterrows():
        code = str(row[0])
        name = str(row[1])
        message_text = f"{code}: {name}"
        await message.answer(message_text)


@dp.message_handler(commands=['stock'])
async def process_stock_code(message: Message):
    """Handle /stock command."""
    subscriber_type, count = await check_user_limit(message)
    if subscriber_type == "free" and count >= DAILY_FREE_LIMIT:
        await message.answer(f"Sorry, more than {DAILY_FREE_LIMIT} reports per day only available for paid subscribers to continue using the service send PM to  @elborsa\_bot.")
        return

    try:
        stock_code = message.text.split()[1]
    except IndexError:
        await message.answer("Please provide a stock code to get information about.")
        return

    stock_codes = df.iloc[:, 0].tolist()
    closest_match = process.extractOne(stock_code.upper(), stock_codes)
    if closest_match[1] < 70:
        await message.answer(f"No stock found for '{stock_code}'. Did you mean:\n")
        similar_stocks = process.extract(stock_code.upper(), stock_codes, limit=5)
        for stocks in similar_stocks:
            await message.answer(f"/stock {stocks[0]}")
    else:
        stock_datas = get_stock_data(file_path, closest_match[0], subscriber_type)
        # Check if stock_datas is empty before sending
        if not stock_datas:
            logging.error(f"Empty stock data for code: {stock_code}")
            await message.reply("❌ عذراً، لم نتمكن من استرجاع بيانات السهم. يرجى التأكد من كود السهم والمحاولة مرة أخرى.")
            return

        await message.reply(stock_datas)


@dp.message_handler(commands=['fibo'])
async def fibo(message: Message):
    """Handle /fibo command."""
    try:
        high_point, low_point = message.text.split()[1:]
        high_point = float(high_point)
        low_point = float(low_point)
        range_ = high_point - low_point
        level_0 = high_point
        level_23_6 = high_point - (0.236 * range_)
        level_38_2 = high_point - (0.382 * range_)
        level_50 = high_point - (0.5 * range_)
        level_61_8 = high_point - (0.618 * range_)
        level_78_6 = high_point - (0.786 * range_)
        level_100 = low_point
        projection_level_161_8 = high_point + (1.618 * range_)
        projection_level_261_8 = high_point + (2.618 * range_)
        projection_level_423_6 = high_point + (4.236 * range_)
        retracement_level_23_6 = high_point + (0.236 * range_)
        retracement_level_38_2 = high_point + (0.382 * range_)
        retracement_level_50 = high_point + (0.5 * range_)
        retracement_level_61_8 = high_point + (0.618 * range_)
        retracement_level_78_6 = high_point + (0.786 * range_)

        fibonacci_levels_string = f"""Fibonacci Levels:
Level 0: {level_0:.2f}
Level 23.6%: {level_23_6:.2f}
Level 38.2%: {level_38_2:.2f}
Level 50%: {level_50:.2f}
Level 61.8%: {level_61_8:.2f}
Level 78.6%: {level_78_6:.2f}
Level 100%: {level_100:.2f}
Projection Level 161.8%: {projection_level_161_8:.2f}
Projection Level 261.8%: {projection_level_261_8:.2f}
Projection Level 423.6%: {projection_level_423_6:.2f}
Retracement Level 23.6%: {retracement_level_23_6:.2f}
Retracement Level 38.2%: {retracement_level_38_2:.2f}
Retracement Level 50%: {retracement_level_50:.2f}
Retracement Level 61.8%: {retracement_level_61_8:.2f}
Retracement Level 78.6%: {retracement_level_78_6:.2f}
"""
        await bot.send_message(chat_id=message.chat.id, text=fibonacci_levels_string)
    except (ValueError, TypeError):
        await bot.send_message(chat_id=message.chat.id, text="Invalid input. Please use the following format: /fibo high_point low_point")


@dp.message_handler(commands=['mysubscribtion'])
async def get_subscription_date(message: types.Message):
    """Handle /mysubscribtion command with enhanced formatting."""
    user_id = message.from_user.id
    user_data = sheet2.find(str(user_id))
    if user_data:
        row_index = user_data.row
        row_values = sheet2.row_values(row_index)
        
        # Log the raw data for diagnostic purposes
        logging.info(f"Row values for user {user_id}: {row_values}")
        
        # Extract all relevant subscription data with correct column mapping
        try:
            # Based on your sheet structure:
            # Column 0: User ID
            # Column 1: Counter
            # Column 2: Subscription Type
            # Column 3: End Date
            # Column 4: First Name
            # Column 5: Last Name
            subscription_type = row_values[2] if len(row_values) > 2 else "غير معروف"
            end_date = row_values[3] if len(row_values) > 3 else "غير محدد"
            
            # Get user name from columns 4-5 if available
            user_name = ""
            if len(row_values) > 4 and row_values[4]:
                user_name = row_values[4]
                if len(row_values) > 5 and row_values[5]:
                    user_name += " " + row_values[5]
        except Exception as e:
            logging.error(f"Error parsing subscription data: {e}")
            subscription_type = "غير معروف"
            end_date = "غير محدد"
            user_name = ""
        
        # Format dates if they exist
        days_remaining = 0
        days_str = ""
        if end_date != "غير محدد":
            try:
                from datetime import datetime
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                days_remaining = (end_date_obj - datetime.now()).days
                days_str = f"(متبقي {days_remaining} يوم)" if days_remaining > 0 else "(منتهي)"
            except Exception as e:
                logging.error(f"Error calculating days remaining: {e}")
                days_str = ""
            
        # Map subscription type to Arabic
        subscription_type_ar = {
            "free": "مجاني",
            "paid": "مدفوع",
            "trail": "تجريبي",
            "admin": "مشرف"
        }.get(subscription_type, subscription_type)
        
        # Create a beautifully formatted subscription message
        subscription_message = (
            f"🌟 *معلومات اشتراكك* 🌟\n\n"
            f"👤 *معرف المستخدم:* `{user_id}`\n"
        )
        
        # Add user name if available
        if user_name:
            subscription_message += f"👨‍💼 *الاسم:* {user_name}\n"
            
        subscription_message += (
            f"📋 *نوع الاشتراك:* {subscription_type_ar}\n"
            f"📅 *تاريخ الانتهاء:* {end_date} {days_str}\n\n"
        )
        
        # Add features based on subscription type
        if subscription_type == "paid" or subscription_type == "trail" or subscription_type == "admin":
            subscription_message += (
                "✅ *الميزات المتاحة:*\n"
                "• تحليلات غير محدودة للأسهم\n"
                "• وصول كامل لجميع مستويات الدعم والمقاومة\n"
                "• توصيات خاصة عالية الدقة\n"
                "• أهداف سعرية مدروسة\n"
                "• تنبيهات الكسر والاختراق"
            )
        else:  # free
            subscription_message += (
                "⚠️ *قيود الحساب المجاني:*\n"
                f"• محدود بـ {DAILY_FREE_LIMIT} تحليلات يومياً\n"
                "• وصول محدود لمستويات الدعم والمقاومة\n"
                "• بدون توصيات خاصة\n\n"
                "🔼 للترقية إلى حساب مدفوع: /subscribe"
            )
            
        # Add renewal reminder if near expiration
        if days_str and days_str != "(منتهي)" and days_remaining < 7:
            subscription_message += (
                f"\n\n⚠️ *تنبيه:* اشتراكك سينتهي قريباً! ({days_remaining} يوم متبقي)\n"
                "📲 للتجديد اضغط: /subscribe"
            )
            
        await message.reply(
            subscription_message,
            parse_mode="Markdown"
        )
    else:
        # User not found in database
        await message.reply(
            "❌ *عذراً، لم نتمكن من العثور على بيانات اشتراكك*\n\n"
            "قد يكون هذا بسبب أنك لم تشترك بعد أو أن هناك مشكلة في نظامنا.\n"
            "يرجى المحاولة مرة أخرى أو التواصل مع الدعم: @elborsa_bot",
            parse_mode="Markdown"
        )


# 4. Menu and Button Handlers
BUTTON_DEALS = "📊 الصفقات"
BUTTON_CHART = "📊 الرسم البياني" 
BUTTON_BACK = "🔙 العودة للقائمة الرئيسية"
BUTTON_MODARBA_ALL = "📊تفاصيل اسهم المتاجرة عن اليوم"
BUTTON_MODARBA = "💹اسهم المتاجرة عن اليوم"
BUTTON_LIQUIDITY = "💰السيوله"
BUTTON_REPORTS = "📈تقارير الأسهم"
BUTTON_AI_ANALYSIS = "🤖تحليل ذكي للأسهم"
BUTTON_HELP = "❓المساعدة"
BUTTON_SUBSCRIBE = "اشتراك / تجديد"
BUTTON_MY_SUBSCRIPTION = "📅اشتراكى"
BUTTON_FIBO = "🔢مستويات فيبوناتشي"
BUTTON_REFER_LINK = "👥رابط الإحالة"
BUTTON_OPEN_DEALS = "📊 صفقات مفتوحة"
BUTTON_T1_ACHIEVED = "✅ تحقق الهدف الأول"
BUTTON_T2_ACHIEVED = "✅ تحقق الهدف الثاني"
BUTTON_T3_ACHIEVED = "✅ تحقق الهدف الثالث"
BUTTON_NEW_SUBSCRIPTION = "مشترك جديد"
BUTTON_RENEW_SUBSCRIPTION = "تجديد اشتراك"
BUTTON_PAYMENT_CONFIRMATION = "إرسال صورة تأكيد الدفع"
BUTTON_INCOMING_LIQUIDITY = "السيوله الداخله"
BUTTON_OUTGOING_LIQUIDITY = "السيوله الخارجه"

@dp.message_handler(commands=['menu'])
async def cmd_menu(message: Message):
    """Display improved main menu with Arabic text"""
    keyboard = ReplyKeyboardMarkup(resize_keyboard=True)
    keyboard.row(KeyboardButton(BUTTON_MODARBA_ALL), KeyboardButton(BUTTON_MODARBA))
    keyboard.row(KeyboardButton(BUTTON_LIQUIDITY), KeyboardButton(BUTTON_REPORTS))
    keyboard.row(KeyboardButton(BUTTON_AI_ANALYSIS), KeyboardButton(BUTTON_CHART))
    keyboard.row(KeyboardButton(BUTTON_DEALS), KeyboardButton(BUTTON_HELP))
    keyboard.row(KeyboardButton(BUTTON_SUBSCRIBE), KeyboardButton(BUTTON_MY_SUBSCRIPTION))
    keyboard.row(KeyboardButton(BUTTON_FIBO), KeyboardButton(BUTTON_REFER_LINK))
    
    await message.reply(MAIN_MENU_MESSAGE, reply_markup=keyboard, parse_mode="Markdown")

@dp.message_handler(lambda message: message.text == "اشتراك / تجديد")
async def subscription_menu(message: Message):
    """Display subscription menu."""
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton(BUTTON_NEW_SUBSCRIPTION),
        KeyboardButton(BUTTON_RENEW_SUBSCRIPTION),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply("اختر من القائمة الفرعية:", reply_markup=markup)

@dp.message_handler(lambda message: message.text == "مشترك جديد")
async def new_subscription(message: Message):
    """Handle new subscription."""
    subscription_message = """
    🎉 **مرحبًا بك في خدمة الاشتراك!** 🎉
    يمكنك الاشتراك في الخدمة عن طريق:

    - **الدفع عبر فودافون كاش**: ارسل المبلغ إلى الرقم **01068090634**.
    - **الدفع عبر إنستاباي**: ارسل المبلغ إلى الرابط التالي: [01068090634](https://instapay.com).

    **أسعار الاشتراك:**
    - اشتراك شهري: 500 جنيه.
    - اشتراك ربع سنوي: 1400 جنيه.
    - اشتراك سنوي: 5000 جنيه.

    بعد إتمام الدفع، أرسل صورة تأكيد الدفع ليتم تفعيل اشتراكك.
    """
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton("إرسال صورة تأكيد الدفع"),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply(subscription_message, reply_markup=markup, parse_mode="Markdown")

@dp.message_handler(lambda message: message.text == "تجديد اشتراك")
async def renew_subscription(message: Message):
    """Handle subscription renewal."""
    renew_message = """
    🔄 **تجديد الاشتراك** 🔄
    يمكنك تجديد اشتراكك عن طريق:

    - **الدفع عبر فودافون كاش**: ارسل المبلغ إلى الرقم **01068090634**.
    - **الدفع عبر إنستاباي**: ارسل المبلغ إلى الرابط التالي: [01068090634](https://instapay.com).

    **أسعار التجديد:**
    - تجديد شهري: 500 جنيه.
    - تجديد ربع سنوي: 1400 جنيه.
    - تجديد سنوي: 5000 جنيه.

    بعد إتمام الدفع، أرسل صورة تأكيد الدفع ليتم تجديد اشتراكك.
    """
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton("إرسال صورة تأكيد الدفع"),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply(renew_message, reply_markup=markup, parse_mode="Markdown")

@dp.message_handler(lambda message: message.text == "إرسال صورة تأكيد الدفع")
async def request_payment_confirmation(message: Message):
    """Request payment confirmation."""
    confirmation_message = """
    📤 **إرسال صورة تأكيد الدفع**
    من فضلك، قم بإرسال صورة تأكيد الدفع مباشرة إلى البوت @elborsa\_bot.

    **تعليمات:**
    1. اضغط على زر الملفات 📎 في البوت.
    2. اختر صورة تأكيد الدفع من معرض الصور.
    3. أرسل الصورة.

    سيتم تفعيل اشتراكك في أقرب وقت بعد التحقق من الصورة. @elborsa\_bot
    """
    await message.reply(confirmation_message, parse_mode="Markdown")

@dp.message_handler(content_types=ContentType.PHOTO)
async def handle_payment_confirmation_photo(message: Message):
    """Handle payment confirmation photo."""
    await message.reply("تم استلام صورة تأكيد الدفع بنجاح. سيتم مراجعتها من قبل الفريق المختص.")
    await bot.send_message(chat_id="@elborsa\_bot", text="تم استلام صورة تأكيد دفع جديدة.")
    await bot.send_photo(chat_id="@elborsa\_bot", photo=message.photo[-1].file_id)

# 5. Liquidity and Deals Handlers
@dp.message_handler(lambda message: message.text == "💰السيوله")
async def liquidity_menu(message: Message):
    """Display liquidity menu."""
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton("السيوله الداخله"),
        KeyboardButton("السيوله الخارجه"),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply("اختر نوع السيوله:", reply_markup=markup)

@dp.message_handler(lambda message: message.text in ["السيوله الداخله", "السيوله الخارجه"])
async def handle_liquidity_buttons(message: Message):
    """Handle liquidity buttons."""
    if message.text == "السيوله الداخله":
        await process_hv(message, hv_code="in")
    elif message.text == "السيوله الخارجه":
        await process_hv(message, hv_code="out")

# 6. Admin Panel
@dp.message_handler(commands=['admin'])
async def admin_panel(message: Message):
    """Handle /admin command."""
    if message.from_user.id not in ADMIN_IDS:
        await message.reply("You are not authorized to access this panel.")
        return
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton("Manage Users"),
        KeyboardButton("Manage Subscriptions"),
        KeyboardButton("View Logs"),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply("Admin Panel", reply_markup=markup)

@dp.message_handler(lambda message: message.text == "Manage Users")
async def manage_users(message: Message):
    """Handle Manage Users button."""
    if message.from_user.id not in ADMIN_IDS:
        await message.reply("You are not authorized to access this panel.")
        return
    await message.reply("Manage Users functionality will be implemented here.")

@dp.message_handler(lambda message: message.text == "Manage Subscriptions")
async def manage_subscriptions(message: Message):
    """Handle Manage Subscriptions button."""
    if message.from_user.id not in ADMIN_IDS:
        await message.reply("You are not authorized to access this panel.")
        return
    await message.reply("Manage Subscriptions functionality will be implemented here.")

@dp.message_handler(lambda message: message.text == "View Logs")
async def view_logs(message: Message):
    """Handle View Logs button."""
    if message.from_user.id not in ADMIN_IDS:
        await message.reply("You are not authorized to access this panel.")
        return
    await message.reply("View Logs functionality will be implemented here.")

# Define the help function for AI analysis
async def show_ai_analysis_help(message: Message):
    """Display help information for AI stock analysis."""
    help_text = """
🤖 *تحليل ذكي للأسهم*
استخدم الأمر التالي لتحليل سهم معين:
/analyze [كود السهم]
مثال:
/analyze FWRY
سيقوم البوت بتحليل السهم وتقديم توصيات ورؤية مبنية على البيانات المتاحة.
"""
    await message.reply(help_text, parse_mode="Markdown")

# Define handlers for special buttons
async def handle_deals_button(message: Message):
    """Handle the 'Deals' button specifically"""
    await process_deals(message)

async def handle_chart_button(message: Message):
    """Handle the Chart button"""
    await message.reply("Please enter the stock code to view its chart, example: /chart FWRY")

# 7. Callback Message Handler
BUTTON_HANDLERS = {
    "📊تفاصيل اسهم المتاجرة عن اليوم": process_modarba_all,
    "💹اسهم المتاجرة عن اليوم": process_modarba,
    "💰السيوله": liquidity_menu,
    "📈تقارير الأسهم": lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل كود السهم كما في المثال /stock fwry"),
    "🤖تحليل ذكي للأسهم": show_ai_analysis_help,
    "❓المساعدة": process_help,
    "اشتراك / تجديد": subscription_menu,
    "📅اشتراكى": get_subscription_date,
    "🔢مستويات فيبوناتشي": lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل النقاط العالية والمنخفضة كما في المثال /fibo 55 47.6"),
    "👥رابط الإحالة": display_refer_link,
    
    # Use the constants for these problematic buttons
    BUTTON_DEALS: handle_deals_button,
    BUTTON_CHART: handle_chart_button,
    BUTTON_BACK: cmd_menu,
    "مشترك جديد": new_subscription,
    "تجديد اشتراك": renew_subscription,
    "إرسال صورة تأكيد الدفع": request_payment_confirmation,
    "Manage Users": manage_users,
    "Manage Subscriptions": manage_subscriptions,
    "View Logs": view_logs,
}

# Add some debug logging to help identify any future button text mismatches
async def process_callback_messages(message: Message):
    """Handle button callbacks."""
    # First check if the message is a command - let command handlers process it
    if message.text.startswith('/'):
        # Skip commands - let the command handlers process them
        return
        
    # Add detailed debug logging to see exact text and bytes
    button_text = message.text
    logging.info(f"Button pressed: '{button_text}', bytes: {button_text.encode('utf-8')}")
    logging.info(f"Available handlers: {list(BUTTON_HANDLERS.keys())}")
    
    # Then process as a button handler if applicable
    handler = BUTTON_HANDLERS.get(button_text)
    if handler:
        await handler(message)
    else:
        # Try to find a close match
        for key in BUTTON_HANDLERS:
            if key.replace(' ', '') == button_text.replace(' ', ''):
                logging.info(f"Found close match: '{key}' for '{button_text}'")
                await BUTTON_HANDLERS[key](message)
                return
                
        # Debug which buttons aren't matching
        await message.reply(f"Invalid option selected: '{button_text}'")
        logging.warning(f"No handler found for button text: '{button_text}'")

# إضافة معالج جديد للأمر `analyze`
@dp.message_handler(commands=['analyze'])
async def process_analyze_command(message: Message):
    """Enhanced stock analysis command with improved messaging and promo codes"""
    try:
        user_id = message.from_user.id
        
        # فحص إمكانية الاستخدام مع الرسائل التحفيزية الجديدة
        can_use, service_message = can_use_service(user_id)
        
        if not can_use:
            await message.answer(service_message, parse_mode="Markdown")
            return
        
        # إذا كانت هناك رسالة تحفيزية، أرسلها
        if "✅ تم قبول الطلب" in service_message:
            await message.answer(service_message, parse_mode="Markdown")
        
        # الحصول على رمز السهم من الرسالة
        parts = message.text.split()
        if len(parts) != 2:
            await message.answer("الرجاء إدخال رمز السهم بعد الأمر، مثال: /analyze COMI")
            return
            
        stock_code = parts[1].upper()
        
        # الحصول على بيانات السهم
        df = pd.read_excel(file_path)
        stock_data = df[df.iloc[:, 0].astype(str).str.upper() == stock_code]
        
        if stock_data.empty:
            await message.answer(f"لم يتم العثور على بيانات للسهم {stock_code}")
            return
            
        # إخبار المستخدم أن التحليل قيد المعالجة
        processing_message = await message.answer("⏳ جاري تحليل السهم... يرجى الانتظار.")
        
        # استخدام محلل الأسهم لإنتاج التحليل
        subscriber_type, _ = await check_user_limit(message)  # للحصول على نوع الاشتراك
        analysis_text = stock_analyzer.analyze_stock(stock_data)
        
        # إضافة كود خصم للمستخدمين المؤهلين
        enhanced_analysis = add_promo_code_to_message(user_id, analysis_text)
        
        # حذف رسالة المعالجة وإرسال التحليل النهائي
        await processing_message.delete()
        # إرسال التحليل المحسن مع معالجة آمنة للـ parsing
        try:
            await message.answer(enhanced_analysis, parse_mode="Markdown")
        except Exception as parse_error:
            if "parse entities" in str(parse_error).lower():
                logging.warning(f"Markdown parsing failed for analysis, sending as plain text: {parse_error}")
                await message.answer(enhanced_analysis)
            else:
                raise parse_error
        
        # إرسال رسالة تحفيزية إضافية للمستخدمين المجانيين
        if subscriber_type == "free" and ENHANCED_MESSAGING_AVAILABLE:
            conversion_message = send_smart_conversion_message(user_id, "success_stories")
            if conversion_message:
                try:
                    await message.answer(conversion_message, parse_mode="Markdown")
                except Exception as parse_error:
                    if "parse entities" in str(parse_error).lower():
                        logging.warning(f"Markdown parsing failed for conversion message, sending as plain text: {parse_error}")
                        await message.answer(conversion_message)
                    else:
                        logging.error(f"Error sending conversion message: {parse_error}")
        
    except Exception as e:
        logger.error(f"خطأ في معالجة أمر التحليل: {e}")
        await message.answer("حدث خطأ أثناء تحليل السهم. يرجى المحاولة مرة أخرى.")

# Add periodic feature promotion
async def schedule_feature_promotions():
    """Schedule periodic feature promotions to all users"""
    while True:
        try:
            # Get all users from the DB
            users = sheet2.get_all_values()  # Assumes sheet2 has user data
            for user in users[1:]:  # Skip header row
                user_id = user[0]
                subscriber_type = user[2]
                
                # Only send promotions to free users
                if subscriber_type == "free":
                    try:
                        # Send a random feature promotion
                        promo = random.choice(list(FEATURE_PROMOTION.values()))
                        await bot.send_message(
                            chat_id=user_id,
                            text=promo,
                            parse_mode="Markdown"
                        )
                        
                        # Also send a random success story
                        if random.random() < 0.5:  # 50% chance
                            await bot.send_message(
                                chat_id=user_id,
                                text=random.choice(SUCCESS_STORIES),
                                parse_mode="Markdown" 
                            )
                    except Exception as e:
                        logging.error(f"Error sending promotion to user {user_id}: {e}")
                        await asyncio.sleep(1)  # Short pause before next user
                
        except Exception as e:
            logging.error(f"Error in feature promotion schedule: {e}")
            await asyncio.sleep(3600)  # Wait an hour before retrying
            
# Add a specific handler for the "الصفقات" button
@dp.message_handler(lambda message: message.text == BUTTON_DEALS)
async def handle_deals_button(message: Message):
    """Handle the 'Deals' button specifically"""
    await process_deals(message)

# Updating Subscription Messages and Deal Formats

# 1. Create a new constant for the premium-only message in Arabic
PREMIUM_ONLY_MESSAGE = """
⚠️ *هذه الميزة متاحة فقط للمشتركين المميزين* ⚠️

لاستخدام هذه الميزة المتقدمة، يرجى ترقية حسابك إلى الاشتراك المدفوع.

✨ *مزايا الاشتراك المميز:*
• تحليلات غير محدودة للأسهم
• توصيات خاصة عالية الدقة
• تنبيهات فورية للفرص الاستثمارية
• دعم فني متواصل

📲 *للاشتراك:* /subscribe
"""

# 2. Update the deals command to use the new message
@dp.message_handler(commands=['deals'])
async def process_deals(message: Message):
    """Handle /deals command."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)
        
    markup = ReplyKeyboardMarkup(resize_keyboard=True, selective=True).add(
        KeyboardButton("📊 صفقات مفتوحة"),
        KeyboardButton("✅ تحقق الهدف الأول"),
        KeyboardButton("✅ تحقق الهدف الثاني"),
        KeyboardButton("✅ تحقق الهدف الثالث"),
        KeyboardButton(BUTTON_BACK)
    )
    await message.reply("اختر فئة الصفقات:", reply_markup=markup)

# 3. Update the Open Deals handler with enhanced Arabic formatting
@dp.message_handler(lambda message: message.text == "📊 صفقات مفتوحة" or message.text == "📊 Open Deals")
async def process_today_deals(message: Message):
    """Handle open deals."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)
            
    # Use safe function to get records
    try:
        today_deals = get_sheet_records_safe(sheet3)
    except Exception as e:
        logging.error(f"Error getting today deals: {e}")
        await message.reply("❌ حدث خطأ في جلب بيانات الصفقات. يرجى المحاولة لاحقاً.")
        return
        
    filtered_deals = [deal for deal in today_deals if deal.get("الحاله", "").strip().lower() == "buy"]
    
    if not filtered_deals:
        await message.reply("💫 لا توجد صفقات مفتوحة اليوم.")
        return
        
    message_text = "📊 *صفقات اليوم (إشارات شراء)*\n\n"
    
    for deal in filtered_deals:
        try:
            buy_price = float(deal.get('سعر الشراء', 0))
            
            # Calculate profit percentages
            t1_profit_pct = round((float(deal.get('الهدف الأول', 0)) - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            t2_profit_pct = round((float(deal.get('الهدف الثانى', 0)) - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            t3_profit_pct = round((float(deal.get('الهدف الثالث', 0)) - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            sl_loss_pct = round((float(deal.get('وقف الخسارة', 0)) - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            
            message_text += (
                f"🔹 *{deal.get('الكود', 'N/A')} - {deal.get('ألأسم', 'N/A')}*\n"
                f"   💰 *سعر الشراء:* {buy_price}\n"
                f"   📅 *تاريخ الشراء:* {deal.get('تاريخ الشراء', 'N/A')}\n"
                f"   🎯 *الهدف الأول:* {deal.get('الهدف الأول', 'N/A')} (ربح {t1_profit_pct}%)\n"
                f"   🎯 *الهدف الثاني:* {deal.get('الهدف الثانى', 'N/A')} (ربح {t2_profit_pct}%)\n"
                f"   🎯 *الهدف الثالث:* {deal.get('الهدف الثالث', 'N/A')} (ربح {t3_profit_pct}%)\n"
                f"   🛑 *وقف الخسارة:* {deal.get('وقف الخسارة', 'N/A')} (خسارة {abs(sl_loss_pct)}%)\n\n"
            )
        except Exception as e:
            logging.error(f"Error processing deal: {e}")
            continue
        
    await send_long_message(message.chat.id, message_text, parse_mode="Markdown")

# 4. Update the T1 Achieved handler with enhanced Arabic formatting
@dp.message_handler(lambda message: message.text == "✅ تحقق الهدف الأول" or message.text == "✅ T1 Achieved")
async def process_t1_achieved(message: Message):
    """Handle T1 achieved deals."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)
    
    # Use safe function to get records    
    try:
        today_deals = get_sheet_records_safe(sheet3)
    except Exception as e:
        logging.error(f"Error getting T1 achieved deals: {e}")
        await message.reply("❌ حدث خطأ في جلب بيانات الصفقات. يرجى المحاولة لاحقاً.")
        return
        
    filtered_deals = [deal for deal in today_deals if deal.get("الحاله", "").strip().lower() == "t1done"]
    
    if not filtered_deals:
        await message.reply("💫 لا توجد صفقات حققت الهدف الأول.")
        return
        
    message_text = "✅ *صفقات حققت الهدف الأول:*\n\n"
    
    for deal in filtered_deals:
        try:
            buy_price = float(deal.get('سعر الشراء', 0))
            t1_price = float(deal.get('الهدف الأول', 0))
            t1_profit_pct = round((t1_price - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            
            # Calculate days to achieve target
            from datetime import datetime
            buy_date_str = deal.get('تاريخ الشراء', '')
            # Try different possible column names for T1 date
            t1_date_str = (deal.get('تاريخ الهدف الأول', '') or 
                          deal.get('تاريخ الهدف أول', '') or 
                          deal.get('تاريخ الهدف1', '') or
                          deal.get('تاريخ_الهدف_الأول', ''))
            
            try:
                buy_date = datetime.strptime(buy_date_str, '%m/%d/%Y') if '/' in buy_date_str else datetime.strptime(buy_date_str, '%Y-%m-%d')
                t1_date = datetime.strptime(t1_date_str, '%m/%d/%Y') if '/' in t1_date_str else datetime.strptime(t1_date_str, '%Y-%m-%d')
                days_to_achieve = (t1_date - buy_date).days
            except (ValueError, TypeError):
                days_to_achieve = 0
                logging.warning(f"Date parsing error for T1 deal {deal.get('الكود', 'N/A')}")
            
            message_text += (
                f"🔹 *{deal.get('الكود', 'N/A')} - {deal.get('ألأسم', 'N/A')}*\n"
                f"   💰 *سعر الشراء:* {buy_price}\n"
                f"   🎯 *الهدف الأول:* {t1_price} (ربح {t1_profit_pct}%)\n"
                f"   📆 *تاريخ تحقق الهدف:* {t1_date_str or 'N/A'}\n"
                f"   ⏱ *المدة:* {days_to_achieve} يوم\n\n"
            )
        except Exception as e:
            logging.error(f"Error processing T1 deal: {e}")
            continue
        
    await send_long_message(message.chat.id, message_text, parse_mode="Markdown")

# 5. Update the T2 Achieved handler with enhanced Arabic formatting
@dp.message_handler(lambda message: message.text == "✅ تحقق الهدف الثاني" or message.text == "✅ T2 Achieved")
async def process_t2_achieved(message: Message):
    """Handle T2 achieved deals."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)
    
    # Use safe function to get records    
    try:
        today_deals = get_sheet_records_safe(sheet3)
    except Exception as e:
        logging.error(f"Error getting T2 achieved deals: {e}")
        await message.reply("❌ حدث خطأ في جلب بيانات الصفقات. يرجى المحاولة لاحقاً.")
        return
        
    filtered_deals = [deal for deal in today_deals if deal.get("الحاله", "").strip().lower() == "t2done"]
    
    if not filtered_deals:
        await message.reply("💫 لا توجد صفقات حققت الهدف الثاني.")
        return
        
    message_text = "✅✅ *صفقات حققت الهدف الثاني:*\n\n"
    
    for deal in filtered_deals:
        try:
            buy_price = float(deal.get('سعر الشراء', 0))
            t2_price = float(deal.get('الهدف الثانى', 0))
            t2_profit_pct = round((t2_price - buy_price) / buy_price * 100, 2) if buy_price > 0 else 0
            
            # Calculate days to achieve target
            from datetime import datetime
            buy_date_str = deal.get('تاريخ الشراء', '')
            t2_date_str = deal.get('تاريخ الهدف الثانى', '')
            
            try:
                buy_date = datetime.strptime(buy_date_str, '%m/%d/%Y') if '/' in buy_date_str else datetime.strptime(buy_date_str, '%Y-%m-%d')
                t2_date = datetime.strptime(t2_date_str, '%m/%d/%Y') if '/' in t2_date_str else datetime.strptime(t2_date_str, '%Y-%m-%d')
                days_to_achieve = (t2_date - buy_date).days
            except ValueError:
                days_to_achieve = 0
                logging.warning(f"Date parsing error for deal {deal.get('الكود', 'N/A')}")
            
            message_text += (
                f"🔹 *{deal.get('الكود', 'N/A')} - {deal.get('ألأسم', 'N/A')}*\n"
                f"   💰 *سعر الشراء:* {buy_price}\n"
                f"   🎯 *الهدف الثاني:* {t2_price} (ربح {t2_profit_pct}%)\n"
                f"   📆 *تاريخ تحقق الهدف:* {deal.get('تاريخ الهدف الثانى', 'N/A')}\n"
                f"   ⏱ *المدة:* {days_to_achieve} يوم\n\n"
            )
        except Exception as e:
            logging.error(f"Error processing T2 deal: {e}")
            continue
        
    await send_long_message(message.chat.id, message_text, parse_mode="Markdown")

# 6. Update the T3 Achieved handler with enhanced Arabic formatting
@dp.message_handler(lambda message: message.text == "✅ تحقق الهدف الثالث" or message.text == "✅ T3 Achieved")
async def process_t3_achieved(message: Message):
    """Handle T3 achieved deals."""
    # Check subscription access first without updating count
    if not await check_subscription_access(message):
        return
    
    # Now check user limit and update count
    subscriber_type, count = await check_user_limit(message)
        
    # Use safe function to get records
    try:
        today_deals = get_sheet_records_safe(sheet3)
    except Exception as e:
        logging.error(f"Error getting T3 achieved deals: {e}")
        await message.reply("❌ حدث خطأ في جلب بيانات الصفقات. يرجى المحاولة لاحقاً.")
        return
        
    filtered_deals = [deal for deal in today_deals if deal.get("الحاله", "").strip().lower() == "t3done"]
    
    if not filtered_deals:
        await message.reply("💫 لا توجد صفقات حققت الهدف الثالث.")
        return
        
    message_text = "✅✅✅ *صفقات حققت الهدف الثالث:*\n\n"
    
    for deal in filtered_deals:
        buy_price = float(deal['سعر الشراء'])
        t3_price = float(deal['الهدف الثالث'])
        t3_profit_pct = round((t3_price - buy_price) / buy_price * 100, 2)
        
        # Calculate days to achieve target
        from datetime import datetime
        buy_date = datetime.strptime(deal['تاريخ الشراء'], '%m/%d/%Y') if '/' in deal['تاريخ الشراء'] else datetime.strptime(deal['تاريخ الشراء'], '%Y-%m-%d')
        t3_date = datetime.strptime(deal['تاريخ الهدف الثالث'], '%m/%d/%Y') if '/' in deal['تاريخ الهدف الثالث'] else datetime.strptime(deal['تاريخ الهدف الثالث'], '%Y-%m-%d')
        days_to_achieve = (t3_date - buy_date).days
        
        message_text += (
            f"🔹 *{deal['الكود']} - {deal['ألأسم']}*\n"
            f"   💰 *سعر الشراء:* {buy_price}\n"
            f"   🎯 *الهدف الثالث:* {t3_price} (ربح {t3_profit_pct}%)\n"
            f"   📆 *تاريخ تحقق الهدف:* {deal['تاريخ الهدف الثالث']}\n"
            f"   ⏱ *المدة:* {days_to_achieve} يوم\n\n"
        )
        
    await send_long_message(message.chat.id, message_text, parse_mode="Markdown")

# 7. Update the BUTTON_HANDLERS dictionary to include the Arabic button labels
BUTTON_HANDLERS = {
    BUTTON_MODARBA_ALL: process_modarba_all,
    BUTTON_MODARBA: process_modarba,
    BUTTON_LIQUIDITY: liquidity_menu,
    BUTTON_REPORTS: lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل كود السهم كما في المثال /stock fwry"),
    BUTTON_AI_ANALYSIS: show_ai_analysis_help,
    BUTTON_HELP: process_help,
    BUTTON_SUBSCRIBE: subscription_menu,
    BUTTON_MY_SUBSCRIPTION: get_subscription_date,
    BUTTON_FIBO: lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل النقاط العالية والمنخفضة كما في المثال /fibo 55 47.6"),
    BUTTON_REFER_LINK: display_refer_link,
    BUTTON_OPEN_DEALS: process_today_deals,
    BUTTON_T1_ACHIEVED: process_t1_achieved,
    BUTTON_T2_ACHIEVED: process_t2_achieved,
    BUTTON_T3_ACHIEVED: process_t3_achieved,
    BUTTON_BACK: cmd_menu,
    BUTTON_NEW_SUBSCRIPTION: new_subscription,
    BUTTON_RENEW_SUBSCRIPTION: renew_subscription,
    BUTTON_PAYMENT_CONFIRMATION: request_payment_confirmation,
    BUTTON_INCOMING_LIQUIDITY: lambda message: process_hv(message, hv_code="in"),
    BUTTON_OUTGOING_LIQUIDITY: lambda message: process_hv(message, hv_code="out"),
    "Manage Users": manage_users,
    "Manage Subscriptions": manage_subscriptions,
    "View Logs": view_logs,
    
    # Legacy button labels for backward compatibility
    "📊تفاصيل اسهم المتاجرة عن اليوم": process_modarba_all,
    "💹اسهم المتاجرة عن اليوم": process_modarba,
    "💰السيوله": liquidity_menu,
    "📈تقارير الأسهم": lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل كود السهم كما في المثال /stock fwry"),
    "🤖تحليل ذكي للأسهم": show_ai_analysis_help,
    "❓المساعدة": process_help,
    "اشتراك / تجديد": subscription_menu,
    "📅اشتراكى": get_subscription_date,
    "🔢مستويات فيبوناتشي": lambda message: bot.send_message(message.chat.id, "من فضلك، أدخل النقاط العالية والمنخفضة كما في المثال /fibo 55 47.6"),
    "👥رابط الإحالة": display_refer_link,
    "📊 Open Deals": process_today_deals,
    "✅ T1 Achieved": process_t1_achieved,
    "✅ T2 Achieved": process_t2_achieved,
    "✅ T3 Achieved": process_t3_achieved,
    "📊 صفقات مفتوحة": process_today_deals,
    "✅ تحقق الهدف الأول": process_t1_achieved,
    "✅ تحقق الهدف الثاني": process_t2_achieved,
    "✅ تحقق الهدف الثالث": process_t3_achieved,
    "مشترك جديد": new_subscription,
    "تجديد اشتراك": renew_subscription,
    "إرسال صورة تأكيد الدفع": request_payment_confirmation,
    "السيوله الداخله": lambda message: process_hv(message, hv_code="in"),
    "السيوله الخارجه": lambda message: process_hv(message, hv_code="out"),
    "🔙 العودة للقائمة الرئيسية": cmd_menu,
}

# ===== دوال الرسائل التحفيزية الجديدة =====

def can_use_service(user_id):
    """فحص إمكانية استخدام الخدمة مع رسائل تحفيزية محسنة"""
    if not ENHANCED_MESSAGING_AVAILABLE:
        # النظام القديم
        return True, "ضمن الحد المسموح"
    
    try:
        # فحص وجود الدوال المطلوبة
        if not hasattr(UserManager, 'get_user_data'):
            return True, "نظام إدارة المستخدمين غير متاح"
            
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data:
            return False, "لم يتم العثور على بيانات المستخدم"
        
        subscription_type = user_data.get('subscription_type', 'free')
        
        if subscription_type in ['paid', 'trail']:
            # فحص وجود دالة is_subscription_active
            if hasattr(UserManager, 'is_subscription_active'):
                if UserManager.is_subscription_active(user_data):
                    return True, "مستخدم مدفوع نشط"
                else:
                    # رسالة تحفيزية لتجديد الاشتراك
                    if hasattr(UserManager, 'send_upgrade_message'):
                        renewal_message = UserManager.send_upgrade_message(str(user_id), "daily_limit_reached")
                        return False, renewal_message
                    else:
                        return False, "انتهت صلاحية الاشتراك"
            else:
                # استخدام نظام بديل للتحقق من تاريخ الانتهاء
                import datetime
                end_date_str = user_data.get('end_date', '')
                if end_date_str:
                    try:
                        end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                        if datetime.date.today() <= end_date:
                            return True, "مستخدم مدفوع نشط"
                    except ValueError:
                        pass
                return False, "انتهت صلاحية الاشتراك"
        
        # مستخدم مجاني - فحص الحد اليومي
        if hasattr(UserManager, 'can_user_make_request'):
            if UserManager.can_user_make_request(str(user_id)):
                # فحص إرسال رسائل تحفيزية حسب الاستخدام
                if hasattr(UserManager, 'get_user_daily_count'):
                    current_count = UserManager.get_user_daily_count(str(user_id))
                    if hasattr(UserManager, 'check_and_send_conversion_message'):
                        conversion_message = UserManager.check_and_send_conversion_message(str(user_id), current_count)
                        if conversion_message and current_count >= 3:
                            return True, f"✅ تم قبول الطلب\n\n{conversion_message}"
                
                return True, "ضمن الحد المسموح"
            else:
                # رسالة تحفيزية عند الوصول للحد الأقصى
                if hasattr(UserManager, 'send_upgrade_message'):
                    upgrade_message = UserManager.send_upgrade_message(str(user_id), "daily_limit_reached")
                    return False, upgrade_message
                else:
                    return False, "تم الوصول للحد الأقصى من الاستخدام المجاني"
        else:
            # استخدام النظام القديم
            return True, "ضمن الحد المسموح"
            
    except Exception as e:
        logger.error(f"خطأ في فحص إمكانية الاستخدام: {e}")
        return True, "تم قبول الطلب"

def add_promo_code_to_message(user_id, original_message):
    """إضافة كود خصم إلى رسالة التحليل إذا كان مناسباً"""
    if not ENHANCED_MESSAGING_AVAILABLE:
        return original_message
        
    try:
        # فحص وجود الدوال المطلوبة
        if not hasattr(UserManager, 'get_user_data'):
            return original_message
            
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data:
            return original_message
            
        subscription_type = user_data.get('subscription_type', 'free')
        
        # فحص وجود دالة get_user_daily_count
        if hasattr(UserManager, 'get_user_daily_count'):
            daily_count = UserManager.get_user_daily_count(str(user_id))
        else:
            daily_count = user_data.get('count', 0)
        
        # إضافة كود خصم للمستخدمين المجانيين النشطين
        if subscription_type == 'free' and daily_count >= 3:
            promo_footer = """

🎁 **عرض خاص لك:**
🎫 كود خصم 30%: `ACTIVE30`
💎 استخدم: `/redeem ACTIVE30`
            """
            return original_message + promo_footer
            
        return original_message
        
    except Exception as e:
        logger.error(f"خطأ في إضافة كود الخصم: {e}")
        return original_message

def send_smart_conversion_message(user_id, context="general"):
    """إرسال رسائل تحفيزية ذكية حسب السياق"""
    if not ENHANCED_MESSAGING_AVAILABLE:
        return ""
        
    try:
        # فحص وجود CONVERSION_CAMPAIGNS
        if 'CONVERSION_CAMPAIGNS' in globals():
            # اختيار الرسالة حسب السياق
            if context == "market_opportunity":
                return CONVERSION_CAMPAIGNS.get("market_opportunity", "")
            elif context == "success_stories":
                return CONVERSION_CAMPAIGNS.get("success_stories", "")
            elif context == "social_proof":
                return CONVERSION_CAMPAIGNS.get("social_proof", "")
        
        # رسالة عامة تحفيزية
        if hasattr(UserManager, 'send_upgrade_message'):
            return UserManager.send_upgrade_message(str(user_id), "conversion_opportunity")
        else:
            return "للحصول على ميزات إضافية، يرجى الاشتراك: /subscribe"
            return UserManager.send_upgrade_message(str(user_id), "conversion_opportunity")
            
    except Exception as e:
        logger.error(f"خطأ في إرسال رسالة تحفيزية ذكية: {e}")
        return ""


