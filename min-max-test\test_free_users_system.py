#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المشتركين المجانيين وإرسال أكواد البرومو
Test Free Users Management and Promo Code Distribution System
"""

import sys
import os
import asyncio

def test_imports():
    """اختبار استيراد جميع المكونات المطلوبة"""
    print("🧪 اختبار استيراد المكونات...")
    
    try:
        from free_users_manager import (
            FreeUsersManager,
            list_free_users_command,
            send_promo_to_active_command,
            send_promo_to_all_command
        )
        print("✅ تم استيراد free_users_manager بنجاح")
        
        from promo_codes import PromoCodeManager
        print("✅ تم استيراد PromoCodeManager بنجاح")
        
        from user_limit import UserManager
        print("✅ تم استيراد UserManager بنجاح")
        
        return True
    except ImportError as e:
        print(f"❌ فشل في الاستيراد: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_free_users_detection():
    """اختبار اكتشاف المشتركين المجانيين"""
    print("\n🧪 اختبار اكتشاف المشتركين المجانيين...")
    
    try:
        from free_users_manager import FreeUsersManager
        
        # جلب جميع المستخدمين المجانيين
        all_free = FreeUsersManager.get_all_free_users()
        print(f"✅ تم العثور على {len(all_free)} مستخدم مجاني")
        
        # جلب المستخدمين النشطين
        active_free = FreeUsersManager.get_active_free_users()
        print(f"✅ تم العثور على {len(active_free)} مستخدم نشط")
        
        # عرض عينة من البيانات
        if all_free:
            sample = all_free[0]
            print(f"📋 عينة من البيانات:")
            print(f"   - معرف المستخدم: {sample.get('user_id', 'غير محدد')}")
            print(f"   - نوع الاشتراك: {sample.get('subscription_type', 'غير محدد')}")
            print(f"   - عدد الاستخدامات: {sample.get('count', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اكتشاف المستخدمين: {e}")
        return False

def test_statistics():
    """اختبار حساب الإحصائيات"""
    print("\n🧪 اختبار حساب الإحصائيات...")
    
    try:
        from free_users_manager import FreeUsersManager
        
        stats = FreeUsersManager.get_free_users_statistics()
        
        if stats:
            print("✅ تم حساب الإحصائيات بنجاح:")
            print(f"   📊 إجمالي المستخدمين المجانيين: {stats.get('total_free_users', 0)}")
            print(f"   🟢 المستخدمين النشطين: {stats.get('active_free_users', 0)}")
            print(f"   📈 معدل النشاط: {stats.get('activity_rate', 0)}%")
            print(f"   👤 لم يستخدموا البوت: {stats.get('never_used', 0)}")
            print(f"   📱 استخدام خفيف: {stats.get('light_users', 0)}")
            print(f"   🔄 استخدام منتظم: {stats.get('regular_users', 0)}")
            print(f"   🚀 استخدام كثيف: {stats.get('heavy_users', 0)}")
            print(f"   ⏰ اشتراكات منتهية: {stats.get('expired_users', 0)}")
            return True
        else:
            print("❌ فشل في حساب الإحصائيات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في حساب الإحصائيات: {e}")
        return False

def test_bulk_code_creation():
    """اختبار إنشاء أكواد مجمعة"""
    print("\n🧪 اختبار إنشاء أكواد مجمعة...")
    
    try:
        from free_users_manager import FreeUsersManager
        
        # إنشاء 3 أكواد للاختبار
        codes = FreeUsersManager.create_bulk_trial_codes(
            count=3,
            days=7,
            note="اختبار النظام"
        )
        
        if codes and len(codes) == 3:
            print(f"✅ تم إنشاء {len(codes)} كود بنجاح:")
            for i, code in enumerate(codes, 1):
                print(f"   {i}. {code}")
            return True
        else:
            print(f"❌ فشل في إنشاء الأكواد - تم إنشاء {len(codes) if codes else 0} من 3")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأكواد: {e}")
        return False

def test_help_message_update():
    """اختبار تحديث رسالة المساعدة"""
    print("\n🧪 اختبار تحديث رسالة المساعدة...")
    
    try:
        from arabic_messages import COMPREHENSIVE_HELP
        
        # التحقق من وجود شرح أمر /redeem
        if '/redeem' in COMPREHENSIVE_HELP:
            print("✅ تم إضافة شرح أمر /redeem في رسالة المساعدة")
            
            # عرض الجزء المتعلق بأكواد الخصم
            lines = COMPREHENSIVE_HELP.split('\n')
            redeem_section = []
            in_redeem_section = False
            
            for line in lines:
                if 'أكواد الخصم' in line or '/redeem' in line:
                    in_redeem_section = True
                elif in_redeem_section and line.strip().startswith('•'):
                    redeem_section.append(line)
                elif in_redeem_section and not line.strip().startswith('•') and line.strip():
                    if not line.startswith('  '):
                        break
                    redeem_section.append(line)
                
                if in_redeem_section:
                    redeem_section.append(line)
                    if len(redeem_section) > 10:  # حد أقصى للعرض
                        break
            
            if redeem_section:
                print("📋 قسم أكواد الخصم في المساعدة:")
                for line in redeem_section[:8]:  # عرض أول 8 أسطر
                    print(f"   {line}")
            
            return True
        else:
            print("❌ لم يتم العثور على شرح أمر /redeem في رسالة المساعدة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص رسالة المساعدة: {e}")
        return False

def test_command_registration():
    """اختبار تسجيل الأوامر الجديدة"""
    print("\n🧪 اختبار تسجيل الأوامر الجديدة...")
    
    try:
        # محاكاة dispatcher
        class MockDispatcher:
            def __init__(self):
                self.registered_commands = []
                
            def register_message_handler(self, handler, commands=None, **kwargs):
                if commands:
                    for cmd in commands:
                        self.registered_commands.append(cmd)
                        print(f"   📝 تم تسجيل الأمر: /{cmd}")
                return True
        
        mock_dp = MockDispatcher()
        
        # اختبار تسجيل الأوامر
        from promo_commands import register_promo_commands
        success = register_promo_commands(mock_dp)
        
        if success:
            print("✅ تم تسجيل الأوامر بنجاح")
            
            # التحقق من الأوامر المطلوبة
            required_commands = [
                'create_trial_code', 'create_discount_code', 'redeem', 
                'list_codes', 'test_promo', 'list_free_users', 
                'send_promo_active', 'send_promo_all'
            ]
            
            missing_commands = []
            for cmd in required_commands:
                if cmd not in mock_dp.registered_commands:
                    missing_commands.append(cmd)
            
            if not missing_commands:
                print("✅ جميع الأوامر المطلوبة مسجلة")
                return True
            else:
                print(f"⚠️ أوامر مفقودة: {missing_commands}")
                return False
        else:
            print("❌ فشل في تسجيل الأوامر")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تسجيل الأوامر: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار نظام إدارة المشتركين المجانيين وأكواد البرومو")
    print("=" * 70)
    
    tests = [
        ("استيراد المكونات", test_imports),
        ("اكتشاف المشتركين المجانيين", test_free_users_detection),
        ("حساب الإحصائيات", test_statistics),
        ("إنشاء أكواد مجمعة", test_bulk_code_creation),
        ("تحديث رسالة المساعدة", test_help_message_update),
        ("تسجيل الأوامر الجديدة", test_command_registration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 نتائج الاختبار:")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام إدارة المشتركين المجانيين جاهز للاستخدام")
        print("\n📋 الأوامر الجديدة المتاحة:")
        print("   👨‍💼 للإدارة:")
        print("     - /list_free_users - عرض إحصائيات المشتركين المجانيين")
        print("     - /send_promo_active - إرسال أكواد للمستخدمين النشطين")
        print("     - /send_promo_all - إرسال أكواد لجميع المستخدمين المجانيين")
        print("   👤 للمستخدمين:")
        print("     - /redeem كود_البرومو - تفعيل كود خصم أو تجربة مجانية")
        print("     - /help - شرح محدث يتضمن أكواد الخصم")
    else:
        print(f"\n⚠️ {total - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
