#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة إنشاء أكواد البرومو
Diagnose promo code creation issues
"""

import logging
import time

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_single_code_creation():
    """اختبار إنشاء كود واحد"""
    try:
        print("🧪 اختبار إنشاء كود واحد...")
        
        from promo_codes import PromoCodeManager
        
        # محاولة إنشاء كود واحد
        code = PromoCodeManager.create_trial_code(
            days=7,
            expiry_days=30,
            note="اختبار إنشاء كود واحد"
        )
        
        if code:
            print(f"✅ تم إنشاء الكود بنجاح: {code}")
            return True
        else:
            print("❌ فشل في إنشاء الكود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الكود: {e}")
        return False

def test_multiple_codes_creation():
    """اختبار إنشاء عدة أكواد"""
    try:
        print("\n🧪 اختبار إنشاء عدة أكواد...")
        
        from promo_codes import PromoCodeManager
        
        test_counts = [3, 5, 10]
        results = {}
        
        for count in test_counts:
            print(f"\n   📊 اختبار إنشاء {count} أكواد:")
            
            codes = []
            failed = 0
            
            start_time = time.time()
            
            for i in range(count):
                try:
                    code = PromoCodeManager.create_trial_code(
                        days=7,
                        expiry_days=30,
                        note=f"اختبار مجمع - {i+1}"
                    )
                    
                    if code:
                        codes.append(code)
                        print(f"      ✅ الكود {i+1}: {code}")
                    else:
                        failed += 1
                        print(f"      ❌ فشل في إنشاء الكود {i+1}")
                        
                except Exception as e:
                    failed += 1
                    print(f"      ❌ خطأ في إنشاء الكود {i+1}: {e}")
                
                # تأخير قصير بين الأكواد
                time.sleep(0.1)
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_rate = (len(codes) / count * 100) if count > 0 else 0
            
            results[count] = {
                'requested': count,
                'created': len(codes),
                'failed': failed,
                'success_rate': success_rate,
                'duration': duration
            }
            
            print(f"   📊 النتيجة: {len(codes)}/{count} نجح ({success_rate:.1f}%) في {duration:.2f} ثانية")
        
        return results
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأكواد المتعددة: {e}")
        return {}

def test_bulk_creation_function():
    """اختبار دالة الإنشاء المجمع"""
    try:
        print("\n🧪 اختبار دالة الإنشاء المجمع...")
        
        from free_users_manager import FreeUsersManager
        
        test_counts = [3, 7, 15]
        
        for count in test_counts:
            print(f"\n   📊 اختبار إنشاء {count} أكواد مجمعة:")
            
            start_time = time.time()
            
            codes = FreeUsersManager.create_bulk_trial_codes(
                count=count,
                days=7,
                note=f"اختبار مجمع - {count} أكواد"
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_rate = (len(codes) / count * 100) if count > 0 else 0
            
            print(f"   📊 النتيجة: {len(codes)}/{count} نجح ({success_rate:.1f}%) في {duration:.2f} ثانية")
            
            if len(codes) > 0:
                print(f"   📝 عينة من الأكواد:")
                for i, code in enumerate(codes[:3], 1):
                    print(f"      {i}. {code}")
                if len(codes) > 3:
                    print(f"      ... و {len(codes) - 3} أكواد أخرى")
            
            if len(codes) < count:
                print(f"   ⚠️ تم إنشاء {len(codes)} كود فقط من أصل {count} مطلوب")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدالة المجمعة: {e}")
        return False

def test_promo_sheet_access():
    """اختبار الوصول لجدول أكواد البرومو"""
    try:
        print("\n🧪 اختبار الوصول لجدول أكواد البرومو...")
        
        from promo_codes import PromoCodeManager
        
        # محاولة الوصول للجدول
        try:
            sheet = PromoCodeManager.promo_sheet
            print("✅ تم الوصول لجدول أكواد البرومو")
            
            # محاولة قراءة البيانات
            records = sheet.get_all_records()
            print(f"✅ تم قراءة {len(records)} سجل من الجدول")
            
            # محاولة إضافة سجل اختبار
            test_row = [
                "TEST123",
                "trail",
                7,
                "2025-07-23",
                "اختبار الوصول",
                "غير مستخدم",
                "",
                "",
                ""
            ]
            
            sheet.append_row(test_row)
            print("✅ تم إضافة سجل اختبار بنجاح")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الوصول للجدول: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الوصول: {e}")
        return False

def diagnose_promo_system():
    """تشخيص شامل لنظام أكواد البرومو"""
    try:
        print("\n🔍 تشخيص شامل لنظام أكواد البرومو...")
        
        # فحص إعدادات النظام
        try:
            from promo_codes import PROMO_CODES_AVAILABLE
            print(f"📋 نظام أكواد البرومو متاح: {PROMO_CODES_AVAILABLE}")
        except ImportError:
            print("❌ لا يمكن الوصول لإعدادات أكواد البرومو")
        
        # فحص الاتصال بـ Google Sheets
        try:
            from auth import open_google_sheet
            sheet_name = "stock"
            sheet, sheet2, sheet3 = open_google_sheet(sheet_name)
            print("✅ الاتصال بـ Google Sheets يعمل")
        except Exception as e:
            print(f"❌ مشكلة في الاتصال بـ Google Sheets: {e}")
        
        # فحص PromoCodeManager
        try:
            from promo_codes import PromoCodeManager
            print("✅ تم استيراد PromoCodeManager")
        except Exception as e:
            print(f"❌ مشكلة في استيراد PromoCodeManager: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص مشكلة إنشاء أكواد البرومو")
    print("=" * 60)
    
    tests = [
        ("تشخيص النظام", diagnose_promo_system),
        ("اختبار الوصول للجدول", test_promo_sheet_access),
        ("اختبار إنشاء كود واحد", test_single_code_creation),
        ("اختبار إنشاء أكواد متعددة", test_multiple_codes_creation),
        ("اختبار الدالة المجمعة", test_bulk_creation_function),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج التشخيص:")
    
    passed = 0
    for test_name, result in results:
        if isinstance(result, dict):
            # نتائج مفصلة
            print(f"📊 {test_name}: تم بنجاح")
            passed += 1
        elif result:
            print(f"✅ {test_name}: نجح")
            passed += 1
        else:
            print(f"❌ {test_name}: فشل")
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed >= len(results) - 1:  # نجاح معظم الاختبارات
        print("\n🎉 نظام أكواد البرومو يعمل بشكل عام!")
        print("\n💡 التوصيات:")
        print("   - النظام قادر على إنشاء الأكواد")
        print("   - قد تحدث أخطاء عرضية في الإنشاء")
        print("   - النظام المحدث يتعامل مع الأخطاء بمرونة")
        print("   - يمكن المتابعة مع الإرسال للمستخدمين")
    else:
        print(f"\n⚠️ توجد مشاكل في النظام")
        print("💡 يُنصح بمراجعة:")
        print("   - اتصال Google Sheets")
        print("   - صلاحيات الوصول للجداول")
        print("   - إعدادات نظام أكواد البرومو")

if __name__ == "__main__":
    main()
