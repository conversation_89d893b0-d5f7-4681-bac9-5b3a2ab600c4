import numpy as np
import pandas as pd
import logging

# Configure logger
logger = logging.getLogger(__name__)

def identify_elliott_wave_pattern(df, window=120, min_points=5):
    """
    تحديد أنماط موجات إليوت في بيانات السعر
    
    Args:
        df: إطار البيانات مع أسعار OHLC
        window: عدد الشموع للتحليل
        min_points: الحد الأدنى لعدد النقاط المطلوبة
        
    Returns:
        dict: معلومات عن نمط موجات إليوت المكتشف أو None
    """
    if len(df) < window:
        return None
    
    # استخدام الفترة الأخيرة من البيانات
    df_window = df.iloc[-window:]
    
    # تحديد القمم والقيعان المحلية
    swing_threshold = 3  # عدد الشموع للنظر في كل اتجاه
    
    swing_points = []
    for i in range(swing_threshold, len(df_window) - swing_threshold):
        # القمم المحلية
        if df_window['HIGH'].iloc[i] == df_window['HIGH'].iloc[i-swing_threshold:i+swing_threshold+1].max():
            swing_points.append({
                'type': 'high',
                'price': df_window['HIGH'].iloc[i],
                'index': df_window.index[i]
            })
        # القيعان المحلية
        elif df_window['LOW'].iloc[i] == df_window['LOW'].iloc[i-swing_threshold:i+swing_threshold+1].min():
            swing_points.append({
                'type': 'low',
                'price': df_window['LOW'].iloc[i],
                'index': df_window.index[i]
            })
    
    # نحتاج على الأقل 5 نقاط لتمثيل الموجات الخمس
    if len(swing_points) < min_points:
        return None
    
    # البحث عن تسلسل يشبه نمط موجات إليوت الأساسي
    # ترتيب النقاط - يجب أن تكون موجات 1، 3، 5 في نفس الاتجاه، و2، 4 في الاتجاه المعاكس
    
    possible_patterns = []
    
    for i in range(len(swing_points) - 4):
        wave1_type = swing_points[i]['type']
        wave2_type = swing_points[i+1]['type']
        
        # يجب أن تكون الموجة 2 في الاتجاه المعاكس للموجة 1
        if wave1_type == wave2_type:
            continue
        
        wave3_type = swing_points[i+2]['type']
        
        # الموجة 3 يجب أن تكون في نفس اتجاه الموجة 1
        if wave1_type != wave3_type:
            continue
        
        wave4_type = swing_points[i+3]['type']
        
        # الموجة 4 يجب أن تكون في نفس اتجاه الموجة 2
        if wave2_type != wave4_type:
            continue
        
        wave5_type = swing_points[i+4]['type']
        
        # الموجة 5 يجب أن تكون في نفس اتجاه الموجة 1 و3
        if wave1_type != wave5_type:
            continue
        
        # استخراج الأسعار للموجات
        wave1_price = swing_points[i]['price']
        wave2_price = swing_points[i+1]['price']
        wave3_price = swing_points[i+2]['price']
        wave4_price = swing_points[i+3]['price']
        wave5_price = swing_points[i+4]['price']
        
        # التحقق من القواعد الأساسية لموجات إليوت
        is_bullish = wave1_type == 'low'  # نموذج صعودي إذا بدأ بقاع (موجة 1 هي قاع)

        # في النموذج الصعودي:
        if is_bullish:
            # الموجة 2 لا يجب أن تتجاوز بداية الموجة 1
            if wave2_price <= wave1_price:
                # الموجة 3 يجب أن تكون أعلى من الموجة 1
                if wave3_price > wave1_price:
                    # الموجة 4 لا يجب أن تتداخل مع الموجة 1
                    if wave4_price > wave1_price:
                        # الموجة 5 يجب أن تكون أعلى من الموجة 3 (الموجة 3 هي الأطول غالبًا)
                        # لكن الموجة 5 يمكن أن تكون أقصر في بعض الحالات
                        pattern_confidence = 0.7
                        if wave5_price < wave3_price:
                            pattern_confidence = 0.5
                        
                        possible_patterns.append({
                            'direction': 'صعودي',
                            'start_index': swing_points[i]['index'],
                            'end_index': swing_points[i+4]['index'],
                            'waves': [
                                {'wave': 1, 'price': wave1_price, 'index': swing_points[i]['index']},
                                {'wave': 2, 'price': wave2_price, 'index': swing_points[i+1]['index']},
                                {'wave': 3, 'price': wave3_price, 'index': swing_points[i+2]['index']},
                                {'wave': 4, 'price': wave4_price, 'index': swing_points[i+3]['index']},
                                {'wave': 5, 'price': wave5_price, 'index': swing_points[i+4]['index']}
                            ],
                            'confidence': pattern_confidence
                        })
        # في النموذج الهبوطي:
        else:
            # الموجة 2 لا يجب أن تتجاوز بداية الموجة 1
            if wave2_price >= wave1_price:
                # الموجة 3 يجب أن تكون أدنى من الموجة 1
                if wave3_price < wave1_price:
                    # الموجة 4 لا يجب أن تتداخل مع الموجة 1
                    if wave4_price < wave1_price:
                        # الموجة 5 يجب أن تكون أدنى من الموجة 3 (الموجة 3 هي الأطول غالبًا)
                        # لكن الموجة 5 يمكن أن تكون أقصر في بعض الحالات
                        pattern_confidence = 0.7
                        if wave5_price > wave3_price:
                            pattern_confidence = 0.5
                        
                        possible_patterns.append({
                            'direction': 'هبوطي',
                            'start_index': swing_points[i]['index'],
                            'end_index': swing_points[i+4]['index'],
                            'waves': [
                                {'wave': 1, 'price': wave1_price, 'index': swing_points[i]['index']},
                                {'wave': 2, 'price': wave2_price, 'index': swing_points[i+1]['index']},
                                {'wave': 3, 'price': wave3_price, 'index': swing_points[i+2]['index']},
                                {'wave': 4, 'price': wave4_price, 'index': swing_points[i+3]['index']},
                                {'wave': 5, 'price': wave5_price, 'index': swing_points[i+4]['index']}
                            ],
                            'confidence': pattern_confidence
                        })
    
    # اختيار النمط الأكثر احتمالية (بأعلى ثقة)
    if possible_patterns:
        best_pattern = max(possible_patterns, key=lambda x: x['confidence'])
        
        # حساب النسب الفيبوناتشي للموجات
        w1 = best_pattern['waves'][0]['price']
        w2 = best_pattern['waves'][1]['price']
        w3 = best_pattern['waves'][2]['price']
        w4 = best_pattern['waves'][3]['price']
        w5 = best_pattern['waves'][4]['price']
        
        # حساب نسب الموجات
        wave1_2_retracement = abs((w2 - w1) / (w1 * 0.01))  # كنسبة مئوية
        wave3_distance = abs((w3 - w2) / (w1 * 0.01))
        wave4_retracement = abs((w4 - w3) / ((w3 - w2) * 0.01))
        
        # تصنيف درجة الموجة (Impulse vs Diagonal)
        is_impulse = True
        if best_pattern['direction'] == 'صعودي':
            if w3 < w1 or w5 < w3 or wave4_retracement > 50:
                is_impulse = False
        else:  # هبوطي
            if w3 > w1 or w5 > w3 or wave4_retracement > 50:
                is_impulse = False
        
        # حساب تصنيف النمط
        pattern_type = "موجة دافعة" if is_impulse else "موجة قطرية"
        
        # حساب المستويات المستهدفة المحتملة بعد الموجة الخامسة
        target_abc = w5 + (w2 - w1) * 0.618 if best_pattern['direction'] == 'صعودي' else w5 - (w1 - w2) * 0.618
        
        return {
            'pattern': 'ELLIOTT',
            'type': pattern_type,
            'direction': best_pattern['direction'],
            'start': best_pattern['start_index'],
            'end': best_pattern['end_index'],
            'waves': best_pattern['waves'],
            'confidence': best_pattern['confidence'],
            'retracements': {
                'wave2_retracement': f"{wave1_2_retracement:.2f}%",
                'wave3_distance': f"{wave3_distance:.2f}%",
                'wave4_retracement': f"{wave4_retracement:.2f}%"
            },
            'targets': {
                'abc_correction': target_abc
            }
        }
    
    return None

def identify_elliott_wave_correction(df, window=60, min_points=3):
    """
    تحديد نمط تصحيح موجات إليوت (الموجات ABC) في بيانات السعر
    
    Args:
        df: إطار البيانات مع أسعار OHLC
        window: عدد الشموع للتحليل
        min_points: الحد الأدنى لعدد النقاط المطلوبة
        
    Returns:
        dict: معلومات عن نمط تصحيح موجات إليوت المكتشف أو None
    """
    if len(df) < window:
        return None
    
    # استخدام الفترة الأخيرة من البيانات
    df_window = df.iloc[-window:]
    
    # تحديد القمم والقيعان المحلية
    swing_threshold = 3  # عدد الشموع للنظر في كل اتجاه
    
    swing_points = []
    for i in range(swing_threshold, len(df_window) - swing_threshold):
        # القمم المحلية
        if df_window['HIGH'].iloc[i] == df_window['HIGH'].iloc[i-swing_threshold:i+swing_threshold+1].max():
            swing_points.append({
                'type': 'high',
                'price': df_window['HIGH'].iloc[i],
                'index': df_window.index[i]
            })
        # القيعان المحلية
        elif df_window['LOW'].iloc[i] == df_window['LOW'].iloc[i-swing_threshold:i+swing_threshold+1].min():
            swing_points.append({
                'type': 'low',
                'price': df_window['LOW'].iloc[i],
                'index': df_window.index[i]
            })
    
    # نحتاج على الأقل 3 نقاط لتمثيل موجات التصحيح
    if len(swing_points) < min_points:
        return None
    
    # البحث عن تسلسل يشبه نمط موجات ABC
    possible_patterns = []
    
    for i in range(len(swing_points) - 2):
        wave_a_type = swing_points[i]['type']
        wave_b_type = swing_points[i+1]['type']
        
        # يجب أن تكون الموجة B في الاتجاه المعاكس للموجة A
        if wave_a_type == wave_b_type:
            continue
        
        wave_c_type = swing_points[i+2]['type']
        
        # الموجة C يجب أن تكون في نفس اتجاه الموجة A
        if wave_a_type != wave_c_type:
            continue
        
        # استخراج الأسعار للموجات
        wave_a_price = swing_points[i]['price']
        wave_b_price = swing_points[i+1]['price']
        wave_c_price = swing_points[i+2]['price']
        
        # التحقق من قواعد نمط التصحيح
        is_bullish_correction = wave_a_type == 'high'  # تصحيح صعودي يبدأ من قمة
        
        # حساب نسبة تصحيح الموجة B
        wave_ab_distance = abs(wave_b_price - wave_a_price)
        
        # حساب امتداد الموجة C
        if is_bullish_correction:  # تصحيح هبوطي
            wave_b_retracement_pct = (wave_b_price - wave_a_price) / abs(wave_a_price) * 100
            wave_c_extension = (wave_a_price - wave_c_price) / abs(wave_b_price - wave_a_price)
            
            # التحقق من قواعد الموجة C (الامتداد غالبًا 1.618 أو 2.618 من طول الموجة A)
            pattern_confidence = 0.5
            if 0.5 <= wave_c_extension <= 1.0:
                pattern_type = "تصحيح بسيط"
                pattern_confidence = 0.7
            elif 1.0 < wave_c_extension <= 1.618:
                pattern_type = "Zigzag"
                pattern_confidence = 0.8
            elif wave_c_extension > 1.618:
                pattern_type = "Zigzag قوي"
                pattern_confidence = 0.9
            else:
                pattern_type = "تصحيح غير قياسي"
                pattern_confidence = 0.6
            
        else:  # تصحيح صعودي
            wave_b_retracement_pct = (wave_b_price - wave_a_price) / abs(wave_a_price) * 100
            wave_c_extension = (wave_c_price - wave_a_price) / abs(wave_b_price - wave_a_price)
            
            # التحقق من قواعد الموجة C (الامتداد غالبًا 1.618 أو 2.618 من طول الموجة A)
            pattern_confidence = 0.5
            if 0.5 <= wave_c_extension <= 1.0:
                pattern_type = "تصحيح بسيط"
                pattern_confidence = 0.7
            elif 1.0 < wave_c_extension <= 1.618:
                pattern_type = "Zigzag"
                pattern_confidence = 0.8
            elif wave_c_extension > 1.618:
                pattern_type = "Zigzag قوي"
                pattern_confidence = 0.9
            else:
                pattern_type = "تصحيح غير قياسي"
                pattern_confidence = 0.6
        
        # التحقق من نسبة تصحيح الموجة B
        if abs(wave_b_retracement_pct) < 20 or abs(wave_b_retracement_pct) > 80:
            pattern_confidence *= 0.7  # انخفاض الثقة إذا كانت نسبة تصحيح الموجة B غير طبيعية
        
        possible_patterns.append({
            'correction_type': pattern_type,
            'direction': 'هبوطي' if is_bullish_correction else 'صعودي',
            'start_index': swing_points[i]['index'],
            'end_index': swing_points[i+2]['index'],
            'waves': [
                {'wave': 'A', 'price': wave_a_price, 'index': swing_points[i]['index']},
                {'wave': 'B', 'price': wave_b_price, 'index': swing_points[i+1]['index']},
                {'wave': 'C', 'price': wave_c_price, 'index': swing_points[i+2]['index']}
            ],
            'confidence': pattern_confidence,
            'wave_b_retracement': wave_b_retracement_pct,
            'wave_c_extension': wave_c_extension
        })
    
    # اختيار النمط الأكثر احتمالية (بأعلى ثقة)
    if possible_patterns:
        best_pattern = max(possible_patterns, key=lambda x: x['confidence'])
        
        # حساب المستويات المستهدفة المحتملة بعد الموجة C
        target_bounce = None
        wave_a_price = best_pattern['waves'][0]['price']
        wave_c_price = best_pattern['waves'][2]['price']
        
        if best_pattern['direction'] == 'هبوطي':
            # بعد التصحيح الهبوطي، عادة ما يكون هناك ارتداد صعودي
            target_bounce = wave_c_price + (wave_a_price - wave_c_price) * 0.618
        else:
            # بعد التصحيح الصعودي، عادة ما يكون هناك ارتداد هبوطي
            target_bounce = wave_c_price - (wave_c_price - wave_a_price) * 0.618
        
        return {
            'pattern': 'ELLIOTT_ABC',
            'type': best_pattern['correction_type'],
            'direction': best_pattern['direction'],
            'start': best_pattern['start_index'],
            'end': best_pattern['end_index'],
            'waves': best_pattern['waves'],
            'confidence': best_pattern['confidence'],
            'metrics': {
                'wave_b_retracement': f"{best_pattern['wave_b_retracement']:.2f}%",
                'wave_c_extension': f"{best_pattern['wave_c_extension']:.2f}x"
            },
            'targets': {
                'bounce_target': target_bounce
            }
        }
    
    return None

def determine_elliott_wave_count(df, window=200):
    """
    تحديد العد الموجي الحالي للسهم بناءً على نظرية موجات إليوت
    
    Args:
        df: إطار البيانات مع أسعار OHLC
        window: النافذة الزمنية للتحليل
        
    Returns:
        dict: معلومات عن العد الموجي الحالي
    """
    if len(df) < window:
        # في حالة عدم وجود بيانات كافية، نستخدم كامل البيانات المتاحة
        df_window = df
    else:
        # استخدام النافذة الزمنية المحددة
        df_window = df.iloc[-window:]
    
    # العثور على الاتجاه العام (صعودي أو هبوطي)
    start_price = df_window['CLOSE'].iloc[0]
    end_price = df_window['CLOSE'].iloc[-1]
    overall_trend = "صعودي" if end_price > start_price else "هبوطي"
    
    # البحث عن نمط موجي دافع
    impulse_pattern = identify_elliott_wave_pattern(df_window)
    
    # البحث عن نمط موجي تصحيحي
    correction_pattern = identify_elliott_wave_correction(df_window, window=min(60, len(df_window)))
    
    # تحديد الموجة الحالية على الإطار الزمني الأكبر
    current_large_wave = None
    current_wave_details = None
    
    if impulse_pattern and correction_pattern:
        # تحديد أيهما أحدث (الموجة الدافعة أم التصحيحية)
        impulse_end = impulse_pattern['end']
        correction_end = correction_pattern['end']
        
        if impulse_end > correction_end:
            # نحن في تصحيح بعد موجة دافعة
            current_large_wave = "تصحيح (ABC) بعد الموجة 5"
            current_wave_details = impulse_pattern
        else:
            # نحن في موجة دافعة جديدة بعد التصحيح
            current_large_wave = "موجة 1 دافعة جديدة محتملة"
            current_wave_details = correction_pattern
    elif impulse_pattern:
        # نظرًا لعدم وجود تصحيح بعد الموجة الدافعة، نحن في تصحيح محتمل
        last_wave = impulse_pattern['waves'][-1]['wave']
        
        if last_wave == 5:
            current_large_wave = "بداية تصحيح (ABC) محتمل بعد الموجة 5"
        else:
            current_large_wave = f"موجة {last_wave + 1} محتملة"
        
        current_wave_details = impulse_pattern
    elif correction_pattern:
        # نظرًا لعدم وجود موجة دافعة حديثة، نحن قد نكون في بداية دورة دافعة جديدة
        current_large_wave = "موجة 1 دافعة محتملة بعد التصحيح"
        current_wave_details = correction_pattern
    else:
        # لم نعثر على أنماط واضحة، نحاول تحديد الموقف بناءً على حركة السعر الأخيرة
        last_20_trend = "صعودي" if df_window['CLOSE'].iloc[-20:].pct_change().sum() > 0 else "هبوطي"
        current_large_wave = f"غير محدد بوضوح، الاتجاه الأخير {last_20_trend}"
    
    # تحديد الموجات الصغيرة المحتملة
    small_wave_patterns = []
    for i in range(3):
        small_window = min(60, len(df_window))
        small_df = df_window.iloc[-(small_window - i*15):]
        
        small_impulse = identify_elliott_wave_pattern(small_df, window=small_window)
        small_correction = identify_elliott_wave_correction(small_df, window=small_window)
        
        if small_impulse and small_impulse not in small_wave_patterns:
            small_wave_patterns.append(small_impulse)
        
        if small_correction and small_correction not in small_wave_patterns:
            small_wave_patterns.append(small_correction)
        
        if len(small_wave_patterns) >= 2:
            break
    
    # حساب مستويات فيبوناتشي لتقدير مناطق التصحيح المحتملة
    high = df_window['HIGH'].max()
    low = df_window['LOW'].min()
    range_price = high - low
    
    fib_levels = {
        '0.236': low + range_price * 0.236,
        '0.382': low + range_price * 0.382,
        '0.5': low + range_price * 0.5,
        '0.618': low + range_price * 0.618,
        '0.786': low + range_price * 0.786
    }
    
    return {
        'overall_trend': overall_trend,
        'current_large_wave': current_large_wave,
        'current_wave_details': current_wave_details,
        'smaller_waves': small_wave_patterns[:2] if small_wave_patterns else [],
        'fibonacci_levels': fib_levels
    }