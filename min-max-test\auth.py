import json
import os
import gspread
import logging
from oauth2client.service_account import ServiceAccountCredentials
from aiogram.types import Message
from aiogram import <PERSON><PERSON>, Dispatch<PERSON>, types
from config import bot_token
# Initialize bot and dispatcher
bot = Bot(bot_token)
dp = Dispatcher(bot)

def authenticate_google_sheet():
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    with open("json_file.json", "r") as json_file:
        GOOGLE_API_KEY = json.load(json_file)
    credentials = ServiceAccountCredentials.from_json_keyfile_dict(GOOGLE_API_KEY, scope)
    client = gspread.authorize(credentials)
    sheet_name = "stock"
    sheet2 = client.open(sheet_name).worksheet("users")
    return sheet2

def open_google_sheet(sheet_name):
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']

    try:
        with open('json_file.json', 'r') as f:
            creds = json.load(f)
    except FileNotFoundError:
        logging.error('json file not found')
        raise
    except json.decoder.JSONDecodeError:
        logging.error('json file is not valid')
        raise
    
    credentials = ServiceAccountCredentials.from_json_keyfile_dict(creds, scope)

    try:
        client = gspread.authorize(credentials)
        sheet = client.open(sheet_name).worksheet('Sheet1')
        sheet2 = client.open(sheet_name).worksheet("users")
        sheet3 = client.open(sheet_name).worksheet("Sheet4")
    except gspread.exceptions.SpreadsheetNotFound:
        logging.error('sheet not found')
        raise
    except gspread.exceptions.APIError:
        logging.error('invalid credentials')
        raise

    return sheet, sheet2, sheet3
