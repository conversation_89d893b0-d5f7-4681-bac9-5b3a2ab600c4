# 🔧 دليل الحلول السريعة لنظام الأكواد

## 🚨 إذا ظهرت رسالة "AttributeError: 'Worksheet' object has no attribute 'add_worksheet'"

### الحل السريع (30 ثانية):
1. **افتح Google Sheets** في المتصفح
2. **اذهب لملف "stock"** 
3. **اضغط زر "+" لإضافة worksheet جديد**
4. **سمي الـ worksheet: `promo_codes`**
5. **في الصف الأول أضف العناوين التالية:**
   ```
   A1: كود البرومو
   B1: نوع الكود  
   C1: قيمة الخصم/الأيام
   D1: مستخدم بواسطة
   E1: تاريخ الاستخدام
   F1: تاريخ الإنشاء
   G1: حالة الكود
   H1: تاريخ الانتهاء
   I1: عدد مرات الاستخدام
   J1: ملاحظات
   ```
6. **احفظ واغلق**
7. **شغل البوت مرة أخرى**

---

## 🧪 اختبار الحل:

**شغل الملف:**
```bash
python test_promo_system.py
```

**إذا ظهر:**
- ✅ **"النظام يعمل بشكل أساسي!"** → كل شيء تمام
- ✅ **"نظام الأكواد يعمل بالكامل!"** → مثالي!
- ⚠️ **"نظام الأكواد غير متاح"** → استخدم الحل السريع أعلاه

---

## 🎯 أوامر البوت بعد الحل:

### للإدارة:
```
/create_trial_code 7 30 كود ترحيبي
/create_discount_code 50 15 عرض نهاية الأسبوع  
/list_codes
```

### للمستخدمين:
```
/redeem TRIAL12AB34
/redeem SAVE50XY9ZT
```

---

## 🔍 حالات أخرى محتملة:

### إذا ظهر "خطأ في الاتصال بـ Google Sheets":
1. **تأكد من وجود ملف `json_file.json`**
2. **تأكد من صحة بيانات Google Sheets API**
3. **تأكد من أن الحساب له صلاحية على ملف "stock"**

### إذا ظهر "ملف 'stock' غير موجود":
1. **تأكد من اسم الملف في Google Sheets**
2. **تأكد من مشاركة الملف مع الحساب المُستخدم في API**

### إذا ظهر "خطأ في الاستيرادات":
1. **تأكد من وجود جميع الملفات المطلوبة**
2. **شغل:** `pip install -r requirements.txt`

---

## 📊 الحماية المُدمجة:

النظام الآن محمي ويعمل حتى لو:
- ❌ worksheet promo_codes غير موجود
- ❌ Google Sheets غير متاح مؤقتاً  
- ❌ مشاكل في الاستيراد

**في هذه الحالات:**
- ✅ البوت يعمل عادي
- ✅ وظائف التحليل تعمل
- ⚠️ أوامر الأكواد فقط غير متاحة

---

## 🆘 إذا لم يعمل شيء:

1. **شغل البوت بدون أكواد:**
   - علق السطر `from promo_codes import PromoCodeManager` في main.py
   - البوت سيعمل عادي بدون نظام الأكواد

2. **تواصل للدعم مع تفاصيل الخطأ**

---

**🎉 النجاح مضمون مع هذه الحلول! 🚀**
