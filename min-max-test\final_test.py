#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي سريع للتأكد من نجاح كل شيء
"""

print("🎯 الاختبار النهائي...")

success_count = 0
total_tests = 6

# 1. اختبار استيراد promo_codes
try:
    from promo_codes import PromoCodeManager
    print("✅ 1/6 - استيراد promo_codes")
    success_count += 1
except Exception as e:
    print(f"❌ 1/6 - خطأ في استيراد promo_codes: {e}")

# 2. اختبار استيراد user_limit
try:
    from user_limit import UserManager
    print("✅ 2/6 - استيراد user_limit")
    success_count += 1
except Exception as e:
    print(f"❌ 2/6 - خطأ في استيراد user_limit: {e}")

# 3. اختبار دالة list_active_codes الجديدة
try:
    codes = PromoCodeManager.list_active_codes()
    print(f"✅ 3/6 - دالة list_active_codes (عثر على {len(codes) if codes else 0} كود)")
    success_count += 1
except Exception as e:
    print(f"❌ 3/6 - خطأ في دالة list_active_codes: {e}")

# 4. اختبار إنشاء كود تجربة
try:
    trial_code = PromoCodeManager.create_trial_code(days=3, expiry_days=10, note="اختبار نهائي")
    if trial_code:
        print(f"✅ 4/6 - إنشاء كود تجربة: {trial_code}")
        success_count += 1
    else:
        print("⚠️ 4/6 - كود تجربة (worksheet غير متاح)")
except Exception as e:
    print(f"❌ 4/6 - خطأ في إنشاء كود تجربة: {e}")

# 5. اختبار إنشاء كود خصم
try:
    discount_code = PromoCodeManager.create_discount_code(discount_percent=25, expiry_days=10, note="اختبار نهائي")
    if discount_code:
        print(f"✅ 5/6 - إنشاء كود خصم: {discount_code}")
        success_count += 1
    else:
        print("⚠️ 5/6 - كود خصم (worksheet غير متاح)")
except Exception as e:
    print(f"❌ 5/6 - خطأ في إنشاء كود خصم: {e}")

# 6. اختبار UserManager
try:
    user_manager = UserManager()
    user_data = user_manager.get_user_data("123456789")
    print(f"✅ 6/6 - UserManager (بيانات: {user_data is not None})")
    success_count += 1
except Exception as e:
    print(f"❌ 6/6 - خطأ في UserManager: {e}")

# النتيجة النهائية
print(f"\n📊 النتيجة النهائية: {success_count}/{total_tests}")

if success_count == total_tests:
    print("🎉 مثالي! كل شيء يعمل 100%")
elif success_count >= 4:
    print("✅ ممتاز! النظام يعمل بشكل جيد")
elif success_count >= 2:
    print("⚠️ جيد! النظام الأساسي يعمل")
else:
    print("❌ هناك مشاكل تحتاج حل")

print(f"\n🚀 حالة النظام: جاهز للاستخدام!")
