#!/usr/bin/env python3
"""
اختبار سريع لفحص سبب عدم عمل أوامر البرومو كود
"""

import sys
import os

def test_promo_commands_issue():
    """فحص سبب عدم عمل أوامر البرومو كود"""
    print("🔍 فحص سبب عدم عمل أوامر البرومو كود...")
    
    # 1. فحص الاستيراد
    print("\n1. فحص الاستيراد:")
    try:
        from promo_codes import PromoCodeManager
        print("✅ تم استيراد PromoCodeManager بنجاح")
    except Exception as e:
        print(f"❌ فشل استيراد PromoCodeManager: {e}")
        return False
    
    # 2. فحص user_limit
    try:
        from user_limit import UserManager
        print("✅ تم استيراد UserManager بنجاح")
    except Exception as e:
        print(f"❌ فشل استيراد UserManager: {e}")
        
    # 3. فحص main.py
    print("\n2. فحص تسجيل الأوامر في main.py:")
    
    # قراءة main.py
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # فحص تسجيل الأوامر
    commands_to_check = [
        'create_trial_code',
        'create_discount_code', 
        'redeem',
        'list_codes'
    ]
    
    for cmd in commands_to_check:
        if f"commands=['{cmd}']" in content:
            print(f"✅ الأمر {cmd} مُسجل في main.py")
        else:
            print(f"❌ الأمر {cmd} غير مُسجل في main.py")
    
    # 4. فحص ترتيب تسجيل المعالجات
    print("\n3. فحص ترتيب تسجيل المعالجات:")
    
    # البحث عن مكان تسجيل أوامر البرومو
    promo_registration_line = None
    general_handler_line = None
    
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'create_trial_code' in line and 'dp.message_handler' in line:
            promo_registration_line = i
        if '@dp.message_handler()' in line:
            general_handler_line = i
    
    if promo_registration_line and general_handler_line:
        if promo_registration_line < general_handler_line:
            print("✅ أوامر البرومو مُسجلة قبل المعالج العام")
        else:
            print("❌ أوامر البرومو مُسجلة بعد المعالج العام - هذه هي المشكلة!")
    
    # 5. فحص المعالج العام
    print("\n4. فحص المعالج العام:")
    if 'process_callback_messages' in content:
        print("✅ المعالج العام موجود")
        
        # فحص إذا كان يتجاهل الأوامر
        if "if message.text.startswith('/'):" in content:
            print("✅ المعالج العام يتجاهل الأوامر (return)")
        else:
            print("❌ المعالج العام لا يتجاهل الأوامر!")
    
    print("\n" + "="*50)
    print("🎯 الخلاصة:")
    print("- إذا كانت المشكلة في الاستيراد: سيظهر PROMO_CODES_AVAILABLE = False")
    print("- إذا كانت المشكلة في التسجيل: سيتم استخدام الدوال البديلة")
    print("- إذا كانت المشكلة في الترتيب: المعالج العام يعترض الأوامر")
    
    return True

if __name__ == "__main__":
    test_promo_commands_issue()
