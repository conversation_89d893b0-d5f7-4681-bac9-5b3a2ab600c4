import datetime
from aiogram.types import Message
from auth import authenticate_google_sheet,dp,bot
# Define the function to upgrade user to paid
def upgrade_user(user_id, period_days):
    sheet = authenticate_google_sheet()
    user_data = sheet.get_all_values()
    today = datetime.date.today()
    period_end = today + datetime.timedelta(days=period_days)

    # Look for the user_id in the sheet
    for index, row in enumerate(user_data):
        if row[0] == str(user_id):
            user_index = index
            if row[2] == 'paid':
                # Update the expired date by adding the new period to the existing date
                expired_date = datetime.datetime.strptime(row[3], '%Y-%m-%d').date()
                new_expired_date = expired_date + datetime.timedelta(days=int(period_days))
                sheet.update_cell(user_index+1, 4, new_expired_date.strftime('%Y-%m-%d'))
                return {"success": True, "new_expired_date": new_expired_date}

    # If the user is not paid, update their status to 'paid' and set the expired date to today + days
    if user_index is not None:
        today = datetime.date.today().strftime('%Y-%m-%d')
        expired_date = (datetime.date.today() + datetime.timedelta(days=int(period_days))).strftime('%Y-%m-%d')
        sheet.update_cell(user_index+1, 3, 'paid')
        sheet.update_cell(user_index+1, 4, expired_date)
        return {"success": True, "new_expired_date": expired_date}
    else:
        return {"success": False, "message": f"User {user_id} not found in the sheet."} 
# Define the command callback function
@dp.message_handler(commands=['upgrade'])
async def upgrade_command(message: Message):
    # Parse the command arguments
    args = message.get_args().split()
    if len(args) != 2:
        await bot.send_message(chat_id=message.chat.id, text="Invalid arguments. Usage: /upgrade user_id period")
        return
    # Get the user_id and period_days from the command arguments
    user_id, period_days = message.text.split()[1:]
    period_days = int(period_days)
    # Call the upgrade_user function and send the result as a message
    result = upgrade_user(user_id, period_days)
    #await message.answer(result)
    if result['success']:
        # Get the user's name to put in the congratulations message
        user = await bot.get_chat(user_id)
        user_name = user.first_name
    
        # Send the congratulations message to the upgraded user
        await bot.send_message(chat_id=user_id, text=f"Congratulations {user_name}! You are now one of our valued members and will have access to our full services until {result['new_expired_date']}!")
    
        # Send a success message to the person who triggered the command
        await bot.send_message(chat_id=message.chat.id, text=f"User {user_id} has been upgraded to paid and the new expiration date is {result['new_expired_date']}.")
    
    else:
        # Send an error message to the person who triggered the command
        await bot.send_message(chat_id=message.chat.id, text=result['message'])
