"""
Utility functions for bot command registration and processing
"""
import logging
import re
from aiogram import Bot, types
from aiogram.types import Bot<PERSON>ommand

# Get the logger
logger = logging.getLogger(__name__)

def register_bot_handlers(dp):
    """Register all command handlers with the dispatcher"""
    try:
        # Import all required handlers
        from user_limit import check_user_limit,display_refer_link
        from update_google_sheet import update_google_sheet,update_sheet
        from reset_counter import reset_counters_command
        from upgrade import upgrade_user,upgrade_command
        from plot import chart_handler
        from process_data import (
            process_modarba_all, 
            process_modarba,
            process_stock_code,
            process_analyze_command,
            process_hv,
            process_callback_messages,
            process_help,
            process_subscribe,
            cmd_menu,
            get_subscription_date,
            fibo,
            process_deals,
            handle_deals_button,
            handle_chart_button,
            BUTTON_DEALS,
            BUTTON_CHART,
        )
        
        # Get bot instance from auth
        from auth import bot

        # Basic commands
        dp.register_message_handler(lambda message: check_user_limit(message, bot), commands=['start'])
        dp.register_message_handler(process_help, commands=['help'])
        dp.register_message_handler(cmd_menu, commands=['menu'])
        dp.register_message_handler(process_subscribe, commands=['subscribe'])
        dp.register_message_handler(display_refer_link, commands=['myrefer'])
        dp.register_message_handler(get_subscription_date, commands=['mysubscribtion'])
        
        # Stock analysis commands
        dp.register_message_handler(process_stock_code, commands=['stock'])
        dp.register_message_handler(process_analyze_command, commands=['analyze'])
        dp.register_message_handler(chart_handler, commands=['chart'])
        dp.register_message_handler(process_modarba, commands=['modarba'])
        dp.register_message_handler(process_modarba_all, commands=['modarba_all'])
        dp.register_message_handler(process_hv, commands=['hv'])
        dp.register_message_handler(fibo, commands=['fibo'])
        dp.register_message_handler(process_deals, commands=['deals'])
        
        # Problematic commands - fixed with proper registration
        # === comp command ===
        dp.register_message_handler(update_sheet, commands=['comp'])
        
        # === upgrade command - with special handling for varied spacing ===
        dp.register_message_handler(
            upgrade_command, 
            commands=['upgrade']
        )
        
        # Add robust handler for upgrade command with extra spaces
        dp.register_message_handler(
            upgrade_command,
            lambda message: message.text and message.text.strip().startswith('/upgrade') and not message.text.startswith('/upgrade ')
        )
        
        # === reset_counters command ===
        dp.register_message_handler(reset_counters_command, commands=['reset_counters'])
        
        # Button handlers for specific texts
        dp.register_message_handler(handle_deals_button, lambda message: message.text == BUTTON_DEALS)
        dp.register_message_handler(handle_chart_button, lambda message: message.text == BUTTON_CHART)
        
        # Callback query handlers
        dp.register_callback_query_handler(
            lambda c: handle_upgrade_callback(c, upgrade_command),
            lambda c: c.data and c.data.startswith('upgrade_')
        )
        
        # Catch-all handler for other messages
        dp.register_message_handler(process_callback_messages)
        
        logger.info("All handlers registered successfully")
        return True
    except Exception as e:
        logger.error(f"Error registering handlers: {e}", exc_info=True)
        return False

async def handle_upgrade_callback(callback_query, upgrade_handler):
    """Handle upgrade button callbacks"""
    try:
        # Extract user_id and days from callback data
        parts = callback_query.data.split('_')
        if len(parts) >= 3:
            user_id = parts[1]
            days = parts[2]
            
            # Create a new message object with the correct command format
            message = callback_query.message
            message.text = f"/upgrade {user_id} {days}"
            
            # Call the upgrade handler
            await upgrade_handler(message)
            
            # Answer the callback query
            await callback_query.answer("✅ Upgrade request processed")
        else:
            await callback_query.answer("❌ Invalid upgrade data")
    except Exception as e:
        logger.error(f"Error in upgrade callback: {e}")
        await callback_query.answer("❌ Error processing upgrade")

async def setup_bot_commands(bot=None):
    """Set up the bot command menu"""
    if bot is None:
        # Import bot from auth if not provided
        from auth import bot
    
    try:
        commands = [
            BotCommand(command="start", description="Start the bot"),
            BotCommand(command="help", description="Get help"),
            BotCommand(command="menu", description="Main menu"),
            BotCommand(command="subscribe", description="Subscription information"),
            BotCommand(command="stock", description="Stock information"),
            BotCommand(command="analyze", description="Analyze a stock"),
            BotCommand(command="chart", description="Stock chart"),
            BotCommand(command="modarba", description="Modaraba stocks"),
            BotCommand(command="comp", description="Compare stocks"),
            BotCommand(command="deals", description="Today's deals"),
        ]
        
        await bot.set_my_commands(commands)
        logger.info("Bot commands menu set up successfully")
        return True
    except Exception as e:
        logger.error(f"Error setting up bot commands: {e}")
        return False
