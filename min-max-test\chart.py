import os
import pandas as pd
import matplotlib.pyplot as plt
import ta
from datetime import datetime, timedelta
import mplfinance as mpf
import matplotlib as mpl
import matplotlib
from matplotlib.legend_handler import HandlerTuple
import numpy as np

def Stochastic(df, window, smooth_window):
    stochastic = pd.DataFrame()
    stochastic['%K'] = ((df['CLOSE'] - df['LOW'].rolling(window).min()) \
        / (df['HIGH'].rolling(window).max() - df['LOW'].rolling(window).min())) * 100
    stochastic['%D'] = stochastic['%K'].rolling(smooth_window).mean()
    stochastic['%SD'] = stochastic['%D'].rolling(smooth_window).mean()
    stochastic['UL'] = 80
    stochastic['DL'] = 20
    return stochastic

def MACD(df, window_slow, window_fast, window_signal):
    macd = pd.DataFrame()
    macd['ema_slow'] = df['CLOSE'].ewm(span=window_slow).mean()
    macd['ema_fast'] = df['CLOSE'].ewm(span=window_fast).mean()
    macd['macd'] = macd['ema_fast'] - macd['ema_slow']  # Fixed subtraction order
    macd['signal'] = macd['macd'].ewm(span=window_signal).mean()
    macd['diff'] = macd['macd'] - macd['signal']
    macd['bar_positive'] = macd['diff'].map(lambda x: x if x > 0 else 0)
    macd['bar_negative'] = macd['diff'].map(lambda x: x if x < 0 else 0)
    return macd

def bollinger_bands(close_price, window_size=20, k=2):
    rolling_mean = close_price.rolling(window=window_size).mean()
    rolling_std = close_price.rolling(window=window_size).std()
    upper_band = rolling_mean + (rolling_std * k)
    lower_band = rolling_mean - (rolling_std * k)
    return upper_band, lower_band, rolling_mean

def calculate_rsi(df, period=14):
    close = df["CLOSE"]
    delta = close - close.shift(1)
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    # Add division by zero protection
    rs = avg_gain / avg_loss.replace(0, np.finfo(float).eps)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def linear_regression_channel(df, window=50):
    """
    Calculate the Linear Regression Channel for the last `window` days.
    """
    # Select the last `window` days of data
    df_last = df.iloc[-window:]
    
    # Calculate linear regression
    x = np.arange(len(df_last))
    y = df_last['CLOSE'].values
    A = np.vstack([x, np.ones(len(x))]).T
    slope, intercept = np.linalg.lstsq(A, y, rcond=None)[0]
    
    # Calculate the regression line
    regression_line = slope * x + intercept
    
    # Calculate residuals and standard deviation
    residuals = y - regression_line
    std_dev = np.std(residuals)
    
    # Calculate upper and lower channel bounds
    upper_channel = regression_line + (2.0 * std_dev)  # Upper multiplier = 2.0
    lower_channel = regression_line - (2.0 * std_dev)  # Lower multiplier = 2.0
    
    # Create a DataFrame to store the LRC values
    lrc = pd.DataFrame(index=df_last.index)
    lrc['regression_line'] = regression_line
    lrc['upper_channel'] = upper_channel
    lrc['lower_channel'] = lower_channel
    
    return lrc

def plot_stock_data(chart_data_path):
    # Read data from text file
    if not chart_data_path.endswith('.TXT'):
        print('File format not supported.')
        return
    df = pd.read_csv(chart_data_path, delimiter=',', header=None, skiprows=1,
                     names=['TICKER', 'PER', 'DTYYYYMMDD', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OI'])
    df = df.astype({'OPEN': float, 'HIGH': float, 'LOW': float, 'CLOSE': float})
    # Convert datetime column to pandas datetime format
    df['datetime'] = pd.to_datetime(df['DTYYYYMMDD'], format='%Y%m%d') + pd.to_timedelta(df['TIME'], unit='m')
    # Set datetime column as index
    df.set_index('datetime', inplace=True)
    # Select rows for the last year
    last_year = df.index[-1] - timedelta(days=180)
    df = df[~df.index.duplicated()]
    df = df.loc[last_year:]
    
    # Calculate indicators
    macd = MACD(df, 12, 26, 9)
    stochastic = Stochastic(df, 14, 3)
    upper_band, lower_band, rolling_mean = bollinger_bands(df['CLOSE'])
    df['upper_band'] = upper_band
    df['lower_band'] = lower_band
    df['rolling_mean'] = rolling_mean
    rsi = calculate_rsi(df, period=14)
    overbought = pd.Series([70] * len(df), index=df.index)
    oversold = pd.Series([30] * len(df), index=df.index)
    ticker = df['TICKER'][0]
    # Calculate moving averages once
    df['MA7'] = df['CLOSE'].rolling(window=7).mean()
    df['MA20'] = df['CLOSE'].rolling(window=20).mean()
    df['MA50'] = df['CLOSE'].rolling(window=50).mean()
    # Fix RSI assignment
    df['RSI'] = rsi  # Assign calculated RSI instead of MA50
    colors = np.where(df['CLOSE'] > df['OPEN'], 'g', 'r')
    daily_volume = df['VOL']
    
    # Calculate Linear Regression Channel for the last 50 days
    lrc = linear_regression_channel(df, window=50)
    
    # Align LRC values with the main DataFrame
    df['regression_line'] = np.nan
    df['upper_channel'] = np.nan
    df['lower_channel'] = np.nan
    df.loc[lrc.index, 'regression_line'] = lrc['regression_line']
    df.loc[lrc.index, 'upper_channel'] = lrc['upper_channel']
    df.loc[lrc.index, 'lower_channel'] = lrc['lower_channel']
    
    # Create a DataFrame with the crossover and crossunder points and colors
    crossover_points = pd.DataFrame()
    crossover_points['crossover'] = np.where((macd['macd'] > macd['signal']) & (macd['macd'].shift() < macd['signal'].shift()), macd['macd'], np.nan)
    crossover_points['crossunder'] = np.where((macd['macd'] < macd['signal']) & (macd['macd'].shift() > macd['signal'].shift()), macd['macd'], np.nan)
    crossover_points.index = macd.index
    crossover_points['signal'] = np.where(crossover_points['crossover'].notnull(), 'crossover', np.nan)
    crossover_points['signal'] = np.where(crossover_points['crossunder'].notnull(), 'crossunder', crossover_points['signal'])
    crossover_points['color'] = np.where(crossover_points['signal'] == 'crossover', 'green', 'red')
    crossover_points['color'] = np.where(crossover_points['signal'] == 'crossunder', 'red', crossover_points['color'])

    # Plot the chart
    fig, axs = plt.subplots(nrows=5, ncols=1, figsize=(12, 8), gridspec_kw={'height_ratios': [3, 1, 1, 1, 1]})
    plots = [
        mpf.make_addplot(crossover_points['crossover'], ax=axs[2], type='scatter', marker='o', markersize=50, color='green'),
        mpf.make_addplot(crossover_points['crossunder'], ax=axs[2], type='scatter', marker='o', markersize=50, color='red'),
        mpf.make_addplot((macd['macd']), color='#606060', panel=1, ylabel='MACD (12,26,9)', secondary_y=False, ax=axs[2]),
        mpf.make_addplot((macd['signal']), color='#1f77b4', panel=1, secondary_y=False, ax=axs[2]),
        mpf.make_addplot((macd['bar_positive']), type='bar', color='#4dc790', panel=1, ax=axs[2]),
        mpf.make_addplot((macd['bar_negative']), type='bar', color='#fd6b6c', panel=1, ax=axs[2]),
        mpf.make_addplot((stochastic[['%D', '%SD', 'UL', 'DL']]), ylim=[0, 100], panel=2, ylabel='Stoch (14,3)', ax=axs[3]),
       # mpf.make_addplot(df['upper_band'], ax=axs[0]),
       # mpf.make_addplot(df['lower_band'], ax=axs[0]),
        mpf.make_addplot(rsi, ylabel="RSI", ax=axs[4]),
        mpf.make_addplot(overbought, linestyle='dotted', ax=axs[4]),
        mpf.make_addplot(oversold, linestyle='dotted', ax=axs[4]),
        mpf.make_addplot(daily_volume, type='bar', ax=axs[1], ylabel='Volume', color=colors.tolist()),
        mpf.make_addplot(df['regression_line'], ax=axs[0], color='blue', linestyle='--', label='Linear Regression'),
        mpf.make_addplot(df['upper_channel'], ax=axs[0], color='red', linestyle='--', label='Upper Channel'),
        mpf.make_addplot(df['lower_channel'], ax=axs[0], color='green', linestyle='--', label='Lower Channel'),
    ]

    # Plot the candlestick chart and the indicators
    mpf.plot(df, type='candle', style='yahoo', ax=axs[0], columns=['OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL'], mav=(7, 20, 50), addplot=plots, show_nontrading=False, returnfig=True)
    axs[0].text(0.5, 0.3, 'https://t.me/egx_stock_analyzer_bot', fontsize=20, color='green', ha='center', va='center', alpha=0.3, transform=axs[0].transAxes)
    axs[0].set_title('{} - {}'.format(ticker, df.index[-1].strftime('%Y-%m-%d')), fontsize=20, color='red', alpha=0.3)
    axs[0].legend(labels=['', '', 'ma7: {:.2f}'.format(df['MA7'][-1]), 'ma20: {:.2f}'.format(df['MA20'][-1]), 'ma50: {:.2f}'.format(df['MA50'][-1]), 'BBand_Upper: {:.2f}'.format(df['upper_band'][-1]), 'BBand_Lower: {:.2f}'.format(df['lower_band'][-1])], handlelength=0.1, borderpad=0.1, labelspacing=0, loc='upper left')
    
    # Save the chart as an image file
    chart_file_path = chart_data_path.replace('.TXT', '.png')
    fig.savefig(chart_file_path, dpi=100, bbox_inches='tight')
    # Close the plot
    plt.close()
