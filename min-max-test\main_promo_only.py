#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من البوت تركز على أوامر البرومو كود فقط
Simplified bot version focusing only on promo code commands
"""

import logging
import datetime
from aiogram import Bo<PERSON>, Dispatcher, executor, types
from aiogram.contrib.fsm_storage.memory import MemoryStorage

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)

# إعداد البوت (يجب تعديل التوكن)
try:
    from auth import bot, dp
    print("✅ تم تحميل إعدادات البوت من auth.py")
except ImportError:
    # إذا لم يوجد ملف auth، استخدم إعدادات افتراضية
    BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"  # ضع التوكن هنا
    bot = Bot(token=BOT_TOKEN)
    storage = MemoryStorage()
    dp = Dispatcher(bot, storage=storage)
    print("⚠️ تم استخدام إعدادات البوت الافتراضية")

# استيراد نظام أكواد الخصم
try:
    print("🔍 محاولة تحميل نظام الأكواد...")
    from promo_codes import PromoCodeManager
    from user_limit import UserManager
    PROMO_CODES_AVAILABLE = True
    print("✅ تم تحميل نظام الأكواد بنجاح")
except ImportError as e:
    PROMO_CODES_AVAILABLE = False
    print(f"❌ فشل استيراد نظام الأكواد (ImportError): {e}")
    logging.warning(f"Promo codes system not available: {e}")
except Exception as e:
    PROMO_CODES_AVAILABLE = False
    print(f"❌ فشل تحميل نظام الأكواد (Exception): {e}")
    logging.error(f"Error importing promo codes system: {e}")

# قائمة الإدارة
try:
    from config import ADMIN_IDS
    print(f"✅ تم تحميل قائمة الإدارة: {len(ADMIN_IDS)} مدير")
except ImportError:
    ADMIN_IDS = []  # قائمة فارغة - سيتم السماح لأي شخص للاختبار
    print("⚠️ لم يتم العثور على ملف config.py - سيتم السماح لأي شخص بالوصول للأوامر")

# ===== أوامر البوت الأساسية =====

@dp.message_handler(commands=['start'])
async def start_command(message: types.Message):
    """أمر البداية"""
    user_id = message.from_user.id
    username = message.from_user.username or "غير محدد"
    
    await message.reply(f"""
🤖 **مرحباً بك في بوت البرومو كود!**

👤 **المستخدم:** {user_id}
📝 **اسم المستخدم:** @{username}
🎯 **حالة نظام الأكواد:** {'متاح' if PROMO_CODES_AVAILABLE else 'غير متاح'}

📋 **الأوامر المتاحة:**
• `/create_trial_code` - إنشاء كود تجربة مجانية
• `/create_discount_code` - إنشاء كود خصم
• `/redeem` - استخدام كود برومو
• `/list_codes` - عرض الأكواد النشطة
• `/test_promo` - اختبار النظام

💡 **ملاحظة:** أوامر الإنشاء والعرض متاحة للإدارة فقط
""", parse_mode="Markdown")

@dp.message_handler(commands=['help'])
async def help_command(message: types.Message):
    """أمر المساعدة"""
    await message.reply("""
📋 **دليل استخدام البوت:**

🎫 **للمستخدمين:**
• `/redeem PROMO_CODE` - استخدام كود برومو

👨‍💼 **للإدارة:**
• `/create_trial_code [أيام] [انتهاء] [ملاحظة]` - إنشاء كود تجربة
• `/create_discount_code [نسبة] [انتهاء] [ملاحظة]` - إنشاء كود خصم
• `/list_codes` - عرض الأكواد النشطة

🧪 **للاختبار:**
• `/test_promo` - اختبار النظام

**أمثلة:**
• `/create_trial_code 7 30 كود للأعضاء الجدد`
• `/create_discount_code 25 15 خصم نهاية الأسبوع`
• `/redeem TRIAL123ABC`
""", parse_mode="Markdown")

# ===== أوامر نظام أكواد الخصم =====

@dp.message_handler(commands=['create_trial_code'])
async def create_trial_code_command(message: types.Message):
    """إنشاء كود تجربة مجانية - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        args = message.text.split()
        days = int(args[1]) if len(args) > 1 else 7
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود تجربة مجانية"
        
        code = PromoCodeManager.create_trial_code(days, expiry_days, note)
        
        if code:
            await message.reply(f"""
✅ **تم إنشاء كود التجربة المجانية بنجاح!**

🎫 **الكود:** `{code}`
📅 **مدة التجربة:** {days} أيام
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`
""", parse_mode="Markdown")
        else:
            await message.reply("❌ حدث خطأ في إنشاء الكود")
    except ValueError:
        await message.reply("""
❌ **خطأ في التنسيق**

📋 **طريقة الاستخدام:**
`/create_trial_code [أيام] [انتهاء] [ملاحظة]`

**مثال:**
`/create_trial_code 7 30 كود خاص للأعضاء الجدد`
""", parse_mode="Markdown")
    except Exception as e:
        await message.reply(f"❌ خطأ: {str(e)}")

@dp.message_handler(commands=['create_discount_code'])
async def create_discount_code_command(message: types.Message):
    """إنشاء كود خصم - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        args = message.text.split()
        discount_percent = int(args[1]) if len(args) > 1 else 20
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود خصم خاص"
        
        code = PromoCodeManager.create_discount_code(discount_percent, expiry_days, note)
        
        if code:
            await message.reply(f"""
✅ **تم إنشاء كود الخصم بنجاح!**

🎫 **الكود:** `{code}`
💰 **نسبة الخصم:** {discount_percent}%
⏰ **صالح حتى:** {expiry_days} يوم من اليوم
📝 **ملاحظة:** {note}

📋 **للاستخدام:** `/redeem {code}`
""", parse_mode="Markdown")
        else:
            await message.reply("❌ حدث خطأ في إنشاء الكود")
    except ValueError:
        await message.reply("""
❌ **خطأ في التنسيق**

📋 **طريقة الاستخدام:**
`/create_discount_code [نسبة] [انتهاء] [ملاحظة]`

**مثال:**
`/create_discount_code 50 15 عرض نهاية الأسبوع`
""", parse_mode="Markdown")
    except Exception as e:
        await message.reply(f"❌ خطأ: {str(e)}")

@dp.message_handler(commands=['redeem'])
async def redeem_promo_code(message: types.Message):
    """استخدام كود البرومو"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    args = message.text.split()
    if len(args) != 2:
        await message.reply("""
🎫 **كيفية استخدام الكود:**

📋 **الاستخدام:** `/redeem YOUR_CODE`

**مثال:** `/redeem TRIAL123ABC`
""", parse_mode="Markdown")
        return
    
    code = args[1].upper()
    user_id = str(message.from_user.id)
    
    is_valid, error_msg, code_data = PromoCodeManager.validate_promo_code(code, user_id)
    
    if not is_valid:
        await message.reply(f"❌ {error_msg}")
        return
    
    # تطبيق الكود حسب نوعه
    if code_data['type'] == 'trail':
        trail_days = int(code_data['value'])
        PromoCodeManager.use_promo_code(code_data, user_id)
        await message.reply(f"""
🎉 **تم تفعيل التجربة المجانية!**

⏰ **المدة:** {trail_days} أيام
🎫 **الكود:** {code}
✅ **الحالة:** مفعل

💡 يمكنك الآن الاستفادة من جميع المزايا المتاحة!
""", parse_mode="Markdown")
    
    elif code_data['type'] == 'discount':
        PromoCodeManager.use_promo_code(code_data, user_id)
        await message.reply(f"""
💰 **تم تفعيل كود الخصم!**

🎯 **نسبة الخصم:** {code_data['value']}%
🎫 **الكود:** {code}
✅ **الحالة:** مفعل

💡 سيتم تطبيق الخصم على عمليات الشراء القادمة!
""", parse_mode="Markdown")

@dp.message_handler(commands=['list_codes'])
async def list_promo_codes(message: types.Message):
    """عرض الأكواد النشطة - للإدارة فقط"""
    if not PROMO_CODES_AVAILABLE:
        await message.reply("❌ نظام الأكواد غير متاح حالياً")
        return
        
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    if ADMIN_IDS and user_id not in ADMIN_IDS:
        await message.reply("❌ هذا الأمر متاح للإدارة فقط")
        return
    
    active_codes = PromoCodeManager.list_active_codes()
    
    if active_codes is None:
        await message.reply("❌ حدث خطأ في عرض الأكواد")
        return
    
    if not active_codes:
        await message.reply("📋 لا توجد أكواد نشطة حالياً")
        return
    
    message_text = f"📋 **الأكواد النشطة ({len(active_codes)}):**\n\n"
    
    for i, code in enumerate(active_codes[-10:], 1):  # عرض آخر 10 أكواد
        code_type = "🎁 تجربة" if code['type'] == 'trail' else "💰 خصم"
        message_text += f"{i}. `{code['code']}`\n"
        message_text += f"   {code_type} - {code['value']}{'أيام' if code['type'] == 'trail' else '%'}\n"
        message_text += f"   📅 ينتهي: {code['expiry_date']}\n"
        message_text += f"   🔢 استخدم: {code['usage_count']} مرة\n\n"
    
    await message.reply(message_text, parse_mode="Markdown")

@dp.message_handler(commands=['test_promo'])
async def test_promo_command(message: types.Message):
    """اختبار نظام البرومو كود"""
    user_id = message.from_user.id
    username = message.from_user.username or "غير محدد"
    
    await message.reply(f"""
🧪 **تقرير حالة نظام البرومو كود**

✅ نظام تسجيل الأوامر سليم
👤 **المستخدم:** {user_id}
📝 **اسم المستخدم:** @{username}
🕐 **الوقت:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 **حالة نظام الأكواد:** {'متاح' if PROMO_CODES_AVAILABLE else 'غير متاح'}
👨‍💼 **صلاحية الإدارة:** {'نعم' if not ADMIN_IDS or user_id in ADMIN_IDS else 'لا'}

📋 **الأوامر المتاحة:**
• `/create_trial_code` - إنشاء كود تجربة
• `/create_discount_code` - إنشاء كود خصم  
• `/redeem` - استخدام كود
• `/list_codes` - عرض الأكواد

💡 **ملاحظة:** أوامر الإنشاء والعرض متاحة للإدارة فقط
""", parse_mode="Markdown")

if __name__ == '__main__':
    print("=" * 50)
    print("🚀 بدء تشغيل بوت البرومو كود المبسط")
    print("=" * 50)
    print(f"🎯 حالة نظام الأكواد: {'متاح' if PROMO_CODES_AVAILABLE else 'غير متاح'}")
    print(f"👨‍💼 عدد المديرين: {len(ADMIN_IDS) if ADMIN_IDS else 'غير محدد (مفتوح للجميع)'}")
    
    print("\n✅ تم تسجيل الأوامر التالية:")
    print("   - /start")
    print("   - /help") 
    print("   - /create_trial_code")
    print("   - /create_discount_code")
    print("   - /redeem")
    print("   - /list_codes")
    print("   - /test_promo")
    
    print("\n🚀 بدء تشغيل البوت...")
    executor.start_polling(dp, skip_updates=True)
