# config.py
bot_token="7703134125:AAHp_d5UzgYgY-Kp_4lqg5cVoTS-HIlZSLU"
# Admin IDs
ADMIN_IDS = [868182073]  # Replace with actual admin IDs
# Google Sheets configuration
sheet_name = "stock"

# File paths
file_path = "/mnt/c/Users/<USER>/OneDrive/Documents/stocks/stock_synco.xlsx"
financial_data_file="/mnt/c/Users/<USER>/OneDrive/Documents/stocks/financial_data.csv"
#chart data path
chart_data_dir ="/mnt/c/Users/<USER>/OneDrive/Documents/stocks/meta2"


# Status mappings
STATUS_MAP = {
    "0": "بداية ايجابية",
    "1": "ايجابى مستمر",
    "2": "بداية سلبية",
    "3": "سلبى مستمر",
    "4": "ايجابى"
}

# Money flow mappings
MONEY_FLOW_MAP = {
    "1": "سيوله داخلة",
    "-1": "سيوله خارجة",
    "0": "سيوله متعادله"
}

# HV mappings
HV_MAP = {
    "in": "1",
    "out": "-1"
}

# Bot commands and messages
HELP_MESSAGE = '''
مثال لطلب سهم معين يكتب كما فى المثال على التجارى  
/stock comi 
لطلب اسهم السيوله الداخلة عن اليوم لحظيا وقت الجلسه  

/hv in 
لطلب اسهم السيوله الخارجه عن اليوم لحظيا وقت الجلسه  

/hv out 
لمعرفه اسهم المتاجرة و الزيرو عن اليوم  

/modarba 
لمعرفه تفاصيل اسهم الزيرو اثناء جلسه التداول واهدافها وما تحقق منها  

/modarba_all 

لمعرفه مستويات ارتداد فيبوناتشى و امتداد فيبوناتشى بين القمه المتكونه و القاع المتكون دون الرسم على الشارت وبدقه
/fibo high low 
لاظهار شارت السهم وعليها المؤشرات الفنيه يتم كتابه الأمر التالى متبوع بكود السهم المطلوب 
/chart 
للحصول على تحليل ذكي متكامل للسهم يرجى كتابة الأمر التالي متبوع بكود السهم
/analyze comi
لمعرفه اسعار الاشتراك و العروض ان وجدت
/subscribe 
لمعرفه موعد انتهاء تاريخ الاشتراك الخاص بك
/mysubscribtion
لأظهار قائمة الأوامر المتاحه لك  
/menu  
لأظهار قائمه المساعده 
/help

فيديو توضيحى لكيفيه العمل على البوت 
https://youtu.be/2ywEgvh9fLo
'''

SUBSCRIBE_MESSAGE = '''
الاشتراك الشهري 500 ج 
يتم الدفع عن طريق فودافون كاش او انستاباى
قم باختيار مده الأشتراك وطريقه الدفع
و ارسل لنا رساله على @elborsa_bot
'''
