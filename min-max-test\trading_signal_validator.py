#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trading Signal Validation Helper
===============================

مساعد التحقق من صحة إشارات التداول
يتصل بالكود الفعلي في server.py ويختبر الوظائف الحقيقية

الاستخدام:
python trading_signal_validator.py
"""

import sys
import os
import json
from datetime import datetime, timedelta
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# محاولة استيراد الوظائف من server.py
try:
    # استيراد الوظائف المطلوبة من server.py
    from server import (
        get_last_achieved_target_price,
        get_last_achieved_target_date,
        calculate_actual_profit_with_highest_price,
        get_highest_price_between_dates
    )
    FUNCTIONS_AVAILABLE = True
    print("✅ تم استيراد الوظائف من server.py بنجاح")
except ImportError as e:
    print(f"⚠️ لا يمكن استيراد الوظائف من server.py: {e}")
    print("سيتم تشغيل الاختبارات النظرية فقط")
    FUNCTIONS_AVAILABLE = False

class TradingSignalValidator:
    """مدقق إشارات التداول مع الكود الفعلي"""
    
    def __init__(self):
        self.test_results = []
        self.validation_summary = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'errors': []
        }
    
    def create_test_sheet_data(self):
        """إنشاء بيانات شيت اختبار"""
        return {
            'sheet1_data': [
                ['COMI', 'البنك التجاري الدولي'],
                ['ETEL', 'المصرية للاتصالات'],
                ['SWDY', 'السويدي اليكتريك'],
                ['EGAL', 'مصر للألومنيوم']
            ],
            'sheet4_data': [
                # مثال: صفقة مع أهداف محققة
                ['ETEL', '', 't2done', 100, '2025-06-01', '', '', 110, 120, 130, 105, '2025-06-10', '2025-06-15', '', '', '', 100],
                # مثال: صفقة بدون أهداف
                ['COMI', '', 'buy', 100, '2025-06-01', '', '', 110, 120, 130, 90, '', '', '', '', '', 100],
                # مثال: صفقة مع جميع الأهداف محققة
                ['EGAL', '', 't3done', 100, '2025-06-01', '', '', 110, 120, 130, 115, '2025-06-05', '2025-06-10', '2025-06-15', '', '', 100]
            ]
        }
    
    def validate_profit_calculation_logic(self):
        """التحقق من منطق حساب الربح الفعلي"""
        
        if not FUNCTIONS_AVAILABLE:
            print("⏭️ تخطي اختبار حساب الربح - الوظائف غير متوفرة")
            return False
        
        print("\n🧮 اختبار منطق حساب الربح الفعلي")
        print("-" * 50)
        
        try:
            # إعداد بيانات اختبار
            test_cases = [
                {
                    'description': 'صفقة مع أهداف محققة وبيانات تاريخية',
                    'stock_code': 'ETEL',
                    'buy_price': 100,
                    'sell_price': 118,
                    'targets': [110, 120, 130],
                    'achieved_targets': ['t1done', 't2done'],
                    'last_target_date': '2025-06-15',
                    'expected_method': 'historical_data'
                },
                {
                    'description': 'صفقة مع أهداف محققة بدون بيانات تاريخية',
                    'stock_code': 'SWDY',
                    'buy_price': 80,
                    'sell_price': 88,
                    'targets': [88, 95, 102],
                    'achieved_targets': ['t1done'],
                    'last_target_date': '2025-06-10',
                    'expected_method': 'fallback_target'
                }
            ]
            
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n📊 حالة اختبار {i}: {test_case['description']}")
                
                # محاكاة إعداد البيانات
                mock_row = [
                    test_case['stock_code'],  # 0
                    '',  # 1
                    test_case['achieved_targets'][-1] if test_case['achieved_targets'] else 'buy',  # 2
                    test_case['buy_price'],  # 3
                    '2025-06-01',  # 4
                    '', '',  # 5, 6
                    test_case['targets'][0],  # 7
                    test_case['targets'][1],  # 8
                    test_case['targets'][2],  # 9
                    90,  # 10 - stop loss
                    '2025-06-05' if 't1done' in test_case['achieved_targets'] else '',  # 11
                    '2025-06-10' if 't2done' in test_case['achieved_targets'] else '',  # 12
                    '2025-06-15' if 't3done' in test_case['achieved_targets'] else '',  # 13
                    '', '', '',  # 14, 15, 16
                    test_case['buy_price']  # 17
                ]
                
                try:
                    # اختبار الوظائف الفعلية
                    if 't1done' in test_case['achieved_targets']:
                        last_target_price = get_last_achieved_target_price(mock_row)
                        last_target_date = get_last_achieved_target_date(mock_row)
                        
                        print(f"   آخر هدف محقق: {last_target_price} جنيه")
                        print(f"   تاريخ آخر هدف: {last_target_date}")
                        
                        # اختبار حساب الربح
                        actual_profit, calculation_details = calculate_actual_profit_with_highest_price(
                            mock_row, test_case['sell_price']
                        )
                        
                        print(f"   الربح الفعلي المحسوب: {actual_profit:.2f}%")
                        print(f"   تفاصيل الحساب: {calculation_details}")
                        
                        # التحقق من صحة النتائج
                        if actual_profit is not None and calculation_details:
                            print(f"   ✅ نجح حساب الربح")
                            self.validation_summary['successful_validations'] += 1
                        else:
                            print(f"   ❌ فشل في حساب الربح")
                            self.validation_summary['failed_validations'] += 1
                    else:
                        print(f"   ⏭️ لا توجد أهداف محققة للاختبار")
                        self.validation_summary['successful_validations'] += 1
                
                except Exception as e:
                    print(f"   ❌ خطأ في الاختبار: {e}")
                    self.validation_summary['failed_validations'] += 1
                    self.validation_summary['errors'].append(f"Test case {i}: {str(e)}")
                
                self.validation_summary['total_validations'] += 1
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ عام في اختبار حساب الربح: {e}")
            self.validation_summary['errors'].append(f"Profit calculation: {str(e)}")
            return False
    
    def validate_position_enhancement_logic(self):
        """التحقق من منطق تعزيز المراكز"""
        
        print("\n💪 اختبار منطق تعزيز المراكز")
        print("-" * 50)
        
        # حالات اختبار تعزيز المراكز
        test_scenarios = [
            {
                'description': 'تعزيز مراكز صحيح - سعر أقل',
                'current_price': 100,
                'new_price': 95,
                'expected_result': 'enhance',
                'expected_improvement': 5.0
            },
            {
                'description': 'رفض تعزيز - سعر أعلى',
                'current_price': 100,
                'new_price': 105,
                'expected_result': 'reject',
                'expected_improvement': 0.0
            },
            {
                'description': 'تعزيز مراكز ممتاز - تحسن كبير',
                'current_price': 100,
                'new_price': 85,
                'expected_result': 'enhance',
                'expected_improvement': 15.0
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📈 سيناريو {i}: {scenario['description']}")
            
            # حساب تحسن السعر
            current_price = scenario['current_price']
            new_price = scenario['new_price']
            
            if new_price < current_price:
                improvement = ((current_price - new_price) / current_price) * 100
                decision = 'enhance'
                message = f"فرصة تعزيز مراكز - تحسن السعر: {improvement:.1f}%"
            else:
                improvement = 0.0
                decision = 'reject'
                message = "رفض التعزيز - السعر الجديد أعلى من الحالي"
            
            # التحقق من النتائج
            if decision == scenario['expected_result']:
                if abs(improvement - scenario['expected_improvement']) < 0.1:
                    print(f"   ✅ النتيجة صحيحة: {message}")
                    self.validation_summary['successful_validations'] += 1
                else:
                    print(f"   ❌ خطأ في حساب التحسن: متوقع {scenario['expected_improvement']}%, محسوب {improvement:.1f}%")
                    self.validation_summary['failed_validations'] += 1
            else:
                print(f"   ❌ قرار خاطئ: متوقع {scenario['expected_result']}, محسوب {decision}")
                self.validation_summary['failed_validations'] += 1
            
            self.validation_summary['total_validations'] += 1
        
        return True
    
    def validate_risk_analysis_logic(self):
        """التحقق من منطق تحليل المخاطر"""
        
        print("\n⚠️ اختبار منطق تحليل المخاطر")
        print("-" * 50)
        
        # حالات اختبار مختلفة للمخاطر
        risk_scenarios = [
            {
                'description': 'مخاطر عالية - عدة مؤشرات سلبية',
                'rsi': 78,
                'macd_diff': -1.2,
                'volume_ratio': 0.3,
                'adx': 12,
                'expected_level': 'عالي',
                'expected_factors': 4
            },
            {
                'description': 'مخاطر متوسطة - بعض المؤشرات السلبية',
                'rsi': 65,
                'macd_diff': -0.3,
                'volume_ratio': 0.7,
                'adx': 22,
                'expected_level': 'متوسط',
                'expected_factors': 2
            },
            {
                'description': 'مخاطر منخفضة - مؤشرات إيجابية',
                'rsi': 45,
                'macd_diff': 0.5,
                'volume_ratio': 1.3,
                'adx': 35,
                'expected_level': 'منخفض',
                'expected_factors': 0
            }
        ]
        
        for i, scenario in enumerate(risk_scenarios, 1):
            print(f"\n🎯 سيناريو مخاطر {i}: {scenario['description']}")
            
            # تحليل عوامل المخاطرة
            risk_factors = []
            
            if scenario['rsi'] > 70:
                risk_factors.append("تشبع شرائي")
            
            if scenario['macd_diff'] < -0.5:
                risk_factors.append("إشارة MACD سلبية قوية")
            elif scenario['macd_diff'] < 0:
                risk_factors.append("إشارة MACD سلبية")
            
            if scenario['volume_ratio'] < 0.5:
                risk_factors.append("حجم تداول منخفض")
            elif scenario['volume_ratio'] < 0.8:
                risk_factors.append("حجم تداول ضعيف")
            
            if scenario['adx'] < 20:
                risk_factors.append("ضعف في الاتجاه")
            
            # تحديد مستوى المخاطرة
            if len(risk_factors) >= 3:
                risk_level = "عالي"
            elif len(risk_factors) >= 1:
                risk_level = "متوسط"
            else:
                risk_level = "منخفض"
            
            # التحقق من النتائج
            factors_count = len(risk_factors)
            
            print(f"   المؤشرات: RSI={scenario['rsi']}, MACD={scenario['macd_diff']}, Volume={scenario['volume_ratio']}, ADX={scenario['adx']}")
            print(f"   عوامل المخاطرة المحددة: {factors_count} ({', '.join(risk_factors)})")
            print(f"   مستوى المخاطرة المحسوب: {risk_level}")
            
            # التحقق من الدقة
            if risk_level == scenario['expected_level']:
                print(f"   ✅ مستوى المخاطرة صحيح")
                self.validation_summary['successful_validations'] += 1
            else:
                print(f"   ❌ مستوى مخاطرة خاطئ: متوقع {scenario['expected_level']}, محسوب {risk_level}")
                self.validation_summary['failed_validations'] += 1
            
            self.validation_summary['total_validations'] += 1
        
        return True
    
    def validate_signal_routing_logic(self):
        """التحقق من منطق توجيه الإشارات"""
        
        print("\n🚦 اختبار منطق توجيه الإشارات")
        print("-" * 50)
        
        # حالات اختبار مختلفة للإشارات
        signal_scenarios = [
            {
                'description': 'إشارة شراء - سهم جديد',
                'signal_type': 'buy',
                'stock_code': 'NEWSTOCK',
                'has_position': False,
                'in_sheet1': False,
                'expected_action': 'add_to_sheet4_no_send'
            },
            {
                'description': 'إشارة شراء - تعزيز مراكز',
                'signal_type': 'buy',
                'stock_code': 'COMI',
                'has_position': True,
                'price_better': True,
                'expected_action': 'enhance_position'
            },
            {
                'description': 'إشارة بيع - صفقة مفتوحة',
                'signal_type': 'sell',
                'stock_code': 'ETEL',
                'has_position': True,
                'has_targets': True,
                'expected_action': 'calculate_profit_and_close'
            },
            {
                'description': 'إشارة وقف خسارة - بدون صفقة',
                'signal_type': 'tsl',
                'stock_code': 'NOSTOCK',
                'has_position': False,
                'expected_action': 'ignore_and_alert'
            }
        ]
        
        for i, scenario in enumerate(signal_scenarios, 1):
            print(f"\n📡 سيناريو إشارة {i}: {scenario['description']}")
            
            # محاكاة منطق توجيه الإشارة
            signal_type = scenario['signal_type']
            has_position = scenario.get('has_position', False)
            
            if signal_type == 'buy':
                if not has_position:
                    if not scenario.get('in_sheet1', True):
                        action = 'add_to_sheet4_no_send'
                        message = "إضافة سهم جديد بدون إرسال"
                    else:
                        action = 'new_position'
                        message = "إنشاء صفقة جديدة"
                else:
                    if scenario.get('price_better', False):
                        action = 'enhance_position'
                        message = "تعزيز المراكز"
                    else:
                        action = 'ignore_duplicate'
                        message = "تجاهل إشارة مكررة"
            
            elif signal_type in ['sell', 'tsl']:
                if has_position:
                    action = 'calculate_profit_and_close'
                    message = "حساب الربح وإغلاق الصفقة"
                else:
                    action = 'ignore_and_alert'
                    message = "تجاهل الإشارة وتنبيه الإدارة"
            
            elif signal_type in ['t1done', 't2done', 't3done']:
                if has_position:
                    action = 'update_target_status'
                    message = "تحديث حالة الهدف"
                else:
                    action = 'ignore_and_alert'
                    message = "تجاهل إشارة هدف بدون صفقة"
            
            else:
                action = 'unknown_signal'
                message = "نوع إشارة غير معروف"
            
            # التحقق من النتائج
            expected_action = scenario['expected_action']
            
            print(f"   نوع الإشارة: {signal_type}")
            print(f"   حالة الصفقة: {'موجودة' if has_position else 'غير موجودة'}")
            print(f"   الإجراء المحسوب: {action}")
            print(f"   الرسالة: {message}")
            
            if action == expected_action:
                print(f"   ✅ الإجراء صحيح")
                self.validation_summary['successful_validations'] += 1
            else:
                print(f"   ❌ إجراء خاطئ: متوقع {expected_action}, محسوب {action}")
                self.validation_summary['failed_validations'] += 1
            
            self.validation_summary['total_validations'] += 1
        
        return True
    
    def run_comprehensive_validation(self):
        """تشغيل التحقق الشامل من جميع الوظائف"""
        
        print("🔍 بدء التحقق الشامل من نظام إشارات التداول")
        print("=" * 60)
        print(f"تاريخ التحقق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"حالة الوظائف: {'متوفرة' if FUNCTIONS_AVAILABLE else 'غير متوفرة'}")
        
        # تشغيل جميع اختبارات التحقق
        validations = [
            ('حساب الربح الفعلي', self.validate_profit_calculation_logic),
            ('تعزيز المراكز', self.validate_position_enhancement_logic),
            ('تحليل المخاطر', self.validate_risk_analysis_logic),
            ('توجيه الإشارات', self.validate_signal_routing_logic)
        ]
        
        for validation_name, validation_func in validations:
            try:
                print(f"\n🧪 تشغيل اختبار: {validation_name}")
                validation_func()
                print(f"✅ انتهى اختبار: {validation_name}")
            except Exception as e:
                print(f"❌ خطأ في اختبار {validation_name}: {e}")
                self.validation_summary['errors'].append(f"{validation_name}: {str(e)}")
                traceback.print_exc()
        
        # إنتاج التقرير النهائي
        self.generate_validation_report()
        
        return self.validation_summary
    
    def generate_validation_report(self):
        """إنتاج تقرير التحقق النهائي"""
        
        total = self.validation_summary['total_validations']
        successful = self.validation_summary['successful_validations']
        failed = self.validation_summary['failed_validations']
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        print("\n" + "=" * 60)
        print("📋 تقرير التحقق النهائي من نظام إشارات التداول")
        print("=" * 60)
        print(f"📊 إجمالي عمليات التحقق: {total}")
        print(f"✅ التحققات الناجحة: {successful}")
        print(f"❌ التحققات الفاشلة: {failed}")
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        # تفاصيل الأخطاء
        if self.validation_summary['errors']:
            print(f"\n⚠️ الأخطاء المكتشفة ({len(self.validation_summary['errors'])}):")
            for i, error in enumerate(self.validation_summary['errors'], 1):
                print(f"   {i}. {error}")
        
        # تقييم الحالة العامة
        print(f"\n🎯 التقييم العام:")
        if success_rate >= 95:
            print("   🟢 ممتاز - النظام جاهز للإنتاج")
        elif success_rate >= 85:
            print("   🟡 جيد - يحتاج إلى تحسينات طفيفة")
        elif success_rate >= 70:
            print("   🟠 مقبول - يحتاج إلى مراجعة")
        else:
            print("   🔴 ضعيف - يحتاج إلى إصلاحات جوهرية")
        
        # توصيات
        print(f"\n💡 التوصيات:")
        if failed > 0:
            print("   - مراجعة وإصلاح النقاط الفاشلة")
            print("   - إجراء اختبارات إضافية")
        
        if not FUNCTIONS_AVAILABLE:
            print("   - التأكد من وجود server.py والوظائف المطلوبة")
            print("   - إجراء اختبار متكامل مع الكود الفعلي")
        
        print("   - توثيق أي تغييرات أو تحديثات")
        print("   - إجراء اختبار أداء تحت ضغط")
        
        # حفظ التقرير
        report_data = {
            'validation_summary': self.validation_summary,
            'timestamp': datetime.now().isoformat(),
            'functions_available': FUNCTIONS_AVAILABLE,
            'success_rate': success_rate
        }
        
        with open('validation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 تم حفظ تقرير التحقق في: validation_report.json")

def main():
    """الدالة الرئيسية لتشغيل التحقق"""
    
    print("🛠️ مدقق نظام إشارات التداول المحدث")
    print(f"الإصدار: 2.0")
    print(f"التاريخ: {datetime.now().strftime('%Y-%m-%d')}")
    print("=" * 60)
    
    # إنشاء وتشغيل المدقق
    validator = TradingSignalValidator()
    results = validator.run_comprehensive_validation()
    
    return results

if __name__ == "__main__":
    main()
