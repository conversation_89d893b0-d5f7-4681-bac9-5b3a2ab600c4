import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from mplfinance.original_flavor import candlestick_ohlc
import matplotlib.pyplot as plt
import matplotlib.dates as mpl_dates
from matplotlib.dates import DateFormatter
def get_stock_price(chart_data_path):
    df = pd.read_csv(chart_data_path, delimiter=',', header=None, skiprows=1, 
                     names=['TICKER', 'PER', 'DTYYYYMMDD', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OI'])
    df = df[['TICKER','DTYYYYMMDD', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE']]
    df = df.astype({'OPEN': float, 'HIGH': float, 'LOW': float, 'CLOSE': float})
    # Convert datetime column to pandas datetime format
    df['datetime'] = pd.to_datetime(df['DTYYYYMMDD'], format='%Y%m%d') + pd.to_timedelta(df['TIME'], unit='m')
    # Set datetime column as index
    df.set_index('datetime', inplace=True)
    # Select rows for the last year
    last_year = df.index[-1] - timedelta(days=180)
    df = df[~df.index.duplicated()]
    df = df.loc[last_year:]
    
    return df 
    
def is_support(df, i):
    cond1 = df['LOW'][i] < df['LOW'][i-1] 
    cond2 = df['LOW'][i] < df['LOW'][i+1] 
    cond3 = df['LOW'][i+1] < df['LOW'][i+2] 
    cond4 = df['LOW'][i-1] < df['LOW'][i-2]
    return (cond1 and cond2 and cond3 and cond4)

def is_resistance(df, i):
    cond1 = df['HIGH'][i] > df['HIGH'][i-1] 
    cond2 = df['HIGH'][i] > df['HIGH'][i+1] 
    cond3 = df['HIGH'][i+1] > df['HIGH'][i+2] 
    cond4 = df['HIGH'][i-1] > df['HIGH'][i-2]
    return (cond1 and cond2 and cond3 and cond4)

def is_far_from_level(value, levels, df):
    ave =  np.mean(df['HIGH'] - df['LOW'])
    return np.sum([abs(value - level) < ave for _, level in levels]) == 0

def plot_all(levels, df, chart_data_path):
    # Create a figure with two plot panels
    #fig,ax = plt.subplots(nrows=1, ncols=1, figsize=(16, 9), dpi=300, sharex=True)
    fig, ax = plt.subplots(nrows=1, ncols=1, figsize=(12, 8))

    # Plot candlestick chart in the first plot panel
    df['candle'] = df.apply(lambda row: (mpl_dates.date2num(row.name), row['OPEN'], row['HIGH'], row['LOW'], row['CLOSE']), axis=1)
    candlestick_ohlc(ax, df['candle'], width=0.6, colorup='green', colordown='red', alpha=0.8)
    date_format = DateFormatter('%d %b %Y')
    ax.xaxis.set_major_formatter(date_format)
    # Iterate over the support and resistance levels
    for level in levels:
        # Determine the color and y-position of the level line based on the current close price
        if df['CLOSE'][-1] < level[1]:
            color = 'red'
            y_pos = level[1] - (0.03 * (df['HIGH'].max() - df['LOW'].min()))
        else:
            color = 'green'
            y_pos = level[1] + (0.03 * (df['HIGH'].max() - df['LOW'].min()))

        # Plot the level line in both plot panels
        ax.hlines(level[1], xmin=df.index[level[0]], xmax=max(df.index), colors=color, linestyle='--')
        # Write the price of the level line next to it in the first plot panel
        ax.text(x=max(df.index), y=y_pos, s=str(level[1]), color=color, ha='right', va='center')
    # Set titles and axis labels for both plot panels
    ticker = df['TICKER'][0]
    ax.text(0.5,0.5, 'https://t.me/egx_stock_analyzer_bot', fontsize=20, color='gray', ha='center', va='center', alpha=0.3, transform=ax.transAxes)
    #ax.text(0.5,0.3, 'https://web.telegram.org/k/#@egx_stock_analyzer_bot', fontsize=20, color='gray', ha='center', va='center', alpha=0.3, transform=ax.transAxes, rotation=45)
    ax.set_title('{} - {}'.format(ticker, df.index[-1].strftime('%Y-%m-%d')), fontsize=20, color='red', alpha=0.3)
    ax.set_xlabel('Date')
    ax.set_ylabel('Price')
    # Save the chart to file
    chart_file_path = chart_data_path.replace('.TXT', '.png')
    fig.savefig(chart_file_path, dpi=100, bbox_inches='tight')
    plt.close()
