"""
This module provides a fix for the get_stock_data function
to handle non-numeric values in stock data.
"""

def fix_get_stock_data():
    """
    Replace the get_stock_data function in process_data.py to handle 
    non-numeric values safely
    """
    with open('process_data.py', 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Find the function definition
    if 'def get_stock_data(' in content:
        # Create a safer version of the function
        safer_function = """
def get_stock_data(file_path, stock_code, subscriber_type):
    \"""Fetch and format stock data.\"""
    df = pd.read_excel(file_path)
    stock_data = df[df.iloc[:, 0].astype(str) == stock_code].iloc[0]
    code, name, open_price, high, low, close = stock_data[:6]
    
    # Safely convert price values to float, using 0.0 as a fallback for invalid values
    try:
        highx = float(high) if high != '-' else 0.0
    except (ValueError, TypeError):
        highx = 0.0
        
    try:
        lowx = float(low) if low != '-' else 0.0
    except (ValueError, TypeError):
        lowx = 0.0
        
    try:
        closex = float(close) if close != '-' else 0.0
    except (ValueError, TypeError):
        closex = 0.0
    
    # Use a sensible rounding value
    rnd = 3 if closex < 2 else 2

    if subscriber_type in ["paid", "trail"]:
        stock_alert = get_stock_alert(sheet3, stock_code, closex)
    else:
        stock_alert = "هذا الجزء مخصص فقط للمشتركين"

    # Rest of the function continues unchanged
"""
        
        # Replace the function definition in the content
        import re
        pattern = r'def get_stock_data\(.*?"""\n(.*?)def '
        modified_content = re.sub(pattern, safer_function + '\n\ndef ', content, flags=re.DOTALL)
        
        # Save the modified file
        with open('process_data.py', 'w', encoding='utf-8') as file:
            file.write(modified_content)
        
        return True
    else:
        print("Could not find get_stock_data function in process_data.py")
        return False

if __name__ == '__main__':
    if fix_get_stock_data():
        print("Successfully updated get_stock_data function with error handling")
    else:
        print("Failed to update get_stock_data function")
