import datetime
import os
import logging
from typing import <PERSON><PERSON>, Optional
from aiogram.types import Message
from aiogram import <PERSON><PERSON>, <PERSON><PERSON>atch<PERSON>, types
from auth import open_google_sheet, dp, bot

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

# Constants
DAILY_FREE_LIMIT = 5
REFERRAL_BONUS_DAYS = 3
trail_PERIOD_DAYS = 7

class UserManager:
    @staticmethod
    def get_user_data(user_id: str) -> Optional[dict]:
        """Get user data from Google Sheets with error handling"""
        try:
            user_data = sheet2.find(str(user_id))
            if not user_data:
                return None
            
            row = user_data.row
            col = user_data.col
            row_values = sheet2.row_values(row)
            
            # Expected format: [user_id, count, subscription_type, end_date, first_name, last_name]
            return {
                'row': row,
                'col': col,
                'user_id': row_values[col - 1],
                'count': int(row_values[col]) if row_values[col] else 0,
                'subscription_type': row_values[col + 1] if len(row_values) > col + 1 else 'free',
                'end_date': row_values[col + 2] if len(row_values) > col + 2 else datetime.date.today().strftime("%Y-%m-%d"),
                'first_name': row_values[col + 3] if len(row_values) > col + 3 else '',
                'last_name': row_values[col + 4] if len(row_values) > col + 4 else ''
            }
        except Exception as e:
            logger.error(f"Error getting user data for {user_id}: {e}")
            return None
    
    @staticmethod
    def update_user_data(row: int, col: int, count: int = None, subscription_type: str = None, end_date: str = None):
        """Update user data in Google Sheets with error handling"""
        try:
            if count is not None:
                sheet2.update_cell(row, col + 1, count)
            if subscription_type is not None:
                sheet2.update_cell(row, col + 2, subscription_type)
            if end_date is not None:
                sheet2.update_cell(row, col + 3, end_date)
        except Exception as e:
            logger.error(f"Error updating user data: {e}")
    @staticmethod
    def add_new_user(user_id: str, user_fname: str = '', user_lname: str = '') -> str:
        """Add new user to Google Sheets"""
        try:
            end_date = (datetime.date.today() + datetime.timedelta(days=trail_PERIOD_DAYS)).strftime("%Y-%m-%d")            # Start with count = 0, will be incremented when first used
            user_info = [user_id, 0, "trail", end_date, user_fname or '', user_lname or '']
            sheet2.append_row(user_info)
            return end_date
        except Exception as e:
            logger.error(f"Error adding new user {user_id}: {e}")
            return None

async def handle_referral(referrer_id: str, new_user_id: str) -> bool:
    """Handle referral logic when new user joins"""
    try:
        if not referrer_id or str(referrer_id) == str(new_user_id):
            return False
        
        referrer_data = UserManager.get_user_data(referrer_id)
        if not referrer_data:
            return False
        
        # Calculate new end date for referrer
        if referrer_data['subscription_type'] == 'free':
            # Convert free user to trail
            new_end_date = (datetime.date.today() + datetime.timedelta(days=REFERRAL_BONUS_DAYS)).strftime("%Y-%m-%d")
            UserManager.update_user_data(
                referrer_data['row'], 
                referrer_data['col'], 
                subscription_type='trail',
                end_date=new_end_date
            )
        else:
            # Extend existing subscription
            current_end_date = datetime.datetime.strptime(referrer_data['end_date'], "%Y-%m-%d").date()
            # If subscription is expired, start from today
            if current_end_date < datetime.date.today():
                current_end_date = datetime.date.today()
            
            new_end_date = (current_end_date + datetime.timedelta(days=REFERRAL_BONUS_DAYS)).strftime("%Y-%m-%d")
            UserManager.update_user_data(
                referrer_data['row'], 
                referrer_data['col'], 
                end_date=new_end_date
            )
        
        # Notify referrer
        await bot.send_message(
            chat_id=referrer_id, 
            text=f'🎉 تهانينا! لقد قمت بإحالة مستخدم جديد!\n'
                 f'تم تمديد اشتراكك إلى {new_end_date}\n\n'
                 f'🎉 Congratulations! You referred a new user!\n'
                 f'Your subscription has been extended to {new_end_date}'
        )
        return True
        
    except Exception as e:
        logger.error(f"Error handling referral from {referrer_id} to {new_user_id}: {e}")
        return False

@dp.message_handler(commands=['myrefer'])
async def display_refer_link(message: Message):
    """Display referral link to user"""
    user_id = message.from_user.id
    refer_link = f'https://t.me/egx_stock_analyzer_bot?start={user_id}'

    # Improved bilingual message
    en_message = (
        "🔗 **رابط الإحالة الشخصي**\n\n"
        f"ادع أصدقائك للانضمام واحصل على **{REFERRAL_BONUS_DAYS} أيام** عضوية مدفوعة لكل عضو جديد!\n\n"
        "🔗 **Your Personal Referral Link**\n\n"
        f"Invite friends and get **{REFERRAL_BONUS_DAYS} days** of paid membership for each new member!\n\n"
        f"🔗 {refer_link}"
    )
    
    try:
        await bot.send_message(chat_id=user_id, text=en_message)
    except Exception as e:
        logger.error(f"Error sending referral link to {user_id}: {e}")

@dp.message_handler(commands=['start'])
async def check_user_limit(message: Message) -> Tuple[str, int]:
    """Check user subscription status and limits with improved error handling"""
    user_id = message.from_user.id
    user_fname = message.from_user.first_name or ''
    user_lname = message.from_user.last_name or ''
    referrer_id = message.get_args()

    try:
        # Handle referral logic for new users
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data and referrer_id:
            await handle_referral(referrer_id, str(user_id))        # Check user subscription status
        if not user_data:
            # New user - add to sheet
            end_date = UserManager.add_new_user(str(user_id), user_fname, user_lname)
            if end_date:
                welcome_msg = (
                    f"🎉 أهلاً بك! لديك حساب تجريبي ينتهي في {end_date}\n"
                    f"🎉 Welcome! You have a trail account ending on {end_date}"
                )
                await bot.send_message(chat_id=user_id, text=welcome_msg)
                # Get the user data again and update count to 1
                user_data_new = UserManager.get_user_data(str(user_id))
                if user_data_new:
                    UserManager.update_user_data(user_data_new['row'], user_data_new['col'], count=1)
                return "trail", 1
            else:
                await bot.send_message(chat_id=user_id, text="حدث خطأ، يرجى المحاولة مرة أخرى\nSystem error, please try again")
                return "free", 0
        
        # Existing user - check status
        subscription_type = user_data['subscription_type']
        count = user_data['count']
        
        try:
            end_date = datetime.datetime.strptime(user_data['end_date'], "%Y-%m-%d").date()
        except:
            end_date = datetime.date.today()
        
        today = datetime.date.today()
        
        # Check if subscription expired
        if subscription_type == "trail" and today > end_date:
            # trail expired, convert to free
            UserManager.update_user_data(
                user_data['row'], 
                user_data['col'], 
                count=0,
                subscription_type="free",
                end_date=today.strftime("%Y-%m-%d")
            )
            await bot.send_message(chat_id=user_id, text="انتهت فترتك التجريبية، يرجى الاشتراك\ntrail period ended, please subscribe")
            return "free", 0
        elif subscription_type == "paid" and today > end_date:
            # Paid subscription expired, convert to free
            UserManager.update_user_data(
                user_data['row'], 
                user_data['col'], 
                count=0,
                subscription_type="free",
                end_date=today.strftime("%Y-%m-%d")
            )
            await bot.send_message(chat_id=user_id, text=f"انتهى اشتراكك في {end_date}، يرجى التجديد\nSubscription ended on {end_date}, please renew")
            return "free", 0
          # Reset daily count if it's a new day for free users
        if subscription_type == "free":
            last_update = datetime.datetime.strptime(user_data['end_date'], "%Y-%m-%d").date()
            if today > last_update:
                # New day, reset count and increment by 1 for current usage
                UserManager.update_user_data(
                    user_data['row'], 
                    user_data['col'], 
                    count=1,
                    end_date=today.strftime("%Y-%m-%d")
                )
                remaining = DAILY_FREE_LIMIT - 1
                await bot.send_message(chat_id=user_id, text=f"يوم جديد! لديك {remaining} تقارير متبقية\nNew day! You have {remaining} reports remaining")
                return "free", 1
            else:
                # Same day - check if limit exceeded BEFORE incrementing
                if count >= DAILY_FREE_LIMIT:
                    await bot.send_message(chat_id=user_id, text=f"عفواً، نفدت التقارير المجانية اليومية ({DAILY_FREE_LIMIT} تقارير)\nSorry, daily free reports limit exceeded ({DAILY_FREE_LIMIT} reports)")
                    return "free", count
        
        # For trail/paid users or free users within limit - update usage count
        new_count = count + 1
        UserManager.update_user_data(user_data['row'], user_data['col'], count=new_count)
        
        # Send remaining quota message for free users
        if subscription_type == "free":
            remaining = DAILY_FREE_LIMIT - new_count
            if remaining > 0:
                await bot.send_message(chat_id=user_id, text=f"لديك {remaining} تقارير متبقية اليوم\nYou have {remaining} reports remaining today")
            elif remaining == 0:
                await bot.send_message(chat_id=user_id, text=f"هذا آخر تقرير مجاني لك اليوم\nThis is your last free report for today")
        
        return subscription_type, new_count
        
    except Exception as e:
        logger.error(f"Error in check_user_limit for user {user_id}: {e}")
        await bot.send_message(chat_id=user_id, text="حدث خطأ في النظام\nSystem error occurred")
        return "free", 0

# Debug function to check user data (you can remove this later)
@dp.message_handler(commands=['debug'])
async def debug_user_data(message: Message):
    """Debug command to check user data"""
    user_id = message.from_user.id
    
    user_data = UserManager.get_user_data(str(user_id))
    if user_data:
        debug_msg = (
            f"🔍 معلومات المستخدم / User Info:\n"
            f"ID: {user_data['user_id']}\n"
            f"Count: {user_data['count']}\n"
            f"Type: {user_data['subscription_type']}\n"
            f"End Date: {user_data['end_date']}\n"
            f"Daily Limit: {DAILY_FREE_LIMIT}"
        )
    else:
        debug_msg = "❌ لم يتم العثور على بيانات المستخدم\nUser data not found"
    
    await bot.send_message(chat_id=user_id, text=debug_msg)

# Helper function to check user status without updating count
async def get_user_status(user_id: str) -> Tuple[str, int]:
    """Get user status without updating usage count - for API calls"""
    try:
        user_data = UserManager.get_user_data(str(user_id))
        if not user_data:
            return "free", 0
        
        subscription_type = user_data['subscription_type']
        count = user_data['count']
        
        try:
            end_date = datetime.datetime.strptime(user_data['end_date'], "%Y-%m-%d").date()
        except:
            end_date = datetime.date.today()
        
        # Check if subscription expired
        if subscription_type in ["trail", "paid"] and datetime.date.today() > end_date:
            return "free", count
        
        return subscription_type, count
        
    except Exception as e:
        logger.error(f"Error getting user status for {user_id}: {e}")
        return "free", 0

# Function to check subscription without counting usage
async def check_subscription_only(message: Message) -> str:
    """Check user subscription status without updating usage count"""
    user_id = message.from_user.id
    subscription_type, _ = await get_user_status(str(user_id))
    return subscription_type

# Function for subscription-only commands
async def check_subscription_access(message: Message) -> bool:
    """Check if user has subscription access without updating count"""
    subscription_type = await check_subscription_only(message)
    if subscription_type not in ["paid", "trail"]:
        await message.reply(
            "عذراً، هذا الأمر متاح للمشتركين فقط\n"
            "Sorry, this command is only available for paid subscribers.\n"
            "للاشتراك: /subscribe"
        )
        return False
    return True