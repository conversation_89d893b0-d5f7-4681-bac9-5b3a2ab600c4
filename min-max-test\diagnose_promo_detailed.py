#!/usr/bin/env python3
"""
اختبار تشخيصي مفصّل لفهم سبب عدم عمل أوامر البرومو كود
"""

import sys
import os

def diagnose_promo_commands():
    """تشخيص مفصّل لمشاكل أوامر البرومو كود"""
    print("🔍 تشخيص مفصّل لأوامر البرومو كود...")
    print("="*60)
    
    # 1. فحص استيراد المتطلبات
    print("\n1️⃣ فحص الاستيرادات:")
    
    try:
        print("   📦 محاولة استيراد aiogram...")
        from aiogram import Bot, Dispatcher
        print("   ✅ aiogram - تم الاستيراد")
    except Exception as e:
        print(f"   ❌ aiogram - فشل: {e}")
        return
    
    try:
        print("   📦 محاولة استيراد auth...")
        from auth import dp, bot
        print("   ✅ auth - تم الاستيراد")
    except Exception as e:
        print(f"   ❌ auth - فشل: {e}")
        return
    
    # 2. محاكاة تحميل نظام الأكواد
    print("\n2️⃣ محاكاة تحميل نظام الأكواد:")
    PROMO_CODES_AVAILABLE = False
    
    try:
        print("   🔍 محاولة تحميل نظام الأكواد...")
        from promo_codes import PromoCodeManager
        from user_limit import UserManager
        import datetime
        PROMO_CODES_AVAILABLE = True
        print("   ✅ تم تحميل نظام الأكواد بنجاح")
    except ImportError as e:
        print(f"   ❌ فشل استيراد نظام الأكواد (ImportError): {e}")
    except Exception as e:
        print(f"   ❌ فشل تحميل نظام الأكواد (Exception): {e}")
    
    print(f"   📊 PROMO_CODES_AVAILABLE: {PROMO_CODES_AVAILABLE}")
    
    # 3. فحص وجود الدوال
    print("\n3️⃣ فحص وجود دوال الأوامر:")
    
    # استيراد main.py كموديول
    try:
        import main
        
        # فحص الدوال الأساسية
        basic_functions = [
            'create_trial_code_command',
            'create_discount_code_command',
            'redeem_promo_code', 
            'list_promo_codes'
        ]
        
        # فحص الدوال البديلة
        fallback_functions = [
            'simple_create_trial_code_command',
            'simple_create_discount_code_command',
            'simple_redeem_promo_code',
            'simple_list_promo_codes'
        ]
        
        print("   📋 الدوال الأساسية:")
        for func in basic_functions:
            if hasattr(main, func):
                print(f"   ✅ {func}")
            else:
                print(f"   ❌ {func}")
        
        print("   📋 الدوال البديلة:")
        for func in fallback_functions:
            if hasattr(main, func):
                print(f"   ✅ {func}")
            else:
                print(f"   ❌ {func}")
                
    except Exception as e:
        print(f"   ❌ فشل استيراد main.py: {e}")
        return
    
    # 4. محاكاة تسجيل الأوامر
    print("\n4️⃣ محاكاة تسجيل الأوامر:")
    
    commands = ['create_trial_code', 'create_discount_code', 'redeem', 'list_codes']
    
    if PROMO_CODES_AVAILABLE:
        print("   🎫 سيتم تسجيل أوامر الأكواد الكاملة:")
        for cmd in commands:
            print(f"   ✅ dp.message_handler(commands=['{cmd}'])")
        print("   ✅ تم تسجيل أوامر الأكواد الكاملة بنجاح!")
    else:
        print("   ⚠️ سيتم تسجيل أوامر الأكواد البديلة:")
        for cmd in commands:
            print(f"   ⚠️ dp.message_handler(commands=['{cmd}']) -> simple_{cmd.replace('create_', 'create_').replace('list_', 'list_')}")
        print("   ⚠️ تم تسجيل أوامر الأكواد البديلة - النظام الكامل غير متاح")
    
    # 5. فحص المعالج العام
    print("\n5️⃣ فحص المعالج العام:")
    print("   📝 المعالج العام @dp.message_handler() سيُسجل أخيراً")
    print("   ✅ process_callback_messages يتجاهل الأوامر التي تبدأ بـ /")
    
    # 6. ملخص التوقعات
    print("\n6️⃣ التوقعات:")
    print("   🎯 ما يجب أن يحدث عند اختبار الأوامر:")
    
    for cmd in commands:
        if PROMO_CODES_AVAILABLE:
            print(f"   ✅ /{cmd} -> يجب أن يعمل بالكامل")
        else:
            print(f"   ⚠️ /{cmd} -> 'نظام الأكواد غير متاح حالياً'")
    
    print(f"\n   ❌ إذا لم تستجب الأوامر نهائياً:")
    print(f"      - مشكلة في تسجيل المعالجات")
    print(f"      - المعالج العام يعترض الأوامر")
    print(f"      - خطأ في المسافات (indentation)")
    
    return True

def create_test_command():
    """إنشاء أمر اختبار بسيط"""
    print("\n🧪 إنشاء أمر اختبار بسيط...")
    
    test_command_code = '''
# إضافة أمر اختبار بسيط إلى main.py
async def test_promo_command(message):
    """أمر اختبار بسيط للتأكد من أن تسجيل الأوامر يعمل"""
    await message.reply("🧪 أمر الاختبار يعمل! نظام تسجيل الأوامر سليم.")

# إضافة هذا السطر بعد تسجيل الأوامر الأخرى:
# dp.message_handler(commands=['test_promo'])(test_promo_command)
'''
    
    print("   📝 كود أمر الاختبار:")
    print(test_command_code)
    
    return test_command_code

if __name__ == "__main__":
    success = diagnose_promo_commands()
    
    if success:
        print("\n" + "="*60)
        print("📋 خطة الاختبار:")
        print("1. شغّل البوت وراقب رسائل التشخيص")
        print("2. اختبر /create_trial_code (يجب أن يستجيب)")
        print("3. إذا لم يستجب، أضف أمر اختبار بسيط")
        print("4. راقب اللوج للرسائل التشخيصية")
        
        # إنشاء أمر اختبار
        create_test_command()
        
        print("\n✅ التشخيص مكتمل!")
    else:
        print("\n❌ فشل التشخيص - هناك مشاكل أساسية")
