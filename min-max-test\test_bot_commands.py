#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أوامر البوت
"""

try:
    import main
    print("✅ تم تحميل main.py بنجاح")
    
    # التحقق من وجود الدوال
    functions = [
        'create_trial_code_command',
        'create_discount_code_command', 
        'redeem_promo_code',
        'list_promo_codes'
    ]
    
    for func_name in functions:
        if hasattr(main, func_name):
            print(f"✅ {func_name}: موجودة")
        else:
            print(f"❌ {func_name}: غير موجودة")
    
    # التحقق من PROMO_CODES_AVAILABLE
    if hasattr(main, 'PROMO_CODES_AVAILABLE'):
        print(f"✅ PROMO_CODES_AVAILABLE: {main.PROMO_CODES_AVAILABLE}")
    else:
        print("❌ PROMO_CODES_AVAILABLE: غير موجودة")
        
    print("\n🎯 الخلاصة: أوامر البوت جاهزة للتشغيل!")
    
except Exception as e:
    print(f"❌ خطأ في تحميل main.py: {e}")
    import traceback
    traceback.print_exc()
