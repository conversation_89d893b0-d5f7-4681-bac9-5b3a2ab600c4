"""
Enhanced technical analysis functions for Egyptian stock market

This module provides advanced technical analysis capabilities including:
- Market breadth analysis
- Liquidity analysis
- Sector rotation detection
- Money flow calculations
"""

import numpy as np
import pandas as pd
import ta
import logging
import os
import pytest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

logger = logging.getLogger(__name__)

# Test Fixtures and Data Setup
@pytest.fixture
def sample_market_data_dir(tmp_path):
    """Create sample market data directory with test files"""
    # Create sample stock data files
    valid_stock = tmp_path / "STOCK1.TXT"
    valid_stock.write_text(
        "TICKER,PER,DATE,TIME,OPEN,HIGH,LOW,CLOSE,VOL,OPENINT\n"
        "STOCK1,D,20240301,1000,10.0,10.5,9.8,10.2,10000,0\n"
        "STOCK1,D,20240302,1000,10.2,10.8,10.1,10.7,12000,0\n"
    )
    
    invalid_stock = tmp_path / "BAD.TXT"
    invalid_stock.write_text("invalid,data\n")
    
    return tmp_path

@pytest.fixture
def sample_price_data():
    """Generate sample DataFrame for money flow tests"""
    return pd.DataFrame({
        'HIGH': [10.5, 10.8, 11.0, 10.7, 10.9],
        'LOW': [9.8, 10.1, 10.5, 10.3, 10.4],
        'CLOSE': [10.2, 10.7, 10.9, 10.6, 10.8],
        'VOL': [10000, 12000, 15000, 13000, 14000]
    })

class TestTechnicalAnalysis:
    """Test suite for technical analysis functions"""
    
    def test_calculate_market_breadth_success(self, sample_market_data_dir):
        """Test market breadth calculation with valid data"""
        result = calculate_market_breadth(sample_market_data_dir, days_back=2)
        
        assert result is not None
        assert result['stocks_analyzed'] == 1  # Only valid stock in sample
        assert result['advance_decline_ratio'] > 1.0
        assert 'STOCK1' in result['bullish_stocks']
        
    def test_calculate_market_breadth_invalid_dir(self):
        """Test market breadth with invalid directory"""
        result = calculate_market_breadth("/invalid/path")
        assert result is None
        
    def test_analyze_money_flow_basic(self, sample_price_data):
        """Test money flow analysis with valid data"""
        result = analyze_money_flow(sample_price_data, period=5)
        
        assert result is not None
        assert 'mfi' in result['values']
        assert isinstance(result['data_frame'], pd.DataFrame)
        assert 0 <= result['values']['mfi'] <= 100
        
    def test_analyze_money_flow_missing_volume(self):
        """Test money flow analysis with missing volume data"""
        test_df = pd.DataFrame({'HIGH': [10], 'LOW': [9], 'CLOSE': [9.5]})
        result = analyze_money_flow(test_df)
        assert result is None

def calculate_market_breadth(market_data_dir, days_back=30):
    """
    Calculate market breadth metrics from directory of stock data files
    
    Args:
        market_data_dir: Directory containing stock data files
        days_back: Number of days to analyze
        
    Returns:
        dict: Market breadth indicators
    """
    try:
        # Initialize metrics
        metrics = {
            'advance_decline_ratio': None,
            'new_highs_ratio': None,
            'new_lows_ratio': None,
            'above_ma50_ratio': None,
            'bullish_stocks': [],
            'bearish_stocks': [],
            'breadth_trend': None,
            'breadth_strength': None,
            'stocks_analyzed': 0
        }
        
        # Collect stats
        advancing = 0
        declining = 0
        new_highs = 0
        new_lows = 0
        above_ma50 = 0
        total_stocks = 0
        bullish_stocks = []
        bearish_stocks = []
        
        # Get all stock files
        stock_files = [f for f in os.listdir(market_data_dir) if f.endswith('.TXT')]
        
        # Process each stock
        for file in stock_files:
            try:
                file_path = os.path.join(market_data_dir, file)
                stock_code = file.split('.')[0]
                
                # Skip indices
                if stock_code.startswith('^') or stock_code.startswith('EGX'):
                    continue
                
                # Read the stock data
                # Read CSV without headers and validate structure
                df = pd.read_csv(file_path, delimiter=',', header=None)
                
                # Basic validation - require at least 10 columns for OHLC data
                if len(df.columns) < 10 or len(df) < days_back:
                    continue
                
                # Rename columns first before type conversion
                df.columns = ['TICKER', 'PER', 'DATE', 'TIME', 'OPEN', 'HIGH', 'LOW', 'CLOSE', 'VOL', 'OPENINT']
                
                # Convert OHLC columns to numeric
                ohlc_columns = ['OPEN', 'HIGH', 'LOW', 'CLOSE']
                for col in ohlc_columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                
                # Get recent data
                recent_data = df.iloc[-days_back:]
                
                # Check advance/decline
                last_close = recent_data.iloc[-1]['CLOSE']
                prev_close = recent_data.iloc[-2]['CLOSE'] if len(recent_data) > 1 else last_close
                
                if last_close > prev_close:
                    advancing += 1
                    if last_close > recent_data['CLOSE'].iloc[:-1].max():
                        new_highs += 1
                elif last_close < prev_close:
                    declining += 1
                    if last_close < recent_data['CLOSE'].iloc[:-1].min():
                        new_lows += 1
                
                # Calculate MA50
                ma50 = recent_data['CLOSE'].rolling(window=min(50, len(recent_data))).mean().iloc[-1]
                if last_close > ma50:
                    above_ma50 += 1
                
                # Check bullish/bearish trend
                short_ma = recent_data['CLOSE'].rolling(window=min(10, len(recent_data))).mean().iloc[-1]
                medium_ma = recent_data['CLOSE'].rolling(window=min(30, len(recent_data))).mean().iloc[-1]
                
                # Handle cases where moving averages are equal
                if last_close > short_ma and short_ma >= medium_ma:
                    bullish_stocks.append(stock_code)
                elif last_close < short_ma and short_ma <= medium_ma:
                    bearish_stocks.append(stock_code)
                
                total_stocks += 1
                
            except Exception as e:
                logger.warning(f"Error processing {file}: {e}")
                continue
        
        # Calculate final metrics
        if total_stocks > 0:
            metrics['stocks_analyzed'] = total_stocks
            # Handle edge cases for advance/decline ratio
            if advancing + declining == 0:
                metrics['advance_decline_ratio'] = 1.0  # Neutral ratio
            else:
                metrics['advance_decline_ratio'] = (advancing + 0.5) / (declining + 0.5)  # Add smoothing
            metrics['new_highs_ratio'] = new_highs / total_stocks
            metrics['new_lows_ratio'] = new_lows / total_stocks
            metrics['above_ma50_ratio'] = above_ma50 / total_stocks
            metrics['bullish_stocks'] = bullish_stocks
            metrics['bearish_stocks'] = bearish_stocks
            
            # Determine market breadth trend
            if metrics['advance_decline_ratio'] > 1.5 and metrics['above_ma50_ratio'] > 0.65:
                metrics['breadth_trend'] = 'strong_bullish'
                metrics['breadth_strength'] = 'high'
            elif metrics['advance_decline_ratio'] > 1.0 and metrics['above_ma50_ratio'] > 0.55:
                metrics['breadth_trend'] = 'bullish'
                metrics['breadth_strength'] = 'medium'
            elif metrics['advance_decline_ratio'] < 0.5 and metrics['above_ma50_ratio'] < 0.4:
                metrics['breadth_trend'] = 'strong_bearish'
                metrics['breadth_strength'] = 'high'
            elif metrics['advance_decline_ratio'] < 1.0 and metrics['above_ma50_ratio'] < 0.5:
                metrics['breadth_trend'] = 'bearish'
                metrics['breadth_strength'] = 'medium'
            else:
                metrics['breadth_trend'] = 'neutral'
                metrics['breadth_strength'] = 'low'
        
        return metrics
    
    except Exception as e:
        logger.error(f"Error calculating market breadth: {e}")
        return None

def analyze_money_flow(df, period=14):
    """
    Analyze money flow for a stock using the Money Flow Index (MFI)
    
    Args:
        df: DataFrame with OHLC and volume data
        period: Lookback period for calculations
        
    Returns:
        dict: Money flow analysis results
    """
    try:
        # Ensure we have volume data
        if 'VOL' not in df.columns:
            return None
        
        # Calculate typical price
        df['typical_price'] = (df['HIGH'] + df['LOW'] + df['CLOSE']) / 3
        
        # Calculate raw money flow
        df['money_flow'] = df['typical_price'] * df['VOL']
        
        # Determine positive and negative money flow
        df['positive_flow'] = 0.0
        df['negative_flow'] = 0.0
        
        # Determine positive or negative flow with boundary checks
        if len(df) >= 2:
            for i in range(1, len(df)):
                current_idx = df.index[i]
                if df['typical_price'].iloc[i] > df['typical_price'].iloc[i-1]:
                    df.at[current_idx, 'positive_flow'] = df['money_flow'].iloc[i]
                else:
                    df.at[current_idx, 'negative_flow'] = df['money_flow'].iloc[i]
        
        # Calculate money flow ratio and index
        df['positive_flow_sum'] = df['positive_flow'].rolling(window=period).sum()
        df['negative_flow_sum'] = df['negative_flow'].rolling(window=period).sum()
        
        # Money Flow Ratio
        df['money_flow_ratio'] = df['positive_flow_sum'] / df['negative_flow_sum'].replace(0, 1e-10)
        
        # Money Flow Index
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        
        # Calculate additional money flow metrics
        
        # Volume Weighted Price
        df['volume_weighted_price'] = (df['CLOSE'] * df['VOL']).rolling(window=period).sum() / \
                                     df['VOL'].rolling(window=period).sum().replace(0, 1e-10)
        
        # On Balance Volume (OBV)
        df['obv'] = 0
        for i in range(1, len(df)):
            if df['CLOSE'].iloc[i] > df['CLOSE'].iloc[i-1]:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] + df['VOL'].iloc[i]
            elif df['CLOSE'].iloc[i] < df['CLOSE'].iloc[i-1]:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1] - df['VOL'].iloc[i]
            else:
                df.loc[df.index[i], 'obv'] = df['obv'].iloc[i-1]
        
        # Force Index
        df['force_index'] = df['CLOSE'].diff(1) * df['VOL']
        df['force_index_ema'] = df['force_index'].ewm(span=13, adjust=False).mean()
        
        # Accumulation/Distribution Line
        df['money_flow_multiplier'] = ((df['CLOSE'] - df['LOW']) - (df['HIGH'] - df['CLOSE'])) / \
                                     (df['HIGH'] - df['LOW']).replace(0, 1e-10)
        df['money_flow_volume'] = df['money_flow_multiplier'] * df['VOL']
        df['adl'] = df['money_flow_volume'].cumsum()
        
        # Get latest values
        latest_values = {
            'mfi': df['mfi'].iloc[-1],
            'obv': df['obv'].iloc[-1],
            'obv_change': df['obv'].iloc[-1] - df['obv'].iloc[-min(6, len(df))],
            'force_index': df['force_index_ema'].iloc[-1],
            'adl': df['adl'].iloc[-1],
            'adl_change': df['adl'].iloc[-1] - df['adl'].iloc[-min(6, len(df))]  # 5-day change with bounds check
        }
        
        # Determine money flow trend
        if latest_values['mfi'] > 80:
            money_flow_trend = 'overbought'
        elif latest_values['mfi'] < 20:
            money_flow_trend = 'oversold'
        elif latest_values['mfi'] > 50 and latest_values['obv_change'] > 0:
            money_flow_trend = 'bullish'
        elif latest_values['mfi'] < 50 and latest_values['obv_change'] < 0:
            money_flow_trend = 'bearish'
        else:
            money_flow_trend = 'neutral'
        
        # Determine divergences
        # Handle insufficient data length in divergence calculation
        lookback = min(6, len(df))
        price_change = df['CLOSE'].iloc[-1] - df['CLOSE'].iloc[-lookback]
        mfi_change = df['mfi'].iloc[-1] - df['mfi'].iloc[-lookback]
        
        if price_change > 0 and mfi_change < 0:
            divergence = 'bearish'
        elif price_change < 0 and mfi_change > 0:
            divergence = 'bullish'
        else:
            divergence = None
        
        # Final results
        results = {
            'values': latest_values,
            'trend': money_flow_trend,
            'divergence': divergence,
            'data_frame': df[['mfi', 'obv', 'force_index_ema', 'adl']]
        }
        
        return results
    
    except Exception as e:
        logger.error(f"Error analyzing money flow: {e}")
        return None

def detect_candlestick_patterns(df, lookback=5):
    """
    اكتشاف أنماط الشموع اليابانية الشائعة

    Args:
        df: إطار البيانات مع أسعار OHLC
        lookback: عدد الشموع التي سيتم فحصها

    Returns:
        dict: قاموس يحتوي على أنماط الشموع المكتشفة
    """
    try:
        patterns = {}
        
        # التأكد من وجود عدد كاف من البيانات
        if len(df) < lookback + 2:
            return patterns
        
        # حصول على البيانات الأخيرة
        recent_data = df.iloc[-lookback-2:].copy()
        
        # التأكد من صحة البيانات
        if any(pd.isna(recent_data['OPEN'])) or any(pd.isna(recent_data['CLOSE'])):
            return patterns
        
        # متوسط حجم الشموع للمقارنة
        avg_candle_size = np.mean(np.abs(recent_data['CLOSE'] - recent_data['OPEN']))
        
        # تكوين نموذج المطرقة Hammer
        for i in range(1, min(lookback, len(recent_data)-1)):
            idx = -i
            
            # حساب أجزاء الشمعة
            body_size = abs(recent_data['CLOSE'].iloc[idx] - recent_data['OPEN'].iloc[idx])
            lower_shadow = min(recent_data['CLOSE'].iloc[idx], recent_data['OPEN'].iloc[idx]) - recent_data['LOW'].iloc[idx]
            upper_shadow = recent_data['HIGH'].iloc[idx] - max(recent_data['CLOSE'].iloc[idx], recent_data['OPEN'].iloc[idx])
            
            # المطرقة: جسم صغير، ظل سفلي طويل (على الأقل ضعف الجسم)، ظل علوي قصير جدا
            is_hammer = (body_size < avg_candle_size * 0.8 and 
                        lower_shadow > 2 * body_size and 
                        upper_shadow < body_size * 0.2 and
                        recent_data['CLOSE'].iloc[idx] > recent_data['OPEN'].iloc[idx])
                    
            # نجمة المساء: شمعة هبوطية تتبع شمعة صعودية قوية
            is_evening_star = (idx > -lookback+1 and
                            recent_data['CLOSE'].iloc[idx-1] > recent_data['OPEN'].iloc[idx-1] and  # الشمعة السابقة صعودية
                            (recent_data['CLOSE'].iloc[idx-1] - recent_data['OPEN'].iloc[idx-1]) > avg_candle_size * 1.2 and  # شمعة قوية
                            recent_data['CLOSE'].iloc[idx] < recent_data['OPEN'].iloc[idx] and  # الشمعة الحالية هبوطية
                            recent_data['CLOSE'].iloc[idx] < recent_data['CLOSE'].iloc[idx-1] * 0.99)  # فجوة هبوطية أو إغلاق أقل
                            
            # نجمة الصباح: شمعة صعودية تتبع شمعة هبوطية قوية
            is_morning_star = (idx > -lookback+1 and
                            recent_data['CLOSE'].iloc[idx-1] < recent_data['OPEN'].iloc[idx-1] and  # الشمعة السابقة هبوطية
                            (recent_data['OPEN'].iloc[idx-1] - recent_data['CLOSE'].iloc[idx-1]) > avg_candle_size * 1.2 and  # شمعة قوية
                            recent_data['CLOSE'].iloc[idx] > recent_data['OPEN'].iloc[idx] and  # الشمعة الحالية صعودية
                            recent_data['CLOSE'].iloc[idx] > recent_data['CLOSE'].iloc[idx-1] * 1.01)  # فجوة صعودية أو إغلاق أعلى
            
            # دوجي: جسم صغير جدًا (فتح وإغلاق متقاربين)
            is_doji = body_size < avg_candle_size * 0.1
            
            # نموذج الابتلاع الصعودي
            is_bullish_engulfing = (idx > -lookback+1 and
                                   recent_data['CLOSE'].iloc[idx-1] < recent_data['OPEN'].iloc[idx-1] and  # الشمعة السابقة هبوطية
                                   recent_data['CLOSE'].iloc[idx] > recent_data['OPEN'].iloc[idx] and  # الشمعة الحالية صعودية
                                   recent_data['CLOSE'].iloc[idx] > recent_data['OPEN'].iloc[idx-1] and  # إغلاق الحالية > فتح السابقة
                                   recent_data['OPEN'].iloc[idx] < recent_data['CLOSE'].iloc[idx-1])  # فتح الحالية < إغلاق السابقة
            
            # نموذج الابتلاع الهبوطي
            is_bearish_engulfing = (idx > -lookback+1 and
                                   recent_data['CLOSE'].iloc[idx-1] > recent_data['OPEN'].iloc[idx-1] and  # الشمعة السابقة صعودية
                                   recent_data['CLOSE'].iloc[idx] < recent_data['OPEN'].iloc[idx] and  # الشمعة الحالية هبوطية
                                   recent_data['CLOSE'].iloc[idx] < recent_data['OPEN'].iloc[idx-1] and  # إغلاق الحالية < فتح السابقة
                                   recent_data['OPEN'].iloc[idx] > recent_data['CLOSE'].iloc[idx-1])  # فتح الحالية > إغلاق السابقة
            
            # تخزين الأنماط المكتشفة
            if is_hammer:
                patterns[f'hammer_{i}'] = {
                    'type': 'hammer',
                    'description': 'مطرقة صاعدة',
                    'bias': 'صعودي',
                    'candle_index': -i,
                    'strength': 0.7
                }
            
            if is_morning_star:
                patterns[f'morning_star_{i}'] = {
                    'type': 'morning_star',
                    'description': 'نجمة الصباح',
                    'bias': 'صعودي',
                    'candle_index': -i,
                    'strength': 0.85
                }
                
            if is_evening_star:
                patterns[f'evening_star_{i}'] = {
                    'type': 'evening_star',
                    'description': 'نجمة المساء',
                    'bias': 'هبوطي',
                    'candle_index': -i,
                    'strength': 0.8
                }
                
            if is_doji:
                patterns[f'doji_{i}'] = {
                    'type': 'doji',
                    'description': 'دوجي',
                    'bias': 'محايد',
                    'candle_index': -i,
                    'strength': 0.6
                }
                
            if is_bullish_engulfing:
                patterns[f'bullish_engulfing_{i}'] = {
                    'type': 'bullish_engulfing',
                    'description': 'ابتلاع صعودي',
                    'bias': 'صعودي',
                    'candle_index': -i,
                    'strength': 0.75
                }
                
            if is_bearish_engulfing:
                patterns[f'bearish_engulfing_{i}'] = {
                    'type': 'bearish_engulfing',
                    'description': 'ابتلاع هبوطي',
                    'bias': 'هبوطي',
                    'candle_index': -i,
                    'strength': 0.75
                }
        
        return patterns
        
    except Exception as e:
        logger.error(f"Error detecting candlestick patterns: {e}")
        return {}
