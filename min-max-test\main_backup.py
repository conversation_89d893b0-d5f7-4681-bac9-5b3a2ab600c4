import logging
from auth import authenticate_google_sheet,dp,bot
from aiogram import Bo<PERSON>, Di<PERSON>atch<PERSON>, executor
import server
import threading
import sys
import argparse
from user_limit import check_user_limit,display_refer_link
from update_google_sheet import update_google_sheet,update_sheet
from reset_counter import reset_counters_command
from upgrade import upgrade_user,upgrade_command
from plot import chart_handler

# استيراد نظام أكواد الخصم الجديد
try:
    from promo_codes import PromoCodeManager
    from user_limit import UserManager
    import datetime
    PROMO_CODES_AVAILABLE = True
except ImportError as e:
    PROMO_CODES_AVAILABLE = False
    logging.warning(f"Promo codes system not available: {e}")
except Exception as e:
    PROMO_CODES_AVAILABLE = False
    logging.error(f"Error importing promo codes system: {e}")

from process_data import (
    process_modarba_all, 
    process_modarba,
    process_stock_code,
    process_analyze_command,
    process_hv,
    process_callback_messages,
    process_help,
    process_subscribe,
    cmd_menu,
    get_subscription_date,
    fibo,
    process_deals,  # Ensure this import is present
    handle_deals_button,  # Ensure this import is present
    handle_chart_button,  # Add this import
    BUTTON_DEALS,
    BUTTON_CHART,
)

logging.basicConfig(level=logging.INFO)

def parse_arguments():
    parser = argparse.ArgumentParser(description='Stock Analyzer Bot')
    parser.add_argument('--api-port', type=int, default=8000,
                        help='Port for the API server (default: 8000)')
    parser.add_argument('--api-host', type=str, default='0.0.0.0',
                        help='Host for the API server (default: 0.0.0.0)')
    parser.add_argument('--disable-api', action='store_true',
                        help='Disable the API server')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    if not args.disable_api:
        try:
            # Start API server with explicit parameters
            print(f"Starting API server on {args.api_host}:{args.api_port}")
            server_thread = threading.Thread(
                target=server.start_server,
                args=(args.api_host, args.api_port),
                daemon=True  # Make thread daemon so it exits when main program exits
            )
            server_thread.start()
            print(f"API thread started")
            
            # Also start webhook server
            print("Starting webhook server on port 9000")
            server.start_server_async()
            print("Webhook thread started")
        except Exception as e:
            print(f"Error starting API server: {e}", file=sys.stderr)
            logging.error(f"Error starting API server: {e}", exc_info=True)
    else:
        print("API server disabled")

    # Register all command handlers
    dp.message_handler(commands=['comp'])(update_sheet)
    dp.message_handler(commands=['subscribe'])(process_subscribe)
    dp.message_handler(commands=['start'])(lambda message: check_user_limit(message, bot))
    dp.message_handler(commands=['help'])(process_help)
    dp.message_handler(commands=['modarba_all'])(process_modarba_all)
    dp.message_handler(commands=['modarba'])(process_modarba)
    dp.message_handler(commands=['hv'])(process_hv)
    dp.message_handler(commands=['mysubscribtion'])(get_subscription_date)
    dp.message_handler(commands=['stock'])(process_stock_code)
    dp.message_handler(commands=['analyze'])(process_analyze_command)
    dp.message_handler(commands=['fibo'])(fibo)
    dp.message_handler(commands=['reset_counters'])(reset_counters_command)
    dp.message_handler(commands=['upgrade'])(upgrade_command)
    dp.message_handler(commands=['chart'])(chart_handler)
    dp.message_handler(commands=['deals'])(process_deals)  # Ensure this handler is registered
    dp.message_handler(commands=['myrefer'])(display_refer_link)
    dp.register_message_handler(cmd_menu, commands=['menu'])
    dp.register_message_handler(lambda message: message.text == "📊 الصفقات", handle_deals_button)  # Ensure this handler is registered
    dp.register_message_handler(process_callback_messages)
    # Register commands    # Register the specific handlers for these buttons
    dp.register_message_handler(handle_deals_button, lambda message: message.text == BUTTON_DEALS)
    dp.register_message_handler(handle_chart_button, lambda message: message.text == BUTTON_CHART)
      # ===== تسجيل أوامر نظام أكواد الخصم =====
    print(f"PROMO_CODES_AVAILABLE: {PROMO_CODES_AVAILABLE}")
    if PROMO_CODES_AVAILABLE:
        print("تسجيل أوامر الأكواد...")
        dp.message_handler(commands=['create_trial_code'])(create_trial_code_command)
        dp.message_handler(commands=['create_discount_code'])(create_discount_code_command) 
        dp.message_handler(commands=['redeem'])(redeem_promo_code)
        dp.message_handler(commands=['list_codes'])(list_promo_codes)
        print("تم تسجيل أوامر الأكواد بنجاح!")   
    else:
        print("⚠️ أوامر الأكواد غير متاحة - PROMO_CODES_AVAILABLE = False")
    
    # Start the bot
    print("Starting bot")
    executor.start_polling(dp, skip_updates=True)
