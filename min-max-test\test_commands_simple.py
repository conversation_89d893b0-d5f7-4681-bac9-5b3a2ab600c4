#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محاكاة أوامر البوت
"""

import asyncio
import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class MockMessage:
    def __init__(self, text, user_id=123456789):
        self.text = text
        self.from_user = MockUser(user_id)
        self.chat = MockChat()
    
    async def reply(self, text, **kwargs):
        print(f"🤖 البوت يرد: {text[:100]}...")
        return MockMessage("ok")

class MockUser:
    def __init__(self, user_id):
        self.id = user_id
        self.first_name = "Test User"

class MockChat:
    def __init__(self):
        self.id = 123456789

async def test_commands():
    """اختبار الأوامر"""
    try:
        # استيراد الدوال
        from main import (
            create_trial_code_command,
            create_discount_code_command,
            redeem_promo_code,
            list_promo_codes,
            PROMO_CODES_AVAILABLE
        )
        
        print(f"✅ PROMO_CODES_AVAILABLE: {PROMO_CODES_AVAILABLE}")
        
        if not PROMO_CODES_AVAILABLE:
            print("⚠️ نظام الأكواد غير متاح - لا يمكن اختبار الأوامر")
            return
        
        print("\n🧪 اختبار الأوامر...")
        
        # اختبار إنشاء كود تجربة
        print("\n1️⃣ اختبار create_trial_code:")
        message = MockMessage("/create_trial_code 5 20 اختبار")
        try:
            await create_trial_code_command(message)
            print("✅ create_trial_code نجح")
        except Exception as e:
            print(f"❌ create_trial_code فشل: {e}")
        
        # اختبار إنشاء كود خصم
        print("\n2️⃣ اختبار create_discount_code:")
        message = MockMessage("/create_discount_code 30 15 اختبار")
        try:
            await create_discount_code_command(message)
            print("✅ create_discount_code نجح")
        except Exception as e:
            print(f"❌ create_discount_code فشل: {e}")
        
        # اختبار عرض الأكواد
        print("\n3️⃣ اختبار list_codes:")
        message = MockMessage("/list_codes")
        try:
            await list_promo_codes(message)
            print("✅ list_codes نجح")
        except Exception as e:
            print(f"❌ list_codes فشل: {e}")
        
        print("\n🎯 انتهى الاختبار!")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 بدء اختبار أوامر البوت...")
    asyncio.run(test_commands())
