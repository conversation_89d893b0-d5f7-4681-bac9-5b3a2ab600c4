"""
نظام أكواد الخصم والتجربة المجانية
Promo Codes and Trial Codes System
"""

import datetime
import secrets
import string
from typing import Optional, Dict, Tuple
from auth import open_google_sheet
import logging

# سيتم استيراد aiogram في الملفات التي تحتاجها
# هذا الملف يحتوي على الكلاسات والدوال فقط

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# الاتصال بـ Google Sheets
sheet_name = "stock"
sheet, sheet2, sheet3 = open_google_sheet(sheet_name)

# الحصول على spreadsheet للوصول لإنشاء worksheets جديدة
try:
    import gspread
    import json
    from oauth2client.service_account import ServiceAccountCredentials
    
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    with open('json_file.json', 'r') as f:
        creds = json.load(f)
    credentials = ServiceAccountCredentials.from_json_keyfile_dict(creds, scope)
    client = gspread.authorize(credentials)
    spreadsheet = client.open(sheet_name)  # الحصول على الـ spreadsheet
    
    # محاولة الوصول للـ worksheet أو إنشاؤه
    try:
        promo_sheet = spreadsheet.worksheet("promo_codes")
    except gspread.exceptions.WorksheetNotFound:
        # إنشاء worksheet جديد للأكواد إذا لم يوجد
        promo_sheet = spreadsheet.add_worksheet(title="promo_codes", rows="1000", cols="10")
        # إضافة العناوين
        headers = ["كود البرومو", "نوع الكود", "قيمة الخصم/الأيام", "مستخدم بواسطة", "تاريخ الاستخدام", "تاريخ الإنشاء", "حالة الكود", "تاريخ الانتهاء", "عدد مرات الاستخدام", "ملاحظات"]
        promo_sheet.insert_row(headers, 1)
        
except Exception as e:
    logger.error(f"خطأ في الاتصال بـ Google Sheets: {e}")
    # استخدام sheet3 كـ fallback (سيحدث خطأ إذا لم يوجد worksheet للأكواد)
    try:
        promo_sheet = sheet3.worksheet("promo_codes")
    except:
        logger.error("لا يمكن الوصول لـ worksheet الأكواد - يجب إنشاؤه يدوياً")
        promo_sheet = None

class PromoCodeManager:
    
    @staticmethod
    def generate_promo_code(length: int = 8) -> str:
        """توليد كود برومو عشوائي"""
        characters = string.ascii_uppercase + string.digits
        # تجنب الأحرف المشابهة O, 0, I, 1, L
        characters = characters.replace('O', '').replace('0', '').replace('I', '').replace('1', '').replace('L', '')
        return ''.join(secrets.choice(characters) for _ in range(length))
    @staticmethod
    def create_trial_code(days: int = 7, expiry_days: int = 30, note: str = "") -> str:
        """إنشاء كود تجربة مجانية"""
        if promo_sheet is None:
            logger.error("لا يمكن إنشاء كود - worksheet غير متاح")
            return None
            
        code = f"TRIAL{PromoCodeManager.generate_promo_code(6)}"
        expiry_date = (datetime.date.today() + datetime.timedelta(days=expiry_days)).strftime("%Y-%m-%d")
        code_data = [
            code,                           # كود البرومو
            "trail",                        # نوع الكود (استخدام trail بدلاً من trial)
            days,                           # عدد الأيام
            "",                             # مستخدم بواسطة (فارغ)
            "",                             # تاريخ الاستخدام (فارغ)
            datetime.date.today().strftime("%Y-%m-%d"),  # تاريخ الإنشاء
            "نشط",                          # حالة الكود
            expiry_date,                    # تاريخ الانتهاء
            0,                              # عدد مرات الاستخدام
            note                            # ملاحظات
        ]
        
        try:
            promo_sheet.append_row(code_data)
            logger.info(f"تم إنشاء كود تجربة مجانية: {code}")
            return code
        except Exception as e:
            logger.error(f"خطأ في إنشاء كود التجربة: {e}")
            return None
    @staticmethod
    def create_discount_code(discount_percent: int, expiry_days: int = 30, note: str = "") -> str:
        """إنشاء كود خصم"""
        if promo_sheet is None:
            logger.error("لا يمكن إنشاء كود - worksheet غير متاح")
            return None
            
        code = f"SAVE{discount_percent}{PromoCodeManager.generate_promo_code(5)}"
        expiry_date = (datetime.date.today() + datetime.timedelta(days=expiry_days)).strftime("%Y-%m-%d")
        
        code_data = [
            code,                           # كود البرومو
            "discount",                     # نوع الكود
            discount_percent,               # نسبة الخصم
            "",                             # مستخدم بواسطة (فارغ)
            "",                             # تاريخ الاستخدام (فارغ)
            datetime.date.today().strftime("%Y-%m-%d"),  # تاريخ الإنشاء
            "نشط",                          # حالة الكود
            expiry_date,                    # تاريخ الانتهاء
            0,                              # عدد مرات الاستخدام
            note                            # ملاحظات
        ]
        
        try:
            promo_sheet.append_row(code_data)
            logger.info(f"تم إنشاء كود خصم: {code}")
            return code
        except Exception as e:
            logger.error(f"خطأ في إنشاء كود الخصم: {e}")
            return None
    
    @staticmethod
    def validate_promo_code(code: str, user_id: str) -> Tuple[bool, str, Dict]:
        """التحقق من صحة كود البرومو"""
        if promo_sheet is None:
            return False, "نظام الأكواد غير متاح حالياً", {}
            
        try:
            # البحث عن الكود في الشيت
            cell = promo_sheet.find(code)
            if not cell:
                return False, "كود غير صحيح", {}
            
            # الحصول على بيانات الصف
            row_values = promo_sheet.row_values(cell.row)
            
            code_data = {
                'code': row_values[0],
                'type': row_values[1],
                'value': int(row_values[2]),
                'used_by': row_values[3],
                'used_date': row_values[4],
                'created_date': row_values[5],
                'status': row_values[6],
                'expiry_date': row_values[7],
                'usage_count': int(row_values[8]) if row_values[8] else 0,
                'notes': row_values[9] if len(row_values) > 9 else "",
                'row': cell.row
            }
            
            # التحقق من حالة الكود
            if code_data['status'] != "نشط":
                return False, "هذا الكود غير نشط", code_data
            
            # التحقق من تاريخ الانتهاء
            expiry_date = datetime.datetime.strptime(code_data['expiry_date'], "%Y-%m-%d").date()
            if datetime.date.today() > expiry_date:
                return False, "انتهت صلاحية هذا الكود", code_data
            
            # التحقق من الاستخدام السابق
            if code_data['used_by'] and str(user_id) in code_data['used_by']:
                return False, "لقد استخدمت هذا الكود من قبل", code_data
            
            return True, "كود صحيح", code_data
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الكود {code}: {e}")
            return False, "خطأ في النظام", {}
    
    @staticmethod
    def use_promo_code(code_data: Dict, user_id: str) -> bool:
        """استخدام كود البرومو وتحديث البيانات"""
        if promo_sheet is None:
            logger.error("لا يمكن استخدام الكود - worksheet غير متاح")
            return False
            
        try:
            row = code_data['row']
            
            # تحديث بيانات الاستخدام
            current_users = code_data['used_by']
            if current_users:
                updated_users = f"{current_users},{user_id}"
            else:
                updated_users = str(user_id)
            
            # تحديث البيانات في الشيت
            promo_sheet.update_cell(row, 4, updated_users)  # مستخدم بواسطة
            promo_sheet.update_cell(row, 5, datetime.date.today().strftime("%Y-%m-%d"))  # تاريخ الاستخدام
            promo_sheet.update_cell(row, 9, code_data['usage_count'] + 1)  # زيادة عدد مرات الاستخدام
            
            logger.info(f"تم استخدام الكود {code_data['code']} بواسطة المستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استخدام الكود: {e}")
            return False
    @staticmethod
    def list_active_codes() -> list:
        """عرض جميع الأكواد النشطة"""
        if promo_sheet is None:
            logger.error("لا يمكن عرض الأكواد - worksheet غير متاح")
            return None
            
        try:
            # الحصول على جميع البيانات
            all_records = promo_sheet.get_all_records()
            
            active_codes = []
            for record in all_records:
                # فلترة الأكواد النشطة فقط
                if record.get('حالة الكود') == 'نشط':
                    active_codes.append({
                        'code': record.get('كود البرومو'),
                        'type': record.get('نوع الكود'),
                        'value': record.get('قيمة الخصم/الأيام'),
                        'created_date': record.get('تاريخ الإنشاء'),
                        'expiry_date': record.get('تاريخ الانتهاء'),
                        'usage_count': record.get('عدد مرات الاستخدام', 0),
                        'note': record.get('ملاحظات')
                    })
            
            logger.info(f"تم العثور على {len(active_codes)} كود نشط")
            return active_codes
            
        except Exception as e:
            logger.error(f"خطأ في عرض الأكواد: {e}")
            return None

# تم نقل دوال أوامر البوت إلى ملف منفصل لتجنب مشاكل الاستيراد
# يمكن إضافتها في main.py أو الملف المناسب

# ======== BOT COMMANDS TO BE ADDED IN MAIN.PY ========

PROMO_COMMANDS_CODE = '''
# إضافة الأوامر التالية في main.py:

@dp.message_handler(commands=['create_trial_code'])
async def create_trial_code_command(message: types.Message):
    """إنشاء كود تجربة مجانية - للإدارة فقط"""
    from promo_codes import PromoCodeManager
    user_id = message.from_user.id
    
    # التحقق من صلاحية الإدارة
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        await message.reply("هذا الأمر متاح للإدارة فقط")
        return
    
    try:
        args = message.text.split()
        days = int(args[1]) if len(args) > 1 else 7
        expiry_days = int(args[2]) if len(args) > 2 else 30
        note = " ".join(args[3:]) if len(args) > 3 else "كود تجربة مجانية"
        
        code = PromoCodeManager.create_trial_code(days, expiry_days, note)
        
        if code:
            await message.reply(f"✅ تم إنشاء كود التجربة: `{code}`", parse_mode="Markdown")
        else:
            await message.reply("حدث خطأ في إنشاء الكود")
    except:
        await message.reply("/create_trial_code [أيام] [انتهاء] [ملاحظة]")

@dp.message_handler(commands=['redeem'])
async def redeem_promo_code(message: types.Message):
    """استخدام كود البرومو - محدث لتحديث الجدول الصحيح"""
    from promo_codes import PromoCodeManager
    from user_subscription_manager import UserSubscriptionManager, activate_user_trial
    import datetime

    args = message.text.split()
    if len(args) != 2:
        await message.reply("🎫 **الاستخدام الصحيح:**\n`/redeem YOUR_CODE`\n\n**مثال:**\n`/redeem TRIAL7FREE`", parse_mode="Markdown")
        return

    code = args[1].upper()
    user_id = str(message.from_user.id)
    user_name = f"{message.from_user.first_name or ''} {message.from_user.last_name or ''}".strip()

    # التحقق من صحة الكود
    is_valid, error_msg, code_data = PromoCodeManager.validate_promo_code(code, user_id)

    if not is_valid:
        await message.reply(f"❌ **خطأ في الكود:**\n{error_msg}")
        return

    # التحقق من وجود المستخدم في النظام
    user_info = UserSubscriptionManager.get_user_subscription_info(user_id)

    if not user_info:
        # إضافة المستخدم الجديد
        success = UserSubscriptionManager.add_new_user_to_sheet(
            user_id,
            message.from_user.first_name or "",
            message.from_user.last_name or ""
        )
        if not success:
            await message.reply("❌ حدث خطأ في النظام. يرجى المحاولة مرة أخرى.")
            return

    # تطبيق الكود حسب نوعه
    if code_data['type'] == 'trail':
        trail_days = code_data['value']

        # تفعيل التجربة المجانية
        success = UserSubscriptionManager.activate_trial_subscription(user_id, trail_days)

        if success:
            # تحديث حالة الكود
            PromoCodeManager.use_promo_code(code_data, user_id)

            # حساب تاريخ الانتهاء
            end_date = (datetime.date.today() + datetime.timedelta(days=trail_days)).strftime("%Y-%m-%d")

            # رسالة النجاح
            success_message = f"""
🎉 **تم تفعيل التجربة المجانية بنجاح!**

✨ **مفعل لك الآن لمدة {trail_days} أيام:**
├ 🔄 تحليلات غير محدودة
├ 📱 تنبيهات فورية للفرص الذهبية
├ 🎯 توصيات خاصة عالية الدقة
├ 📈 تحليل تقني متقدم بالذكاء الاصطناعي
├ 💎 مستويات دعم ومقاومة مخفية
└ 👨‍💼 دعم شخصي من المحللين

📅 **ينتهي في:** {end_date}

🚀 **ابدأ الاستفادة الآن:**
├ 📊 /analyze + كود السهم
├ 🎯 /stock + كود السهم
└ 📈 /chart + كود السهم

💡 **نصيحة:** استفد من فترة التجربة لتقييم الأداء!
"""
            await message.reply(success_message, parse_mode="Markdown")

            # إشعار في السجل
            logger.info(f"تم تفعيل التجربة المجانية للمستخدم {user_id} ({user_name}) لمدة {trail_days} أيام باستخدام الكود {code}")
        else:
            await message.reply("❌ فشل في تفعيل التجربة المجانية. يرجى المحاولة مرة أخرى.")

    elif code_data['type'] == 'discount':
        # تحديث حالة الكود
        PromoCodeManager.use_promo_code(code_data, user_id)

        discount_message = f"""
💰 **تم تفعيل كود الخصم بنجاح!**

🎫 **خصم {code_data['value']}% مفعل لحسابك!**

✅ **تفاصيل العرض:**
├ 💳 الخصم محفوظ في حسابك
├ ⏰ سيتم تطبيقه تلقائياً عند الدفع
├ 🔄 صالح لجميع باقات الاشتراك
└ 📅 صالح لمدة أسبوعين

🔥 **استفد من العرض الآن:**
💳 *للاشتراك وتطبيق الخصم:* /subscribe

⚡ *لا تفوت هذا العرض المحدود!*
"""
        await message.reply(discount_message, parse_mode="Markdown")

        # إشعار في السجل
        logger.info(f"تم تفعيل كود خصم {code_data['value']}% للمستخدم {user_id} ({user_name}) باستخدام الكود {code}")

    else:
        await message.reply("❌ نوع كود غير مدعوم")
'''
