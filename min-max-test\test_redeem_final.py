#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل لأمر /redeem
Final comprehensive test for /redeem command
"""

import asyncio
import datetime
from unittest.mock import AsyncMock, MagicMock

async def test_redeem_complete_workflow():
    """اختبار سير العمل الكامل لأمر /redeem"""
    try:
        print("🧪 اختبار سير العمل الكامل لأمر /redeem...")
        
        from promo_codes import PromoCodeManager
        from user_subscription_manager import UserSubscriptionManager
        from promo_commands import redeem_promo_code
        
        # 1. إنشاء كود تجربة مجانية
        print("\n🎫 إنشاء كود تجربة مجانية:")
        test_code = PromoCodeManager.create_trial_code(
            days=7,
            expiry_days=30,
            note="اختبار نهائي شامل"
        )
        
        if test_code:
            print(f"✅ تم إنشاء الكود: {test_code}")
        else:
            print("❌ فشل في إنشاء الكود")
            return False
        
        # 2. اختيار مستخدم للاختبار
        test_user_id = "1078882205"  # Michael
        print(f"\n👤 اختبار مع المستخدم: {test_user_id}")
        
        # 3. فحص حالة المستخدم قبل التفعيل
        print("\n📊 حالة المستخدم قبل التفعيل:")
        user_before = UserSubscriptionManager.get_user_subscription_info(test_user_id)
        if user_before:
            print(f"   - نوع الاشتراك: {user_before['subscription_type']}")
            print(f"   - تاريخ الانتهاء: {user_before['end_date']}")
            print(f"   - نشط: {user_before['is_active']}")
        else:
            print("   - المستخدم غير موجود")
        
        # 4. فحص حالة الكود قبل الاستخدام
        print(f"\n🎫 حالة الكود {test_code} قبل الاستخدام:")
        is_valid_before, _, code_data = PromoCodeManager.validate_promo_code(test_code, test_user_id)
        if is_valid_before:
            print(f"   - صحيح: نعم")
            print(f"   - النوع: {code_data['type']}")
            print(f"   - القيمة: {code_data['value']} أيام")
            print(f"   - مستخدم: {code_data['used']}")
        else:
            print(f"   - صحيح: لا")
        
        # 5. محاكاة استخدام الأمر
        print(f"\n🤖 محاكاة استخدام الأمر /redeem {test_code}:")
        
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = int(test_user_id)
        mock_message.from_user.first_name = "Michael"
        mock_message.from_user.last_name = ""
        mock_message.text = f"/redeem {test_code}"
        
        # تشغيل الأمر
        await redeem_promo_code(mock_message)
        
        # 6. التحقق من النتائج
        print("\n📋 التحقق من النتائج:")
        
        # فحص الرسالة المرسلة
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            print("✅ تم إرسال رسالة الرد")
            
            # التحقق من محتوى الرسالة
            success_indicators = [
                "تم تفعيل التجربة المجانية بنجاح",
                "تحليلات غير محدودة",
                "ينتهي في"
            ]
            
            success_count = 0
            for indicator in success_indicators:
                if indicator in sent_message:
                    success_count += 1
                    print(f"   ✅ {indicator}")
                else:
                    print(f"   ❌ مفقود: {indicator}")
            
            if success_count == len(success_indicators):
                print("✅ رسالة النجاح صحيحة")
            else:
                print("❌ رسالة النجاح غير مكتملة")
        else:
            print("❌ لم يتم إرسال رسالة")
            return False
        
        # 7. فحص حالة المستخدم بعد التفعيل
        print("\n📊 حالة المستخدم بعد التفعيل:")
        user_after = UserSubscriptionManager.get_user_subscription_info(test_user_id)
        if user_after:
            print(f"   - نوع الاشتراك: {user_after['subscription_type']}")
            print(f"   - تاريخ الانتهاء: {user_after['end_date']}")
            print(f"   - نشط: {user_after['is_active']}")
            print(f"   - أيام متبقية: {user_after['days_remaining']}")
            
            # التحقق من التحديث
            if (user_after['subscription_type'] == 'trail' and 
                user_after['is_active'] and 
                user_after['days_remaining'] > 0):
                print("✅ تم تحديث بيانات المستخدم بنجاح")
                user_updated = True
            else:
                print("❌ لم يتم تحديث بيانات المستخدم")
                user_updated = False
        else:
            print("❌ فشل في قراءة بيانات المستخدم")
            user_updated = False
        
        # 8. فحص حالة الكود بعد الاستخدام
        print(f"\n🎫 حالة الكود {test_code} بعد الاستخدام:")
        is_valid_after, error_msg, _ = PromoCodeManager.validate_promo_code(test_code, test_user_id)
        if not is_valid_after:
            print(f"   - صحيح: لا (متوقع)")
            print(f"   - السبب: {error_msg}")
            code_updated = True
        else:
            print(f"   - صحيح: نعم (غير متوقع)")
            code_updated = False
        
        # 9. النتيجة النهائية
        print("\n" + "="*50)
        print("📊 النتيجة النهائية:")
        
        results = {
            "إنشاء الكود": test_code is not None,
            "إرسال الرسالة": mock_message.reply.called,
            "تحديث المستخدم": user_updated,
            "تحديث الكود": code_updated
        }
        
        passed = 0
        for test_name, result in results.items():
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
        
        if passed == len(results):
            print("\n🎉 جميع الاختبارات نجحت!")
            print("✅ أمر /redeem يعمل بكفاءة كاملة")
            return True
        else:
            print(f"\n⚠️ {len(results) - passed} اختبار فشل")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار الشامل: {e}")
        return False

async def test_redeem_error_cases():
    """اختبار حالات الخطأ"""
    try:
        print("\n🛡️ اختبار حالات الخطأ:")
        
        from promo_commands import redeem_promo_code
        
        test_cases = [
            {
                "name": "كود غير موجود",
                "text": "/redeem INVALIDCODE123",
                "expected": "خطأ"
            },
            {
                "name": "تنسيق خاطئ",
                "text": "/redeem",
                "expected": "كيفية استخدام الكود"
            },
            {
                "name": "كود فارغ",
                "text": "/redeem ",
                "expected": "كيفية استخدام الكود"
            }
        ]
        
        passed_tests = 0
        
        for test_case in test_cases:
            print(f"\n   🧪 اختبار: {test_case['name']}")
            
            mock_message = MagicMock()
            mock_message.reply = AsyncMock()
            mock_message.from_user.id = 1078882205
            mock_message.from_user.first_name = "Michael"
            mock_message.from_user.last_name = ""
            mock_message.text = test_case['text']
            
            await redeem_promo_code(mock_message)
            
            if mock_message.reply.called:
                call_args = mock_message.reply.call_args
                sent_message = call_args[0][0]
                
                if test_case['expected'] in sent_message:
                    print(f"   ✅ تم التعامل مع الخطأ بشكل صحيح")
                    passed_tests += 1
                else:
                    print(f"   ❌ رسالة خطأ غير متوقعة: {sent_message[:100]}...")
            else:
                print(f"   ❌ لم يتم إرسال رسالة خطأ")
        
        print(f"\n📊 نتيجة اختبار الأخطاء: {passed_tests}/{len(test_cases)} نجح")
        return passed_tests == len(test_cases)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حالات الخطأ: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار نهائي شامل لأمر /redeem")
    print("=" * 60)
    
    tests = [
        ("اختبار سير العمل الكامل", test_redeem_complete_workflow),
        ("اختبار حالات الخطأ", test_redeem_error_cases),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة الإجمالية: {passed}/{len(results)} اختبار نجح")
    
    if passed == len(results):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ أمر /redeem يعمل بكفاءة كاملة")
        print("\n🔧 الوظائف المحققة:")
        print("   ✅ يحدث كود البرومو في sheet promo_codes")
        print("   ✅ يحدث حالة المستخدم من free إلى trail في sheet users")
        print("   ✅ يحدث تاريخ الانتهاء بشكل صحيح")
        print("   ✅ يرسل رسائل تأكيد مفصلة وجميلة")
        print("   ✅ يتعامل مع الأخطاء بشكل صحيح")
        
        print("\n🚀 للاستخدام:")
        print("   /redeem TRIAL7FREE")
        print("   /redeem YOUR_PROMO_CODE")
        
        print("\n💡 النتيجة المتوقعة:")
        print("   - تحديث نوع الاشتراك من free إلى trail")
        print("   - تحديث تاريخ انتهاء الاشتراك")
        print("   - تحديث حالة كود البرومو إلى مستخدم")
        print("   - إرسال رسالة تأكيد مفصلة للمستخدم")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")

if __name__ == "__main__":
    asyncio.run(main())
