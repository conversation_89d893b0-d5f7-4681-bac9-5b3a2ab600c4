#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحميل نظام الأكواد والتشخيص
"""

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_promo_codes_dependencies():
    """اختبار dependencies نظام الأكواد"""
    print("🔍 اختبار dependencies نظام الأكواد...")
    
    try:
        print("📦 اختبار gspread...")
        import gspread
        print("   ✅ gspread متاح")
    except ImportError as e:
        print(f"   ❌ gspread غير متاح: {e}")
        return False
    
    try:
        print("📦 اختبار oauth2client...")
        from oauth2client.service_account import ServiceAccountCredentials
        print("   ✅ oauth2client متاح")
    except ImportError as e:
        print(f"   ❌ oauth2client غير متاح: {e}")
        return False
    
    try:
        print("📁 اختبار وجود json_file.json...")
        if os.path.exists('json_file.json'):
            print("   ✅ json_file.json موجود")
        else:
            print("   ❌ json_file.json غير موجود")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص json_file.json: {e}")
        return False
    
    try:
        print("📁 اختبار قراءة json_file.json...")
        import json
        with open('json_file.json', 'r') as f:
            creds = json.load(f)
        print("   ✅ json_file.json قابل للقراءة")
        
        # فحص المفاتيح المطلوبة
        required_keys = ['client_email', 'private_key', 'project_id']
        missing_keys = [key for key in required_keys if key not in creds]
        if missing_keys:
            print(f"   ⚠️ مفاتيح ناقصة في json_file.json: {missing_keys}")
        else:
            print("   ✅ جميع المفاتيح المطلوبة موجودة")
            
    except Exception as e:
        print(f"   ❌ خطأ في قراءة json_file.json: {e}")
        return False
    
    return True

def test_auth_import():
    """اختبار استيراد auth module"""
    print("\n🔍 اختبار استيراد auth module...")
    
    try:
        from auth import open_google_sheet
        print("   ✅ auth.open_google_sheet متاح")
        return True
    except ImportError as e:
        print(f"   ❌ فشل استيراد auth: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في auth: {e}")
        return False

def test_promo_codes_import():
    """اختبار استيراد promo_codes module"""
    print("\n🔍 اختبار استيراد promo_codes...")
    
    try:
        print("   📁 فحص وجود promo_codes.py...")
        if os.path.exists('promo_codes.py'):
            print("   ✅ promo_codes.py موجود")
        else:
            print("   ❌ promo_codes.py غير موجود")
            return False
        
        print("   📦 محاولة استيراد promo_codes...")
        import promo_codes
        print("   ✅ promo_codes تم استيراده")
        
        print("   🏗️ فحص وجود PromoCodeManager...")
        if hasattr(promo_codes, 'PromoCodeManager'):
            print("   ✅ PromoCodeManager موجود")
        else:
            print("   ❌ PromoCodeManager غير موجود")
            return False
            
        return True
        
    except ImportError as e:
        print(f"   ❌ فشل استيراد promo_codes (ImportError): {e}")
        return False
    except Exception as e:
        print(f"   ❌ فشل استيراد promo_codes (Exception): {e}")
        return False

def test_user_limit_import():
    """اختبار استيراد user_limit module"""
    print("\n🔍 اختبار استيراد user_limit...")
    
    try:
        from user_limit import UserManager
        print("   ✅ UserManager تم استيراده")
        return True
    except ImportError as e:
        print(f"   ❌ فشل استيراد UserManager: {e}")
        return False
    except Exception as e:
        print(f"   ❌ خطأ في UserManager: {e}")
        return False

def simulate_main_import():
    """محاكاة استيراد main.py"""
    print("\n🔍 محاكاة استيراد كما في main.py...")
    
    try:
        print("   📦 استيراد PromoCodeManager...")
        from promo_codes import PromoCodeManager
        print("   ✅ PromoCodeManager نجح")
        
        print("   📦 استيراد UserManager...")
        from user_limit import UserManager
        print("   ✅ UserManager نجح")
        
        print("   📦 استيراد datetime...")
        import datetime
        print("   ✅ datetime نجح")
        
        print("   🎯 PROMO_CODES_AVAILABLE = True")
        return True
        
    except ImportError as e:
        print(f"   ❌ ImportError: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def main():
    """الاختبار الرئيسي"""
    print("🚀 بدء تشخيص نظام الأكواد...")
    
    step1 = test_promo_codes_dependencies()
    step2 = test_auth_import()
    step3 = test_promo_codes_import()
    step4 = test_user_limit_import()
    step5 = simulate_main_import()
    
    print(f"\n📊 ملخص النتائج:")
    print(f"Dependencies: {'✅' if step1 else '❌'}")
    print(f"Auth import: {'✅' if step2 else '❌'}")
    print(f"Promo codes import: {'✅' if step3 else '❌'}")
    print(f"User limit import: {'✅' if step4 else '❌'}")
    print(f"Main simulation: {'✅' if step5 else '❌'}")
    
    if all([step1, step2, step3, step4, step5]):
        print(f"\n🎉 جميع الاختبارات نجحت! نظام الأكواد يجب أن يعمل.")
        print(f"إذا كانت الأوامر لا تزال لا تعمل، المشكلة قد تكون في:")
        print(f"   - مسار الملفات في الخادم")
        print(f"   - permissions في Google Sheets")
        print(f"   - إصدارات مختلفة من الملفات")
    else:
        print(f"\n⚠️ هناك مشاكل تمنع عمل نظام الأكواد.")
        print(f"الأوامر البديلة البسيطة ستعمل بدلاً منها.")

if __name__ == "__main__":
    main()
