from flask import request, jsonify, g
from functools import wraps
import time
import hashlib
import logging
import re

logger = logging.getLogger(__name__)

# Simple in-memory rate limiter
class RateLimiter:
    def __init__(self, limit=60, window=60):  # Default: 60 requests per minute
        self.limit = limit
        self.window = window
        self.clients = {}
        
    def is_allowed(self, client_id):
        current_time = time.time()
        client_key = hashlib.md5(str(client_id).encode()).hexdigest()
        
        if client_key not in self.clients:
            self.clients[client_key] = []
            
        # Clean old requests
        self.clients[client_key] = [t for t in self.clients[client_key] 
                                   if t > current_time - self.window]
        
        # Check if allowed
        if len(self.clients[client_key]) < self.limit:
            self.clients[client_key].append(current_time)
            return True
        return False

# Create a rate limiter instance
rate_limiter = RateLimiter()

def rate_limit(f):
    """Rate limiting decorator for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_id = request.remote_addr
        
        if not rate_limiter.is_allowed(client_id):
            logger.warning(f"Rate limit exceeded for {client_id}")
            return jsonify({"error": "Rate limit exceeded, please try again later"}), 429
            
        return f(*args, **kwargs)
    return decorated_function

def sanitize_input(f):
    """Sanitize user input to prevent injection attacks"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check for SQL injection patterns
        for key in request.args:
            if re.search(r"(?i)(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER)", request.args[key]):
                logger.warning(f"Potentially malicious input detected: {request.args[key]}")
                return jsonify({"error": "Invalid input"}), 400
                
        return f(*args, **kwargs)
    return decorated_function
