absl-py==2.1.0
acme==1.1.0
aiofiles==23.2.1
aiogram==2.24
aiohttp==3.8.6
aiosignal==1.3.1
aiosqlite==0.20.0
annotated-types==0.6.0
APScheduler==3.10.4
asgiref==3.8.1
asn1crypto==1.5.1
astunparse==1.6.3
async-timeout==4.0.3
atomicwrites==1.1.5
attrs==23.2.0
Automat==0.8.0
Babel==2.9.1
backports.zoneinfo==0.2.1
bayesian-optimization==1.4.3
beautifulsoup4==4.12.3
blinker==1.8.2
cachetools==4.2.4
certbot==0.40.0
certbot-nginx==0.40.0
certifi==2024.2.2
cffi==1.16.0
chardet==3.0.4
charset-normalizer==3.3.2
click==8.1.7
click-plugins==1.1.1
cloud-init==24.1.3
cloudpickle==3.1.1
colorama==0.4.6
colorlog==3.1.0
command-not-found==0.3
ConfigArgParse==0.13.0
configobj==5.0.6
constantly==15.1.0
contourpy==1.1.1
cryptography==2.3
cupshelpers==1.0
CurrencyConverter==0.17.17
cvxopt==1.3.2
cycler==0.12.1
daemonize==2.4.7
dash==2.17.1
dash-bootstrap-components==1.6.0
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
dbus-python==1.2.16
decorator==4.4.2
defer==1.0.6
distro==1.4.0
distro-info==0.23+ubuntu1.1
entrypoints==0.3
et-xmlfile==1.1.0
exceptiongroup==1.2.0
filelock==3.16.1
flask==3.0.3
Flask-Login==0.6.3
flatbuffers==25.1.24
fonttools==4.50.0
frozendict==2.4.6
frozenlist==1.4.1
future==0.18.2
fuzzywuzzy==0.18.0
gast==0.4.0
google-api-core==1.34.1
google-api-python-client==1.8.3
google-auth==1.35.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.0.0
google-pasta==0.2.0
googleapis-common-protos==1.62.0
grpcio==1.50.0
grpcio-tools==1.50.0
gspread==6.0.2
gunicorn==21.2.0
gym==0.26.2
gym-notices==0.0.8
h11==0.14.0
h2==4.1.0
h5py==3.11.0
hpack==4.0.0
html5lib==1.1
httplib2==0.22.0
hypercorn==0.17.3
hyperframe==6.0.1
hyperlink==19.0.0
idna==2.6
importlib-metadata==7.0.1
importlib-resources==6.3.2
incremental==22.10.0
iniconfig==2.0.0
itsdangerous==2.1.2
jdcal==1.0
Jinja2==3.1.3
joblib==1.4.2
josepy==1.2.0
json-rpc==1.13.0
jsonpatch==1.22
jsonpointer==2.0
jsonschema==3.2.0
keras==2.13.1
keyring==18.0.1
kiwisolver==1.4.5
language-selector==0.1
launchpadlib==1.10.13
lazr.restfulclient==0.14.2
lazr.uri==1.0.3
Levenshtein==0.25.0
libclang==18.1.1
llvmlite==0.41.1
lxml==5.3.0
macaroonbakery==1.3.1
magic-filter==1.0.12
Markdown==3.7
MarkupSafe==2.1.5
matplotlib==3.7.5
mock==3.0.5
more-itertools==4.2.0
mplfinance==0.12.10b0
multidict==6.0.5
multitasking==0.0.11
nest-asyncio==1.6.0
netifaces==0.10.4
networkx==3.1
nltk==3.9.1
ntplib==0.3.4
numba==0.58.1
numexpr==2.8.6
numpy==1.24.3
oauth2client==4.1.3
oauthlib==3.1.0
olefile==0.46
openpyxl==3.1.2
opt-einsum==3.4.0
packaging==23.2
pandas==2.0.3
pandas-ta==0.3.14b0
parsedatetime==2.4
patsy==1.0.1
pbr==5.4.5
peewee==3.17.8
pexpect==4.6.0
pillow==10.2.0
platformdirs==4.3.6
plotly==5.22.0
pluggy==1.4.0
plyvel==1.4.0
prettytable==3.10.0
priority==2.0.0
pritunl==1.32.3897.75
protobuf==3.20.3
py==1.8.1
py-lets-be-rational==1.0.1
py-vollib==1.0.1
pyasn1==0.4.2
pyasn1-modules==0.2.1
pycairo==1.16.2
pycoingecko==3.1.0
pycparser==2.21
pycups==1.9.73
pydantic==2.5.3
pydantic-core==2.14.6
pyecharts==1.9.0
PyGObject==3.36.0
PyHamcrest==1.9.0
PyICU==2.4.2
PyJWT==1.7.1
pymacaroons==0.13.0
PyNaCl==1.3.0
pyOpenSSL==17.5.0
pyparsing==2.4.6
pyqrandomx==0.3.2
pyqrllib==1.2.4
pyqryptonight==0.99.11
pyRFC3339==1.1
pyrsistent==0.15.5
pyserial==3.4
pytest==8.0.2
pytest-asyncio==0.24.0
python-apt==2.0.1+ubuntu0.20.4.1
python-dateutil==2.9.0.post0
python-debian==0.1.36+ubuntu1.1
python-dotenv==1.0.1
python-Levenshtein==0.25.0
pytz==2023.3
pywebio==1.8.3
PyYAML==5.3.1
qrl==4.0.4
quart==0.19.6
rapidfuzz==3.6.2
regex==2024.11.6
requests==2.31.0
requests-file==2.1.0
requests-oauthlib==1.4.0
requests-toolbelt==0.8.0
requests-unixsocket==0.2.0
retrying==1.3.4
rsa==4.9
scikit-learn==1.3.2
scipy==1.10.1
SecretStorage==2.3.1
service-identity==17.0.0
shap==0.44.1
shodan==1.31.0
simplejson==3.11.1
six==1.13.0
slicer==0.0.7
sos==4.5.6
soupsieve==1.9.5
ssh-import-id==5.10
statsmodels==0.14.1
StrEnum==0.4.15
supervisor==4.1.0
systemd-python==234
ta==0.11.0
ta-lib==0.6.3
tables==3.6.1
tailer==0.4.1
taskgroup==0.0.0a4
telegram==0.0.1
tenacity==8.4.1
tensorboard==2.13.0
tensorboard-data-server==0.7.2
tensorflow==2.13.1
tensorflow-estimator==2.13.0
tensorflow-io-gcs-filesystem==0.34.0
termcolor==2.4.0
testresources==2.0.1
textblob==0.18.0.post0
threadpoolctl==3.5.0
tldextract==5.1.2
tomli==2.0.1
tornado==6.4
tqdm==4.67.1
Twisted==20.3.0
typing-extensions==4.5.0
tzdata==2024.1
tzlocal==5.2
ua-parser==0.18.0
ubuntu-pro-client==8001
ufw==0.36
unattended-upgrades==0.1
uritemplate==3.0.1
urllib3==1.25.11
user-agents==2.2.0
wadllib==1.3.3
wcwidth==0.1.8
webencodings==0.5.1
werkzeug==3.0.3
wrapt==1.17.2
wsproto==1.2.0
xgboost==2.1.3
xlrd==1.1.0
XlsxWriter==3.2.2
xlwt==1.3.0
yarl==1.9.4
yfinance==0.2.52
zipp==3.18.1
zope.component==4.3.0
zope.event==4.4
zope.hookable==5.0.0
zope.interface==6.3
