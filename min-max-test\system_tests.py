# 🧪 اختبارات شاملة لنظام إشارات التداول

## 🎯 الهدف من الاختبارات
فحص النظام الحالي لتحديد المشاكل الفعلية وقياس الأداء الحقيقي قبل تطبيق الإصلاحات.

## 📋 خطة الاختبار

### المرحلة الأولى: اختبارات الاستيراد والتبعيات
### المرحلة الثانية: اختبارات الوظائف الأساسية
### المرحلة الثالثة: اختبارات webhook
### المرحلة الرابعة: اختبارات قاعدة البيانات
### المرحلة الخامسة: اختبارات الأداء

---

## 🔍 المرحلة الأولى: فحص المستوردات والتبعيات

### اختبار 1.1: فحص المكتبات المطلوبة
```python
import sys
import importlib

def test_imports():
    """اختبار جميع المستوردات المطلوبة"""
    required_modules = [
        'flask',
        'datetime', 
        'json',
        'gspread',
        'pandas',
        'logging',
        'socket',
        'sys',
        'traceback',
        'asyncio',
        'threading',
        'oauth2client.service_account'
    ]
    
    results = {}
    missing_modules = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            results[module] = "✅ متاح"
        except ImportError as e:
            results[module] = f"❌ مفقود: {str(e)}"
            missing_modules.append(module)
    
    return results, missing_modules

# تشغيل الاختبار
import_results, missing = test_imports()
print("=== نتائج فحص المستوردات ===")
for module, status in import_results.items():
    print(f"{module}: {status}")

if missing:
    print(f"\n⚠️ المكتبات المفقودة: {missing}")
else:
    print("\n✅ جميع المكتبات متوفرة")
```

### اختبار 1.2: فحص المكتبات الاختيارية
```python
def test_optional_imports():
    """اختبار المكتبات الاختيارية"""
    optional_modules = {
        'aiogram': 'Telegram Bot functionality',
        'stock_analyzer': 'Technical analysis',
        'technical_analysis': 'Advanced technical indicators'
    }
    
    results = {}
    
    for module, description in optional_modules.items():
        try:
            importlib.import_module(module)
            results[module] = f"✅ متاح - {description}"
        except ImportError:
            results[module] = f"⚠️ غير متاح - {description}"
    
    return results

# تشغيل الاختبار
optional_results = test_optional_imports()
print("\n=== نتائج فحص المكتبات الاختيارية ===")
for module, status in optional_results.items():
    print(f"{module}: {status}")
```

---

## 🔧 المرحلة الثانية: اختبارات الوظائف الأساسية

### اختبار 2.1: فحص تشغيل الخادم
```python
import requests
import time
import subprocess
import threading

def test_server_startup():
    """اختبار بدء تشغيل الخادم"""
    try:
        # محاولة تشغيل الخادم في thread منفصل
        def run_server():
            subprocess.run([sys.executable, "server.py"], timeout=10)
        
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        
        # انتظار قصير لبدء الخادم
        time.sleep(3)
        
        # اختبار الاتصال
        try:
            response = requests.get("http://localhost:9000", timeout=5)
            return {
                "status": "✅ نجح تشغيل الخادم",
                "response_code": response.status_code,
                "response_time": response.elapsed.total_seconds()
            }
        except requests.exceptions.ConnectionError:
            return {
                "status": "❌ فشل الاتصال بالخادم",
                "error": "Connection refused"
            }
        except requests.exceptions.Timeout:
            return {
                "status": "❌ انتهت مهلة الاتصال",
                "error": "Timeout"
            }
            
    except Exception as e:
        return {
            "status": "❌ خطأ في تشغيل الخادم",
            "error": str(e)
        }

# تشغيل الاختبار
print("\n=== اختبار تشغيل الخادم ===")
server_result = test_server_startup()
for key, value in server_result.items():
    print(f"{key}: {value}")
```

### اختبار 2.2: فحص endpoint الصحة
```python
def test_health_endpoint():
    """اختبار endpoint الصحة إذا كان موجوداً"""
    try:
        response = requests.get("http://localhost:9000/health", timeout=5)
        
        if response.status_code == 200:
            try:
                data = response.json()
                return {
                    "status": "✅ endpoint الصحة يعمل",
                    "response": data
                }
            except:
                return {
                    "status": "⚠️ endpoint الصحة يعمل لكن الاستجابة ليست JSON صحيح",
                    "response": response.text[:200]
                }
        else:
            return {
                "status": f"❌ endpoint الصحة يرجع كود خطأ: {response.status_code}",
                "response": response.text[:200]
            }
            
    except requests.exceptions.ConnectionError:
        return {
            "status": "❌ endpoint الصحة غير متاح - الخادم لا يعمل",
            "error": "Connection refused"
        }
    except Exception as e:
        return {
            "status": "❌ خطأ في فحص endpoint الصحة",
            "error": str(e)
        }

# تشغيل الاختبار
print("\n=== اختبار endpoint الصحة ===")
health_result = test_health_endpoint()
for key, value in health_result.items():
    print(f"{key}: {value}")
```

---

## 📡 المرحلة الثالثة: اختبارات webhook

### اختبار 3.1: اختبار webhook أساسي
```python
def test_webhook_basic():
    """اختبار webhook بإشارة بسيطة"""
    webhook_url = "http://localhost:9000/webhook"
    
    # بيانات اختبار أساسية
    test_data = {
        "stock_code": "TEST",
        "report": "buy",
        "buy_price": "10.00",
        "tp1": "11.00",
        "tp2": "12.00", 
        "tp3": "13.00",
        "sl": "9.00"
    }
    
    try:
        response = requests.post(
            webhook_url + "?jsonRequest=true",
            json=test_data,
            timeout=10
        )
        
        return {
            "status": f"استجابة webhook: {response.status_code}",
            "response_time": response.elapsed.total_seconds(),
            "response_body": response.text[:500] if response.text else "لا توجد استجابة"
        }
        
    except requests.exceptions.ConnectionError:
        return {
            "status": "❌ فشل الاتصال بـ webhook",
            "error": "Connection refused"
        }
    except requests.exceptions.Timeout:
        return {
            "status": "❌ انتهت مهلة webhook",
            "error": "Timeout after 10 seconds"
        }
    except Exception as e:
        return {
            "status": "❌ خطأ في webhook",
            "error": str(e)
        }

# تشغيل الاختبار
print("\n=== اختبار webhook الأساسي ===")
webhook_result = test_webhook_basic()
for key, value in webhook_result.items():
    print(f"{key}: {value}")
```

### اختبار 3.2: اختبار webhook مع بيانات خاطئة
```python
def test_webhook_invalid_data():
    """اختبار webhook مع بيانات خاطئة للتحقق من معالجة الأخطاء"""
    webhook_url = "http://localhost:9000/webhook"
    
    # اختبارات مختلفة للبيانات الخاطئة
    test_cases = [
        {
            "name": "بيانات فارغة",
            "data": {}
        },
        {
            "name": "JSON غير صحيح",
            "data": "invalid json"
        },
        {
            "name": "حقول مفقودة",
            "data": {"stock_code": "TEST"}
        },
        {
            "name": "أسعار سالبة",
            "data": {
                "stock_code": "TEST",
                "report": "buy",
                "buy_price": "-10.00",
                "tp1": "11.00",
                "sl": "9.00"
            }
        },
        {
            "name": "ترتيب أسعار خاطئ",
            "data": {
                "stock_code": "TEST", 
                "report": "buy",
                "buy_price": "10.00",
                "tp1": "9.00",  # أقل من سعر الشراء
                "sl": "11.00"   # أعلى من سعر الشراء
            }
        }
    ]
    
    results = {}
    
    for test_case in test_cases:
        try:
            if isinstance(test_case["data"], str):
                # إرسال JSON غير صحيح
                response = requests.post(
                    webhook_url + "?jsonRequest=true",
                    data=test_case["data"],
                    timeout=5
                )
            else:
                response = requests.post(
                    webhook_url + "?jsonRequest=true",
                    json=test_case["data"],
                    timeout=5
                )
            
            results[test_case["name"]] = {
                "status_code": response.status_code,
                "handled_gracefully": response.status_code in [400, 422],  # أكواد خطأ مناسبة
                "response": response.text[:200]
            }
            
        except Exception as e:
            results[test_case["name"]] = {
                "status_code": "خطأ",
                "handled_gracefully": False,
                "error": str(e)
            }
    
    return results

# تشغيل الاختبار
print("\n=== اختبار webhook مع بيانات خاطئة ===")
invalid_data_results = test_webhook_invalid_data()
for test_name, result in invalid_data_results.items():
    print(f"\n{test_name}:")
    for key, value in result.items():
        print(f"  {key}: {value}")
```

---

## 🗄️ المرحلة الرابعة: اختبارات قاعدة البيانات

### اختبار 4.1: فحص الاتصال بـ Google Sheets
```python
def test_google_sheets_connection():
    """اختبار الاتصال بـ Google Sheets"""
    try:
        import gspread
        from oauth2client.service_account import ServiceAccountCredentials
        
        # محاولة إعداد الاتصال
        scope = [
            "https://spreadsheets.google.com/feeds",
            'https://www.googleapis.com/auth/spreadsheets',
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive"
        ]
        
        try:
            with open('json_file.json') as json_file:
                import json
                json_data = json.load(json_file)
                credentials = ServiceAccountCredentials.from_json_keyfile_dict(json_data, scope)
                client = gspread.authorize(credentials)
                
            # محاولة فتح الشيت
            try:
                sheet = client.open("stock").worksheet("Sheet4")
                row_count = sheet.row_count
                
                return {
                    "status": "✅ اتصال ناجح بـ Google Sheets",
                    "sheet_rows": row_count,
                    "sheet_name": "Sheet4"
                }
                
            except gspread.SpreadsheetNotFound:
                return {
                    "status": "❌ لم يتم العثور على ملف 'stock'",
                    "error": "Spreadsheet not found"
                }
            except gspread.WorksheetNotFound:
                return {
                    "status": "❌ لم يتم العثور على ورقة 'Sheet4'",
                    "error": "Worksheet not found"
                }
                
        except FileNotFoundError:
            return {
                "status": "❌ ملف json_file.json غير موجود",
                "error": "Credentials file missing"
            }
        except json.JSONDecodeError:
            return {
                "status": "❌ ملف json_file.json تالف",
                "error": "Invalid JSON in credentials file"
            }
            
    except ImportError as e:
        return {
            "status": "❌ مكتبات Google Sheets غير متاحة",
            "error": str(e)
        }
    except Exception as e:
        return {
            "status": "❌ خطأ غير متوقع في الاتصال",
            "error": str(e)
        }

# تشغيل الاختبار
print("\n=== اختبار الاتصال بـ Google Sheets ===")
sheets_result = test_google_sheets_connection()
for key, value in sheets_result.items():
    print(f"{key}: {value}")
```

### اختبار 4.2: فحص وظائف الشيت الأساسية
```python
def test_sheet_operations():
    """اختبار العمليات الأساسية على الشيت"""
    if sheets_result["status"].startswith("❌"):
        return {"status": "❌ تخطي الاختبار - فشل الاتصال بالشيت"}
    
    try:
        import gspread
        from oauth2client.service_account import ServiceAccountCredentials
        import json
        
        # إعادة الاتصال
        scope = [
            "https://spreadsheets.google.com/feeds",
            'https://www.googleapis.com/auth/spreadsheets',
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive"
        ]
        
        with open('json_file.json') as json_file:
            json_data = json.load(json_file)
            credentials = ServiceAccountCredentials.from_json_keyfile_dict(json_data, scope)
            client = gspread.authorize(credentials)
        
        sheet = client.open("stock").worksheet("Sheet4")
        
        operations_results = {}
        
        # اختبار قراءة البيانات
        try:
            all_values = sheet.get_all_values()
            operations_results["read_data"] = f"✅ قراءة {len(all_values)} صف"
        except Exception as e:
            operations_results["read_data"] = f"❌ فشل قراءة البيانات: {str(e)}"
        
        # اختبار البحث
        try:
            # محاولة البحث عن أي قيمة في الصف الأول
            if all_values and len(all_values) > 0 and len(all_values[0]) > 0:
                search_value = all_values[0][0] if all_values[0][0] else "TEST_SEARCH"
                cell = sheet.find(search_value) if search_value != "TEST_SEARCH" else None
                operations_results["search"] = "✅ البحث يعمل" if cell else "⚠️ لم يتم العثور على قيم للبحث"
            else:
                operations_results["search"] = "⚠️ الشيت فارغ - لا يمكن اختبار البحث"
        except Exception as e:
            operations_results["search"] = f"❌ فشل البحث: {str(e)}"
        
        # اختبار التحديث (محاولة حذرة)
        try:
            # نجرب تحديث خلية فارغة أو إنشاء خلية جديدة
            test_row = len(all_values) + 1
            sheet.update_cell(test_row, 1, "TEST_UPDATE")
            
            # التحقق من التحديث
            updated_value = sheet.cell(test_row, 1).value
            if updated_value == "TEST_UPDATE":
                operations_results["update"] = "✅ التحديث يعمل"
                # حذف القيمة التجريبية
                sheet.update_cell(test_row, 1, "")
            else:
                operations_results["update"] = "❌ فشل التحديث"
                
        except Exception as e:
            operations_results["update"] = f"❌ فشل التحديث: {str(e)}"
        
        return operations_results
        
    except Exception as e:
        return {"status": f"❌ خطأ في اختبار عمليات الشيت: {str(e)}"}

# تشغيل الاختبار
print("\n=== اختبار عمليات الشيت الأساسية ===")
sheet_ops_result = test_sheet_operations()
for key, value in sheet_ops_result.items():
    print(f"{key}: {value}")
```

---

## ⚡ المرحلة الخامسة: اختبارات الأداء

### اختبار 5.1: قياس زمن الاستجابة
```python
import time
import statistics

def test_response_times():
    """قياس أزمنة الاستجابة للعمليات المختلفة"""
    
    results = {}
    
    # اختبار زمن استجابة الصفحة الرئيسية
    try:
        times = []
        for i in range(5):
            start_time = time.time()
            response = requests.get("http://localhost:9000", timeout=10)
            end_time = time.time()
            if response.status_code == 200:
                times.append(end_time - start_time)
            time.sleep(0.5)  # فترة انتظار قصيرة
        
        if times:
            results["homepage"] = {
                "average_time": round(statistics.mean(times), 3),
                "min_time": round(min(times), 3),
                "max_time": round(max(times), 3),
                "status": "✅ يعمل"
            }
        else:
            results["homepage"] = {"status": "❌ فشل جميع الطلبات"}
            
    except Exception as e:
        results["homepage"] = {"status": f"❌ خطأ: {str(e)}"}
    
    # اختبار زمن استجابة webhook
    try:
        webhook_times = []
        test_data = {
            "stock_code": "PERF_TEST",
            "report": "buy",
            "buy_price": "10.00",
            "tp1": "11.00",
            "sl": "9.00"
        }
        
        for i in range(3):  # عدد أقل للwebhook لأنه أثقل
            start_time = time.time()
            response = requests.post(
                "http://localhost:9000/webhook?jsonRequest=true",
                json=test_data,
                timeout=15
            )
            end_time = time.time()
            webhook_times.append(end_time - start_time)
            time.sleep(1)  # فترة انتظار أطول
        
        results["webhook"] = {
            "average_time": round(statistics.mean(webhook_times), 3),
            "min_time": round(min(webhook_times), 3),
            "max_time": round(max(webhook_times), 3),
            "status": "✅ يعمل"
        }
        
    except Exception as e:
        results["webhook"] = {"status": f"❌ خطأ: {str(e)}"}
    
    return results

# تشغيل الاختبار
print("\n=== اختبار أزمنة الاستجابة ===")
performance_results = test_response_times()
for endpoint, metrics in performance_results.items():
    print(f"\n{endpoint}:")
    for key, value in metrics.items():
        print(f"  {key}: {value}")
```

### اختبار 5.2: اختبار تحت الضغط
```python
import concurrent.futures

def test_load_handling():
    """اختبار تعامل النظام مع عدة طلبات متزامنة"""
    
    def make_request():
        try:
            response = requests.get("http://localhost:9000", timeout=10)
            return {
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "success": response.status_code == 200
            }
        except Exception as e:
            return {
                "status_code": "error",
                "response_time": 0,
                "success": False,
                "error": str(e)
            }
    
    print("\n=== اختبار التحميل (10 طلبات متزامنة) ===")
    
    # تشغيل 10 طلبات متزامنة
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(10)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    # تحليل النتائج
    successful_requests = sum(1 for r in results if r["success"])
    failed_requests = len(results) - successful_requests
    
    response_times = [r["response_time"] for r in results if r["success"]]
    avg_response_time = statistics.mean(response_times) if response_times else 0
    
    return {
        "total_requests": len(results),
        "successful_requests": successful_requests,
        "failed_requests": failed_requests,
        "success_rate": f"{(successful_requests/len(results)*100):.1f}%",
        "average_response_time": round(avg_response_time, 3),
        "status": "✅ مقبول" if successful_requests >= 8 else "❌ ضعيف"
    }

# تشغيل الاختبار
load_results = test_load_handling()
for key, value in load_results.items():
    print(f"{key}: {value}")
```

---

## 📊 تجميع النتائج وإنشاء التقرير

### تقرير شامل لحالة النظام
```python
def generate_system_health_report():
    """إنشاء تقرير شامل لحالة النظام"""
    
    print("\n" + "="*60)
    print("📊 تقرير شامل لحالة نظام إشارات التداول")
    print("="*60)
    
    # تجميع جميع النتائج
    all_results = {
        "المستوردات والتبعيات": {
            "المكتبات الأساسية": import_results,
            "المكتبات الاختيارية": optional_results,
            "المكتبات المفقودة": missing
        },
        "تشغيل الخادم": {
            "بدء التشغيل": server_result,
            "endpoint الصحة": health_result
        },
        "وظائف webhook": {
            "الاختبار الأساسي": webhook_result,
            "البيانات الخاطئة": invalid_data_results
        },
        "قاعدة البيانات": {
            "الاتصال": sheets_result,
            "العمليات": sheet_ops_result
        },
        "الأداء": {
            "أزمنة الاستجابة": performance_results,
            "اختبار التحميل": load_results
        }
    }
    
    # حساب النقاط الإجمالية
    total_score = 0
    max_score = 0
    
    critical_issues = []
    warnings = []
    recommendations = []
    
    # تحليل النتائج
    # المستوردات
    if missing:
        critical_issues.append(f"مكتبات مفقودة: {', '.join(missing)}")
        total_score += 0
    else:
        total_score += 20
    max_score += 20
    
    # الخادم
    if "✅" in server_result.get("status", ""):
        total_score += 20
    else:
        critical_issues.append("فشل تشغيل الخادم")
    max_score += 20
    
    # webhook
    if webhook_result.get("status", "").startswith("استجابة webhook: 200"):
        total_score += 15
    elif "200" in webhook_result.get("status", ""):
        total_score += 10
        warnings.append("webhook يعمل لكن قد توجد مشاكل")
    else:
        critical_issues.append("webhook لا يعمل بشكل صحيح")
    max_score += 15
    
    # قاعدة البيانات
    if "✅" in sheets_result.get("status", ""):
        total_score += 25
    else:
        critical_issues.append("مشكلة في الاتصال بقاعدة البيانات")
    max_score += 25
    
    # الأداء
    if load_results.get("status") == "✅ مقبول":
        total_score += 20
    else:
        warnings.append("أداء ضعيف تحت الضغط")
        total_score += 10
    max_score += 20
    
    # حساب النسبة المئوية
    health_percentage = (total_score / max_score) * 100
    
    # تحديد حالة النظام العامة
    if health_percentage >= 90:
        system_status = "🟢 ممتاز"
    elif health_percentage >= 75:
        system_status = "🟡 جيد مع تحذيرات"
    elif health_percentage >= 50:
        system_status = "🟠 يحتاج تحسين"
    else:
        system_status = "🔴 حرج - يحتاج إصلاح فوري"
    
    # طباعة التقرير
    print(f"\n🎯 حالة النظام العامة: {system_status}")
    print(f"📊 نقاط الصحة: {total_score}/{max_score} ({health_percentage:.1f}%)")
    
    if critical_issues:
        print(f"\n🔴 مشاكل حرجة ({len(critical_issues)}):")
        for issue in critical_issues:
            print(f"  • {issue}")
    
    if warnings:
        print(f"\n⚠️ تحذيرات ({len(warnings)}):")
        for warning in warnings:
            print(f"  • {warning}")
    
    # التوصيات
    if health_percentage < 50:
        recommendations.extend([
            "إيقاف النظام فوراً لتطبيق الإصلاحات",
            "إصلاح جميع المشاكل الحرجة قبل العودة للعمل",
            "تنفيذ اختبارات شاملة بعد الإصلاح"
        ])
    elif health_percentage < 75:
        recommendations.extend([
            "جدولة صيانة عاجلة خلال 48 ساعة",
            "تطبيق الإصلاحات الأساسية",
            "تحسين نظام المراقبة"
        ])
    else:
        recommendations.extend([
            "مراقبة مستمرة للأداء",
            "تطبيق التحسينات المقترحة تدريجياً",
            "إعداد نسخ احتياطية منتظمة"
        ])
    
    if recommendations:
        print(f"\n💡 التوصيات:")
        for recommendation in recommendations:
            print(f"  • {recommendation}")
    
    print("\n" + "="*60)
    print(f"📅 تاريخ الاختبار: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 أجرى الاختبار: نظام الاختبار الآلي")
    print("="*60)
    
    return {
        "health_percentage": health_percentage,
        "system_status": system_status,
        "critical_issues": critical_issues,
        "warnings": warnings,
        "recommendations": recommendations,
        "detailed_results": all_results
    }

# إنشاء التقرير النهائي
final_report = generate_system_health_report()
```

---

## 🚀 تشغيل جميع الاختبارات

لتشغيل جميع الاختبارات، احفظ هذا الكود في ملف `system_tests.py` وشغله:

```bash
python system_tests.py
```

**ملاحظات مهمة:**
1. تأكد من أن النظام يعمل قبل تشغيل الاختبارات
2. بعض الاختبارات قد تحتاج لتعديل بناءً على بيئة العمل
3. اختبارات قاعدة البيانات تحتاج ملف `json_file.json` صحيح
4. اختبارات webhook قد تؤثر على البيانات الفعلية - استخدم بحذر

## 📋 نتائج متوقعة
بناءً على المراجعة السابقة، نتوقع:
- ❌ مشاكل في المستوردات
- ❌ أو ⚠️ مشاكل في webhook
- ❌ أو ⚠️ مشاكل في معالجة الأخطاء  
- ✅ أو ⚠️ اتصال قاعدة البيانات (حسب الإعدادات)
- ⚠️ أداء متوسط أو ضعيف

هذه الاختبارات ستعطينا صورة دقيقة عن الحالة الفعلية للنظام.
