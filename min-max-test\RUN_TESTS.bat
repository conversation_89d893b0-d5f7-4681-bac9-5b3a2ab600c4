@echo off
chcp 65001 > nul
echo ====================================
echo 🚀 أداة اختبار نظام إشارات التداول
echo ====================================
echo.

:: التحقق من وجود Python
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير متاح في PATH
    echo 💡 تأكد من تثبيت Python وإضافته لـ PATH
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

:: التحقق من وجود ملفات الاختبار
if not exist "SYSTEM_TESTING_SUITE.py" (
    echo ❌ ملف SYSTEM_TESTING_SUITE.py غير موجود
    pause
    exit /b 1
)

if not exist "QUICK_TEST_RUNNER.py" (
    echo ❌ ملف QUICK_TEST_RUNNER.py غير موجود
    pause
    exit /b 1
)

echo ✅ ملفات الاختبار موجودة
echo.

:: التحقق من حالة السيرفر
echo 🔍 فحص حالة السيرفر...
python -c "import requests; print('✅ السيرفر يعمل') if requests.get('http://localhost:9000/health', timeout=3).status_code == 200 else print('❌ السيرفر لا يستجيب')" 2>nul
if errorlevel 1 (
    echo ⚠️ السيرفر غير متاح
    echo.
    echo 🎯 اختر أحد الخيارات:
    echo 1. تشغيل السيرفر تلقائياً وبدء الاختبارات
    echo 2. بدء الاختبارات فقط (تأكد من تشغيل السيرفر يدوياً)
    echo 3. خروج
    echo.
    set /p choice="اختر رقم (1-3): "
    
    if "!choice!"=="1" (
        echo 🚀 تشغيل السيرفر...
        start /b python server.py
        timeout /t 5 /nobreak > nul
        echo ✅ تم تشغيل السيرفر
    ) else if "!choice!"=="2" (
        echo ⚠️ تأكد من تشغيل السيرفر قبل المتابعة
    ) else (
        echo 👋 إلى اللقاء!
        pause
        exit /b 0
    )
) else (
    echo ✅ السيرفر يعمل بشكل طبيعي
)

echo.
echo 🧪 بدء أداة الاختبارات التفاعلية...
echo.

:: تشغيل أداة الاختبارات
python QUICK_TEST_RUNNER.py

echo.
echo ====================================
echo ✅ انتهت جلسة الاختبار
echo ====================================
echo.
echo 📁 ملفات النتائج:
if exist "testing_results.json" echo   - testing_results.json ✅
if exist "advanced_test_results.json" echo   - advanced_test_results.json ✅
if exist "quick_test_report.json" echo   - quick_test_report.json ✅
if exist "monitoring_report.json" echo   - monitoring_report.json ✅
echo.
echo 📋 ملفات السجلات:
if exist "testing_results.log" echo   - testing_results.log ✅
if exist "system_monitor.log" echo   - system_monitor.log ✅
echo.

pause
