#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إرسال أكواد البرومو
Test promo code sending
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock

async def test_promo_sending():
    """اختبار إرسال أكواد البرومو"""
    try:
        from free_users_manager import FreeUsersManager
        
        print("🧪 اختبار إرسال أكواد البرومو...")
        
        # 1. اختبار جلب المستخدمين
        print("\n📊 اختبار جلب المستخدمين:")
        
        all_free = FreeUsersManager.get_all_free_users()
        active_free = FreeUsersManager.get_active_free_users()
        
        print(f"✅ إجمالي المستخدمين المجانيين: {len(all_free)}")
        print(f"✅ المستخدمين النشطين: {len(active_free)}")
        
        if len(all_free) == 0:
            print("❌ لا توجد مستخدمين مجانيين")
            return False
        
        # 2. اختبار إنشاء أكواد مجمعة
        print("\n🎫 اختبار إنشاء أكواد مجمعة:")
        
        codes = FreeUsersManager.create_bulk_trial_codes(
            count=3,
            days=7,
            note="اختبار النظام"
        )
        
        if codes and len(codes) == 3:
            print(f"✅ تم إنشاء {len(codes)} كود بنجاح:")
            for i, code in enumerate(codes, 1):
                print(f"   {i}. {code}")
        else:
            print("❌ فشل في إنشاء الأكواد")
            return False
        
        # 3. محاكاة إرسال الأكواد (بدون إرسال فعلي)
        print("\n📱 محاكاة إرسال الأكواد:")
        
        # محاكاة دالة الإرسال
        original_send = FreeUsersManager.send_promo_code_to_user
        
        async def mock_send(user_id, promo_code, user_name=""):
            print(f"   📤 محاكاة إرسال {promo_code} للمستخدم {user_id} ({user_name})")
            return True  # محاكاة نجاح الإرسال
        
        # استبدال دالة الإرسال بالمحاكاة
        FreeUsersManager.send_promo_code_to_user = mock_send
        
        try:
            # اختبار الإرسال للمستخدمين النشطين
            result = await FreeUsersManager.send_bulk_promo_codes("active")
            
            if result["success"]:
                print(f"✅ محاكاة الإرسال نجحت:")
                print(f"   📊 المستهدفين: {result['target_type']}")
                print(f"   👥 إجمالي المستخدمين: {result['total_users']}")
                print(f"   ✅ تم الإرسال بنجاح: {result['sent_count']}")
                print(f"   ❌ فشل الإرسال: {result['failed_count']}")
                print(f"   📈 معدل النجاح: {round(result['sent_count']/result['total_users']*100, 1)}%")
                
                return True
            else:
                print(f"❌ فشل في محاكاة الإرسال: {result['error']}")
                return False
                
        finally:
            # إعادة الدالة الأصلية
            FreeUsersManager.send_promo_code_to_user = original_send
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإرسال: {e}")
        return False

async def test_command_functions():
    """اختبار دوال الأوامر"""
    try:
        print("\n🤖 اختبار دوال الأوامر:")
        
        from free_users_manager import (
            list_free_users_command,
            send_promo_to_active_command,
            send_promo_to_all_command
        )
        
        # محاكاة رسالة من المدير
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        mock_message.from_user.id = 868182073  # معرف المدير من config.py
        
        # 1. اختبار أمر عرض الإحصائيات
        print("\n📊 اختبار أمر /list_free_users:")
        
        await list_free_users_command(mock_message)
        
        # التحقق من استدعاء reply
        if mock_message.reply.called:
            call_args = mock_message.reply.call_args
            sent_message = call_args[0][0]
            print("✅ تم إرسال رسالة الإحصائيات")
            
            # التحقق من وجود المحتوى المطلوب
            required_content = [
                "إحصائيات المشتركين المجانيين",
                "إجمالي المستخدمين المجانيين",
                "المستخدمين النشطين",
                "معدل النشاط"
            ]
            
            missing_content = []
            for content in required_content:
                if content not in sent_message:
                    missing_content.append(content)
            
            if missing_content:
                print(f"⚠️ محتوى مفقود: {missing_content}")
            else:
                print("✅ جميع المحتوى المطلوب موجود")
        else:
            print("❌ لم يتم استدعاء reply")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دوال الأوامر: {e}")
        return False

async def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لإرسال أكواد البرومو")
    print("=" * 60)
    
    tests = [
        ("اختبار إرسال أكواد البرومو", test_promo_sending),
        ("اختبار دوال الأوامر", test_command_functions),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            results.append((test_name, False))
    
    # عرض النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة: {passed}/{len(results)} اختبار نجح")
    
    if passed == len(results):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ نظام إرسال أكواد البرومو يعمل بشكل صحيح")
        print("✅ الأوامر جاهزة للاستخدام:")
        print("   - /list_free_users")
        print("   - /send_promo_active")
        print("   - /send_promo_all")
        
        print("\n🚀 للاستخدام الفعلي:")
        print("   1. شغل البوت: python run.py")
        print("   2. استخدم الأوامر في التليجرام")
        print("   3. راقب النتائج")
    else:
        print(f"\n⚠️ {len(results) - passed} اختبار فشل")
        print("❌ قد تحتاج إلى مراجعة النظام")

if __name__ == "__main__":
    asyncio.run(main())
